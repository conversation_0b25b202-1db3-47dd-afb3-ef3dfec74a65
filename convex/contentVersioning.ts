import { v } from "convex/values";
import { mutation, query, internalMutation } from "./_generated/server";

// Create a new draft
export const createDraft = mutation({
  args: {
    identifier: v.string(),
    language: v.string(),
    data: v.any(),
    contentTypeId: v.id("contentTypes"),
    changeNote: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check
    const userId = "system"; // TODO: Get from auth
    
    // Get the current published content if it exists
    const publishedContent = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) => 
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .filter((q) => q.eq(q.field("status"), "published"))
      .first();

    // Check if there's already a draft
    const existingDraft = await ctx.db
      .query("contentDrafts")
      .withIndex("by_identifier_language", (q) => 
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    const now = Date.now();
    const version = publishedContent ? publishedContent.version + 1 : 1;

    if (existingDraft) {
      // Update existing draft
      await ctx.db.patch(existingDraft._id, {
        data: args.data,
        version,
        lastModifiedBy: userId,
        changeNote: args.changeNote,
        updatedAt: now,
      });
      return existingDraft._id;
    } else {
      // Create new draft
      return await ctx.db.insert("contentDrafts", {
        contentId: publishedContent?._id || ("" as any), // Will be set when published
        identifier: args.identifier,
        language: args.language,
        data: args.data,
        contentTypeId: args.contentTypeId,
        version,
        createdBy: userId,
        lastModifiedBy: userId,
        changeNote: args.changeNote,
        createdAt: now,
        updatedAt: now,
      });
    }
  },
});

// Get draft content
export const getDraft = query({
  args: {
    identifier: v.string(),
    language: v.string(),
  },
  handler: async (ctx, args) => {
    const draft = await ctx.db
      .query("contentDrafts")
      .withIndex("by_identifier_language", (q) => 
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    if (draft) {
      const contentType = await ctx.db.get(draft.contentTypeId);
      return {
        ...draft,
        contentType,
        isDraft: true,
      };
    }

    return null;
  },
});

// Publish draft
export const publishDraft = mutation({
  args: {
    identifier: v.string(),
    language: v.string(),
    publishNote: v.optional(v.string()),
    scheduledFor: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check
    const userId = "system"; // TODO: Get from auth
    
    const draft = await ctx.db
      .query("contentDrafts")
      .withIndex("by_identifier_language", (q) => 
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    if (!draft) {
      throw new Error("No draft found to publish");
    }

    const now = Date.now();
    const publishTime = args.scheduledFor || now;

    // Get existing published content
    const existingContent = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) => 
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .filter((q) => q.eq(q.field("status"), "published"))
      .first();

    let contentId;

    if (existingContent) {
      // Create history entry for current published version
      await ctx.db.insert("contentHistory", {
        contentId: existingContent._id,
        data: existingContent.data,
        version: existingContent.version,
        changedBy: userId,
        changeNote: args.publishNote || "Published new version",
        changeType: "published",
        previousVersion: existingContent.version - 1,
        metadata: {
          publishedAt: publishTime,
        },
        createdAt: now,
      });

      // Update existing content
      await ctx.db.patch(existingContent._id, {
        data: draft.data,
        version: draft.version,
        updatedAt: now,
      });

      contentId = existingContent._id;
    } else {
      // Create new published content
      contentId = await ctx.db.insert("content", {
        identifier: args.identifier,
        language: args.language,
        data: draft.data,
        contentTypeId: draft.contentTypeId,
        status: "published",
        version: draft.version,
        createdBy: draft.createdBy,
        createdAt: draft.createdAt,
        updatedAt: now,
      });

      // Create history entry for creation
      await ctx.db.insert("contentHistory", {
        contentId,
        data: draft.data,
        version: draft.version,
        changedBy: userId,
        changeNote: args.publishNote || "Initial publication",
        changeType: "created",
        metadata: {
          publishedAt: publishTime,
        },
        createdAt: now,
      });
    }

    // Update draft with content ID and remove it
    await ctx.db.patch(draft._id, { contentId });
    await ctx.db.delete(draft._id);

    return contentId;
  },
});

// Schedule content for future publication
export const schedulePublication = mutation({
  args: {
    identifier: v.string(),
    language: v.string(),
    scheduledFor: v.number(),
    publishNote: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check
    
    const draft = await ctx.db
      .query("contentDrafts")
      .withIndex("by_identifier_language", (q) => 
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    if (!draft) {
      throw new Error("No draft found to schedule");
    }

    await ctx.db.patch(draft._id, {
      scheduledPublishAt: args.scheduledFor,
      changeNote: args.publishNote,
      updatedAt: Date.now(),
    });

    return draft._id;
  },
});

// Get scheduled content
export const getScheduledContent = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    
    return await ctx.db
      .query("contentDrafts")
      .withIndex("by_scheduled", (q) => q.gt("scheduledPublishAt", 0))
      .order("asc")
      .take(limit);
  },
});

// Process scheduled publications (internal function)
export const processScheduledPublications = internalMutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    const scheduledDrafts = await ctx.db
      .query("contentDrafts")
      .withIndex("by_scheduled", (q) => q.lte("scheduledPublishAt", now))
      .collect();

    const results = [];

    for (const draft of scheduledDrafts) {
      try {
        // Publish the draft
        await publishDraft(ctx, {
          identifier: draft.identifier,
          language: draft.language,
          publishNote: `Scheduled publication: ${draft.changeNote || ""}`,
        });

        results.push({
          identifier: draft.identifier,
          language: draft.language,
          status: "published",
        });
      } catch (error) {
        results.push({
          identifier: draft.identifier,
          language: draft.language,
          status: "error",
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return results;
  },
});

// Compare versions
export const compareVersions = query({
  args: {
    identifier: v.string(),
    language: v.string(),
    version1: v.number(),
    version2: v.number(),
  },
  handler: async (ctx, args) => {
    const content = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) => 
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    if (!content) {
      throw new Error("Content not found");
    }

    // Get both versions from history
    const [version1, version2] = await Promise.all([
      ctx.db
        .query("contentHistory")
        .withIndex("by_version", (q) => 
          q.eq("contentId", content._id).eq("version", args.version1)
        )
        .first(),
      ctx.db
        .query("contentHistory")
        .withIndex("by_version", (q) => 
          q.eq("contentId", content._id).eq("version", args.version2)
        )
        .first(),
    ]);

    if (!version1 || !version2) {
      throw new Error("One or both versions not found");
    }

    // Simple diff - in a real app you'd use a proper diff library
    const changes = [];
    const data1 = version1.data;
    const data2 = version2.data;

    for (const key in data1) {
      if (JSON.stringify(data1[key]) !== JSON.stringify(data2[key])) {
        changes.push({
          field: key,
          oldValue: data1[key],
          newValue: data2[key],
        });
      }
    }

    for (const key in data2) {
      if (!(key in data1)) {
        changes.push({
          field: key,
          oldValue: null,
          newValue: data2[key],
        });
      }
    }

    return {
      version1: {
        ...version1,
        versionNumber: args.version1,
      },
      version2: {
        ...version2,
        versionNumber: args.version2,
      },
      changes,
    };
  },
});

// Get content timeline
export const getContentTimeline = query({
  args: {
    identifier: v.string(),
    language: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    
    const content = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) => 
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    if (!content) {
      return [];
    }

    const history = await ctx.db
      .query("contentHistory")
      .withIndex("by_content", (q) => q.eq("contentId", content._id))
      .order("desc")
      .take(limit);

    // Enrich with user information
    const enrichedHistory = await Promise.all(
      history.map(async (entry) => {
        const user = await ctx.db
          .query("users")
          .filter((q) => q.eq(q.field("clerkId"), entry.changedBy))
          .first();

        return {
          ...entry,
          user: user ? {
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
          } : null,
        };
      })
    );

    return enrichedHistory;
  },
});

// Discard draft
export const discardDraft = mutation({
  args: {
    identifier: v.string(),
    language: v.string(),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check
    
    const draft = await ctx.db
      .query("contentDrafts")
      .withIndex("by_identifier_language", (q) => 
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    if (!draft) {
      throw new Error("No draft found to discard");
    }

    await ctx.db.delete(draft._id);
    return { success: true };
  },
});
