import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get or create user from Clerk with invitation handling
export const getOrCreateUser = mutation({
  args: {
    clerkId: v.string(),
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
      .first();

    const now = Date.now();

    if (existing) {
      // Update last login for existing user
      await ctx.db.patch(existing._id, {
        lastLogin: now,
        updatedAt: now,
      });
      return existing;
    } else {
      // Check for pending invitation for this email
      const pendingInvitation = await ctx.db
        .query("adminInvitations")
        .withIndex("by_email", (q) => q.eq("email", args.email))
        .filter((q) => q.eq(q.field("status"), "pending"))
        .filter((q) => q.gt(q.field("expiresAt"), now))
        .first();

      let userRole: "super_admin" | "admin" | "content_editor" | "viewer" = "viewer"; // Default role

      if (pendingInvitation) {
        // User has a valid invitation - assign the invited role
        userRole = pendingInvitation.role;

        // Mark invitation as accepted
        await ctx.db.patch(pendingInvitation._id, {
          status: "accepted",
          acceptedAt: now,
        });

        console.log(`✅ Invitation accepted: ${args.email} joined as ${pendingInvitation.role}`);
      }

      // Create new user with appropriate role
      const newUser = await ctx.db.insert("users", {
        clerkId: args.clerkId,
        email: args.email,
        firstName: args.firstName,
        lastName: args.lastName,
        role: userRole,
        isActive: true,
        lastLogin: now,
        createdAt: now,
        updatedAt: now,
      });

      return newUser;
    }
  },
});

// Get user by Clerk ID
export const getUserByClerkId = query({
  args: { clerkId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
      .first();
  },
});

// Get all users (admin only)
export const getAllUsers = query({
  args: {},
  handler: async (ctx) => {
    // TODO: Add admin authentication check
    return await ctx.db.query("users").collect();
  },
});



// Update user role (super admin only)
export const updateUserRole = mutation({
  args: {
    userId: v.id("users"),
    role: v.union(
      v.literal("super_admin"),
      v.literal("admin"),
      v.literal("content_editor"),
      v.literal("viewer")
    ),
  },
  handler: async (ctx, args) => {
    // TODO: Add super admin authentication check
    return await ctx.db.patch(args.userId, {
      role: args.role,
      updatedAt: Date.now(),
    });
  },
});

// Check for pending invitation by email
export const checkPendingInvitation = query({
  args: {
    email: v.string(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const pendingInvitation = await ctx.db
      .query("adminInvitations")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .filter((q) => q.gt(q.field("expiresAt"), now))
      .first();

    if (pendingInvitation) {
      return {
        hasInvitation: true,
        role: pendingInvitation.role,
        message: pendingInvitation.message,
        invitedBy: pendingInvitation.invitedBy,
        expiresAt: pendingInvitation.expiresAt,
      };
    }

    return {
      hasInvitation: false,
    };
  },
});

// Update user role by Clerk ID (for development/admin use)
export const updateUserRoleByClerkId = mutation({
  args: {
    clerkId: v.string(),
    role: v.union(
      v.literal("super_admin"),
      v.literal("admin"),
      v.literal("content_editor"),
      v.literal("viewer")
    ),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", args.clerkId))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    return await ctx.db.patch(user._id, {
      role: args.role,
      updatedAt: Date.now(),
    });
  },
});

// Deactivate user
export const deactivateUser = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    // TODO: Add admin authentication check
    return await ctx.db.patch(args.userId, {
      isActive: false,
      updatedAt: Date.now(),
    });
  },
});
