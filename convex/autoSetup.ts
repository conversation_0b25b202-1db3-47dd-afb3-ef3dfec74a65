import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Auto-initialization function that runs when needed
export const autoInitializeCMS = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if already initialized
    const existingContentTypes = await ctx.db.query("contentTypes").collect();
    const existingContent = await ctx.db.query("content").collect();
    
    if (existingContentTypes.length > 0 && existingContent.length > 0) {
      return {
        status: "already_initialized",
        message: "CMS is already initialized",
        contentTypesCount: existingContentTypes.length,
        contentItemsCount: existingContent.length,
      };
    }

    const results = {
      contentTypes: [] as any[],
      content: [] as any[],
      errors: [] as string[],
    };

    try {
      const now = Date.now();

      // Create essential content types first
      const contentTypes = [
        {
          name: "hero_section",
          label: "Hero Section",
          description: "Main hero section with title, subtitle, and call-to-action",
          icon: "Layout",
          category: "sections",
          fields: [
            { name: "title", label: "Title", type: "text", required: true },
            { name: "subtitle", label: "Subtitle", type: "richText", required: false },
            { name: "backgroundImage", label: "Background Image", type: "image", required: false },
            { name: "ctaText", label: "CTA Button Text", type: "text", required: false },
            { name: "ctaUrl", label: "CTA Button URL", type: "url", required: false },
          ],
          settings: { allowMultiple: false, isSystem: true, sortable: false },
        },
        {
          name: "content_section",
          label: "Content Section",
          description: "General content section with title, content, and optional image",
          icon: "FileText",
          category: "sections",
          fields: [
            { name: "title", label: "Section Title", type: "text", required: true },
            { name: "subtitle", label: "Subtitle", type: "text", required: false },
            { name: "content", label: "Content", type: "richText", required: true },
            { name: "image", label: "Section Image", type: "image", required: false },
          ],
          settings: { allowMultiple: true, isSystem: false, sortable: true },
        },
        {
          name: "service_card",
          label: "Service Card",
          description: "Service or feature card with icon and description",
          icon: "Briefcase",
          category: "services",
          fields: [
            { name: "title", label: "Service Title", type: "text", required: true },
            { name: "description", label: "Description", type: "richText", required: true },
            { name: "icon", label: "Icon", type: "text", required: false },
            { name: "image", label: "Service Image", type: "image", required: false },
          ],
          settings: { allowMultiple: true, isSystem: false, sortable: true },
        },
        {
          name: "testimonial",
          label: "Testimonial",
          description: "Customer testimonial with quote, author, and company",
          icon: "Quote",
          category: "testimonials",
          fields: [
            { name: "quote", label: "Quote", type: "richText", required: true },
            { name: "author", label: "Author Name", type: "text", required: true },
            { name: "position", label: "Position/Title", type: "text", required: false },
            { name: "company", label: "Company", type: "text", required: false },
            { name: "rating", label: "Rating (1-5)", type: "number", required: false, defaultValue: 5 },
          ],
          settings: { allowMultiple: true, isSystem: false, sortable: true },
        },
        {
          name: "contact_info",
          label: "Contact Information",
          description: "Contact details including address, phone, email, and hours",
          icon: "Phone",
          category: "contact",
          fields: [
            { name: "title", label: "Section Title", type: "text", required: false },
            { name: "address", label: "Address", type: "richText", required: false },
            { name: "phone", label: "Phone Number", type: "text", required: false },
            { name: "email", label: "Email Address", type: "email", required: false },
            { name: "hours", label: "Business Hours", type: "richText", required: false },
            { name: "socialLinks", label: "Social Media Links", type: "array", required: false },
          ],
          settings: { allowMultiple: false, isSystem: false, sortable: false },
        },
        {
          name: "cta_section",
          label: "Call to Action Section",
          description: "Call to action section with title, description, and buttons",
          icon: "ArrowRight",
          category: "sections",
          fields: [
            { name: "title", label: "CTA Title", type: "text", required: true },
            { name: "description", label: "Description", type: "richText", required: false },
            { name: "primaryButtonText", label: "Primary Button Text", type: "text", required: false },
            { name: "primaryButtonUrl", label: "Primary Button URL", type: "url", required: false },
            { name: "secondaryButtonText", label: "Secondary Button Text", type: "text", required: false },
            { name: "secondaryButtonUrl", label: "Secondary Button URL", type: "url", required: false },
          ],
          settings: { allowMultiple: true, isSystem: false, sortable: true },
        },
      ];

      // Create content types
      const contentTypeIds: Record<string, any> = {};
      for (const contentType of contentTypes) {
        try {
          const id = await ctx.db.insert("contentTypes", {
            ...contentType,
            createdAt: now,
            updatedAt: now,
          });
          contentTypeIds[contentType.name] = id;
          results.contentTypes.push({ name: contentType.name, id, status: "created" });
        } catch (error) {
          results.errors.push(`Failed to create content type ${contentType.name}: ${error}`);
        }
      }

      // Create essential content items
      const contentItems = [
        {
          identifier: "home-hero",
          language: "en",
          contentTypeId: contentTypeIds.hero_section,
          status: "published" as const,
          data: {
            title: "Leading Technology Solutions in Equatorial Guinea",
            subtitle: "OfficeTech delivers cutting-edge cybersecurity, network infrastructure, and IT training solutions to protect and empower your business in the digital age.",
            ctaText: "Get Started Today",
            ctaUrl: "/contact",
          },
        },
        {
          identifier: "services-banner",
          language: "en",
          contentTypeId: contentTypeIds.content_section,
          status: "published" as const,
          data: {
            title: "Our Services",
            subtitle: "Comprehensive Technology Solutions",
            content: "From cybersecurity to network infrastructure, we provide the complete technology stack your business needs to thrive in today's digital landscape.",
          },
        },
        {
          identifier: "contact-info",
          language: "en",
          contentTypeId: contentTypeIds.contact_info,
          status: "published" as const,
          data: {
            title: "Get in Touch",
            address: "Malabo, Equatorial Guinea<br>Central Business District",
            phone: "+*********** 789",
            email: "<EMAIL>",
            hours: "Monday - Friday: 8:00 AM - 6:00 PM<br>Saturday: 9:00 AM - 2:00 PM<br>24/7 Emergency Support Available",
            socialLinks: [
              { name: "LinkedIn", url: "https://linkedin.com/company/officetech-eg" },
              { name: "Facebook", url: "https://facebook.com/officetecheg" },
            ],
          },
        },
        {
          identifier: "service-cybersecurity",
          language: "en",
          contentTypeId: contentTypeIds.service_card,
          status: "published" as const,
          data: {
            title: "Cybersecurity",
            description: "Protecting Your Digital World at Every Stage. Comprehensive cybersecurity solutions including prevention, detection, response, recovery, and continuous improvement.",
            icon: "Shield",
          },
        },
        {
          identifier: "service-network",
          language: "en",
          contentTypeId: contentTypeIds.service_card,
          status: "published" as const,
          data: {
            title: "Network Solutions",
            description: "International and national connectivity solutions including MPLS, SD-WAN, and VPL. High-speed internet and private networks with 24/7 support.",
            icon: "Wifi",
          },
        },
        {
          identifier: "service-surveillance",
          language: "en",
          contentTypeId: contentTypeIds.service_card,
          status: "published" as const,
          data: {
            title: "Electronic Security",
            description: "Comprehensive electronic security solutions including IP cameras, alarm systems, access control, and professional installation with 24/7 monitoring.",
            icon: "Eye",
          },
        },
        {
          identifier: "service-training",
          language: "en",
          contentTypeId: contentTypeIds.service_card,
          status: "published" as const,
          data: {
            title: "IT Training & Support",
            description: "Professional IT training programs and technical support to empower your team with the latest technology skills.",
            icon: "GraduationCap",
          },
        },
        {
          identifier: "cta-section",
          language: "en",
          contentTypeId: contentTypeIds.cta_section,
          status: "published" as const,
          data: {
            title: "Ready to Secure Your Business?",
            description: "Get a free consultation and discover how our technology solutions can protect and grow your business.",
            primaryButtonText: "Get Free Consultation",
            primaryButtonUrl: "/contact",
            secondaryButtonText: "View Our Services",
            secondaryButtonUrl: "/services",
          },
        },
      ];

      // Create content items
      for (const contentItem of contentItems) {
        if (contentItem.contentTypeId) {
          try {
            const contentId = await ctx.db.insert("content", {
              identifier: contentItem.identifier,
              language: contentItem.language,
              data: contentItem.data,
              contentTypeId: contentItem.contentTypeId,
              status: contentItem.status,
              version: 1,
              createdBy: "system",
              createdAt: now,
              updatedAt: now,
            });
            results.content.push({ identifier: contentItem.identifier, id: contentId, status: "created" });
          } catch (error) {
            results.errors.push(`Failed to create content ${contentItem.identifier}: ${error}`);
          }
        }
      }

      return {
        status: "initialized",
        message: "CMS initialized successfully",
        ...results,
      };
    } catch (error) {
      results.errors.push(`Setup failed: ${error}`);
      return {
        status: "error",
        message: "CMS initialization failed",
        ...results,
      };
    }
  },
});

// Check if CMS needs initialization
export const checkInitializationStatus = query({
  args: {},
  handler: async (ctx) => {
    const contentTypes = await ctx.db.query("contentTypes").collect();
    const content = await ctx.db.query("content").collect();
    
    const isInitialized = contentTypes.length > 0 && content.length > 0;
    
    return {
      isInitialized,
      contentTypesCount: contentTypes.length,
      contentItemsCount: content.length,
      needsInitialization: !isInitialized,
    };
  },
});
