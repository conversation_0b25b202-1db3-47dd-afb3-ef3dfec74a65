import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Submit contact form
export const submitContact = mutation({
  args: {
    name: v.string(),
    email: v.string(),
    phone: v.optional(v.string()),
    company: v.optional(v.string()),
    service: v.optional(v.string()),
    message: v.string(),
    source: v.string(),
    language: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const language = args.language || "en";
    
    return await ctx.db.insert("contactSubmissions", {
      name: args.name,
      email: args.email,
      phone: args.phone,
      company: args.company,
      service: args.service,
      message: args.message,
      status: "new",
      source: args.source,
      language,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  },
});

// Get all contact submissions (admin only)
export const getContactSubmissions = query({
  args: {
    status: v.optional(v.union(
      v.literal("new"),
      v.literal("contacted"),
      v.literal("qualified"),
      v.literal("closed")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // TODO: Add admin authentication check
    let query = ctx.db.query("contactSubmissions");
    
    if (args.status) {
      query = query.withIndex("by_status", (q) => q.eq("status", args.status));
    }
    
    query = query.order("desc");
    
    if (args.limit) {
      query = query.take(args.limit);
    }
    
    return await query.collect();
  },
});

// Update contact submission status
export const updateContactStatus = mutation({
  args: {
    submissionId: v.id("contactSubmissions"),
    status: v.union(
      v.literal("new"),
      v.literal("contacted"),
      v.literal("qualified"),
      v.literal("closed")
    ),
  },
  handler: async (ctx, args) => {
    // TODO: Add admin authentication check
    return await ctx.db.patch(args.submissionId, {
      status: args.status,
      updatedAt: Date.now(),
    });
  },
});

// Get contact submission by ID
export const getContactSubmission = query({
  args: { submissionId: v.id("contactSubmissions") },
  handler: async (ctx, args) => {
    // TODO: Add admin authentication check
    return await ctx.db.get(args.submissionId);
  },
});
