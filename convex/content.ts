import { v } from "convex/values";
import { mutation, query, internalMutation } from "./_generated/server";
import { api } from "./_generated/api";

// Helper function to get current user and check permissions
async function getCurrentUser(ctx: any) {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Not authenticated");
  }

  const user = await ctx.db
    .query("users")
    .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
    .first();

  if (!user) {
    throw new Error("User not found");
  }

  if (!user.isActive) {
    throw new Error("User account is deactivated");
  }

  return user;
}

function hasPermission(user: any, permission: string): boolean {
  const permissions: Record<string, string[]> = {
    viewer: ["content:read", "media:read"],
    content_editor: [
      "content:read", "content:write", "content:publish",
      "media:read", "media:write", "media:upload"
    ],
    admin: [
      "content:read", "content:write", "content:publish", "content:delete",
      "media:read", "media:write", "media:upload", "media:delete",
      "users:read", "users:write", "analytics:read",
      "settings:read", "settings:write"
    ],
    super_admin: ["*"],
  };

  const userPermissions = permissions[user.role] || [];
  return userPermissions.includes("*") || userPermissions.includes(permission);
}

// Get content by identifier and language with caching
export const getContent = query({
  args: {
    identifier: v.string(),
    language: v.optional(v.string()),
    includeDraft: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    const language = args.language || "en";
    const includeDraft = args.includeDraft || false;

    // Build status filter
    const statusFilter = includeDraft ?
      (q: any) => q.or(q.eq(q.field("status"), "published"), q.eq(q.field("status"), "draft")) :
      (q: any) => q.eq(q.field("status"), "published");

    const content = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", language)
      )
      .filter(statusFilter)
      .order("desc") // Get latest version first
      .first();

    if (!content) {
      // Fallback to English if content not found in requested language
      if (language !== "en") {
        return await ctx.db
          .query("content")
          .withIndex("by_identifier_language", (q) =>
            q.eq("identifier", args.identifier).eq("language", "en")
          )
          .filter(statusFilter)
          .order("desc")
          .first();
      }
      return null;
    }

    // Get content type information
    const contentType = await ctx.db.get(content.contentTypeId);

    return {
      ...content,
      contentType,
    };
  },
});

// Get all content for a specific language with pagination
export const getAllContent = query({
  args: {
    language: v.optional(v.string()),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const status = args.status || "published";
    const limit = args.limit || 50;

    let query = ctx.db.query("content");

    // If language is specified, filter by language, otherwise get all languages
    if (args.language) {
      query = query.withIndex("by_language", (q) => q.eq("language", args.language));
    }

    query = query
      .filter((q) => q.eq(q.field("status"), status))
      .order("desc");

    if (args.cursor) {
      // Implement cursor-based pagination if needed
    }

    const results = await query.take(limit);

    // Enrich with content type information
    const enrichedResults = await Promise.all(
      results.map(async (content) => {
        const contentType = await ctx.db.get(content.contentTypeId);
        return {
          ...content,
          contentType,
        };
      })
    );

    return enrichedResults;
  },
});

// Create or update content (admin only)
export const upsertContent = mutation({
  args: {
    identifier: v.string(),
    language: v.string(),
    data: v.any(),
    contentTypeId: v.id("contentTypes"),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    // Check permissions based on status
    const status = args.status || "draft";
    const requiredPermission = status === "published" ? "content:publish" : "content:write";
    if (!hasPermission(user, requiredPermission)) {
      throw new Error(`Insufficient permissions: ${requiredPermission} required`);
    }
    
    const existing = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) => 
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    const now = Date.now();
    
    if (existing) {
      // Create history entry
      await ctx.db.insert("contentHistory", {
        contentId: existing._id,
        data: existing.data,
        version: existing.version,
        changeType: "updated",
        changedBy: "system", // TODO: Get from auth
        createdAt: now,
      });

      // Update existing content
      return await ctx.db.patch(existing._id, {
        data: args.data,
        status,
        version: existing.version + 1,
        updatedAt: now,
      });
    } else {
      // Create new content
      return await ctx.db.insert("content", {
        identifier: args.identifier,
        language: args.language,
        data: args.data,
        contentTypeId: args.contentTypeId,
        status,
        version: 1,
        createdBy: "system", // TODO: Get from auth
        createdAt: now,
        updatedAt: now,
      });
    }
  },
});

// Get content by multiple identifiers (batch fetch)
export const getContentBatch = query({
  args: {
    identifiers: v.array(v.string()),
    language: v.optional(v.string()),
    includeDraft: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    const language = args.language || "en";
    const includeDraft = args.includeDraft || false;

    const results: Record<string, any> = {};

    for (const identifier of args.identifiers) {
      const statusFilter = includeDraft ?
        (q: any) => q.or(q.eq(q.field("status"), "published"), q.eq(q.field("status"), "draft")) :
        (q: any) => q.eq(q.field("status"), "published");

      const content = await ctx.db
        .query("content")
        .withIndex("by_identifier_language", (q) =>
          q.eq("identifier", identifier).eq("language", language)
        )
        .filter(statusFilter)
        .order("desc")
        .first();

      if (content) {
        const contentType = await ctx.db.get(content.contentTypeId);
        results[identifier] = {
          ...content,
          contentType,
        };
      } else if (language !== "en") {
        // Fallback to English
        const fallbackContent = await ctx.db
          .query("content")
          .withIndex("by_identifier_language", (q) =>
            q.eq("identifier", identifier).eq("language", "en")
          )
          .filter(statusFilter)
          .order("desc")
          .first();

        if (fallbackContent) {
          const contentType = await ctx.db.get(fallbackContent.contentTypeId);
          results[identifier] = {
            ...fallbackContent,
            contentType,
          };
        }
      }
    }

    return results;
  },
});

// Get content history with pagination
export const getContentHistory = query({
  args: {
    identifier: v.string(),
    language: v.string(),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;

    const content = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    if (!content) return [];

    const history = await ctx.db
      .query("contentHistory")
      .withIndex("by_content", (q) => q.eq("contentId", content._id))
      .order("desc")
      .take(limit);

    // Enrich with user information if available
    const enrichedHistory = await Promise.all(
      history.map(async (entry) => {
        const user = await ctx.db
          .query("users")
          .filter((q) => q.eq(q.field("clerkId"), entry.changedBy))
          .first();

        return {
          ...entry,
          user: user ? {
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
          } : null,
        };
      })
    );

    return enrichedHistory;
  },
});

// Publish content (change from draft to published)
export const publishContent = mutation({
  args: {
    identifier: v.string(),
    language: v.string(),
    publishNote: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!hasPermission(user, "content:publish")) {
      throw new Error("Insufficient permissions: content:publish required");
    }

    const content = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .filter((q) => q.eq(q.field("status"), "draft"))
      .first();

    if (!content) {
      throw new Error("Draft content not found");
    }

    // Create history entry
    await ctx.db.insert("contentHistory", {
      contentId: content._id,
      data: content.data,
      version: content.version,
      changeType: "published",
      changedBy: "system", // TODO: Get from auth
      changeNote: args.publishNote || "Published content",
      createdAt: Date.now(),
    });

    // Update content status
    return await ctx.db.patch(content._id, {
      status: "published",
      updatedAt: Date.now(),
    });
  },
});

// Unpublish content (change from published to draft)
export const unpublishContent = mutation({
  args: {
    identifier: v.string(),
    language: v.string(),
    unpublishNote: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check

    const content = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .filter((q) => q.eq(q.field("status"), "published"))
      .first();

    if (!content) {
      throw new Error("Published content not found");
    }

    // Create history entry
    await ctx.db.insert("contentHistory", {
      contentId: content._id,
      data: content.data,
      version: content.version,
      changeType: "unpublished",
      changedBy: "system", // TODO: Get from auth
      changeNote: args.unpublishNote || "Unpublished content",
      createdAt: Date.now(),
    });

    // Update content status
    return await ctx.db.patch(content._id, {
      status: "draft",
      updatedAt: Date.now(),
    });
  },
});

// Duplicate content to another language
export const duplicateContent = mutation({
  args: {
    identifier: v.string(),
    fromLanguage: v.string(),
    toLanguage: v.string(),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check

    const sourceContent = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", args.fromLanguage)
      )
      .first();

    if (!sourceContent) {
      throw new Error("Source content not found");
    }

    // Check if target already exists
    const existingTarget = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", args.toLanguage)
      )
      .first();

    if (existingTarget) {
      throw new Error("Content already exists in target language");
    }

    const now = Date.now();
    const status = args.status || "draft";

    // Create new content
    return await ctx.db.insert("content", {
      identifier: args.identifier,
      language: args.toLanguage,
      data: sourceContent.data, // Copy data structure
      contentTypeId: sourceContent.contentTypeId,
      status,
      version: 1,
      createdBy: "system", // TODO: Get from auth
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Revert content to a previous version
export const revertContent = mutation({
  args: {
    identifier: v.string(),
    language: v.string(),
    targetVersion: v.number(),
    revertNote: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check

    const currentContent = await ctx.db
      .query("content")
      .withIndex("by_identifier_language", (q) =>
        q.eq("identifier", args.identifier).eq("language", args.language)
      )
      .first();

    if (!currentContent) {
      throw new Error("Content not found");
    }

    // Find the target version in history
    const targetHistory = await ctx.db
      .query("contentHistory")
      .withIndex("by_version", (q) =>
        q.eq("contentId", currentContent._id).eq("version", args.targetVersion)
      )
      .first();

    if (!targetHistory) {
      throw new Error("Target version not found in history");
    }

    // Create history entry for current state
    await ctx.db.insert("contentHistory", {
      contentId: currentContent._id,
      data: currentContent.data,
      version: currentContent.version,
      changeType: "reverted",
      changedBy: "system", // TODO: Get from auth
      changeNote: args.revertNote || `Reverted to version ${args.targetVersion}`,
      createdAt: Date.now(),
    });

    // Update content with target version data
    return await ctx.db.patch(currentContent._id, {
      data: targetHistory.data,
      version: currentContent.version + 1,
      updatedAt: Date.now(),
    });
  },
});

// Search content across all languages and types
export const searchContent = query({
  args: {
    query: v.string(),
    language: v.optional(v.string()),
    contentType: v.optional(v.string()),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    const status = args.status || "published";

    // Get all content matching status
    let query = ctx.db
      .query("content")
      .filter((q) => q.eq(q.field("status"), status));

    if (args.language) {
      query = query.withIndex("by_language", (q) => q.eq("language", args.language));
    }

    const allContent = await query.collect();

    // Filter by content type if specified
    let filteredContent = allContent;
    if (args.contentType) {
      const contentType = await ctx.db
        .query("contentTypes")
        .withIndex("by_name", (q) => q.eq("name", args.contentType))
        .first();

      if (contentType) {
        filteredContent = allContent.filter(c => c.contentTypeId === contentType._id);
      }
    }

    // Simple text search in content data
    const searchResults = filteredContent.filter(content => {
      const searchText = args.query.toLowerCase();
      const contentString = JSON.stringify(content.data).toLowerCase();
      return contentString.includes(searchText) ||
             content.identifier.toLowerCase().includes(searchText);
    });

    // Take only the requested limit
    const limitedResults = searchResults.slice(0, limit);

    // Enrich with content type information
    const enrichedResults = await Promise.all(
      limitedResults.map(async (content) => {
        const contentType = await ctx.db.get(content.contentTypeId);
        return {
          ...content,
          contentType,
        };
      })
    );

    return enrichedResults;
  },
});

// Get content statistics
export const getContentStats = query({
  args: {
    language: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const language = args.language;

    let query = ctx.db.query("content");
    if (language) {
      query = query.withIndex("by_language", (q) => q.eq("language", language));
    }

    const allContent = await query.collect();

    const stats = {
      total: allContent.length,
      published: allContent.filter(c => c.status === "published").length,
      draft: allContent.filter(c => c.status === "draft").length,
      byLanguage: {} as Record<string, number>,
      byContentType: {} as Record<string, number>,
      recentlyUpdated: allContent
        .sort((a, b) => b.updatedAt - a.updatedAt)
        .slice(0, 10),
    };

    // Group by language
    for (const content of allContent) {
      stats.byLanguage[content.language] = (stats.byLanguage[content.language] || 0) + 1;
    }

    // Group by content type
    const contentTypes = await ctx.db.query("contentTypes").collect();
    const contentTypeMap = new Map(contentTypes.map(ct => [ct._id, ct.name]));

    for (const content of allContent) {
      const typeName = contentTypeMap.get(content.contentTypeId) || "unknown";
      stats.byContentType[typeName] = (stats.byContentType[typeName] || 0) + 1;
    }

    return stats;
  },
});

// Get content by ID
export const getContentById = query({
  args: { id: v.id("content") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Temporary alias for backward compatibility (remove after browser cache clears)
export const createOrUpdateContent = upsertContent;

// Find duplicate content entries (same identifier + language)
export const findDuplicateContent = query({
  args: {},
  handler: async (ctx) => {
    const allContent = await ctx.db.query("content").collect();

    // Group by identifier + language
    const grouped = allContent.reduce((acc, item) => {
      const key = `${item.identifier}-${item.language}`;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(item);
      return acc;
    }, {} as Record<string, any[]>);

    // Find groups with more than one item (duplicates)
    const duplicates = Object.entries(grouped)
      .filter(([_, items]) => items.length > 1)
      .map(([key, items]) => ({
        key,
        identifier: items[0].identifier,
        language: items[0].language,
        count: items.length,
        items: items.sort((a, b) => b.updatedAt - a.updatedAt) // Most recent first
      }));

    return duplicates;
  },
});

// Add web development service card
export const addWebDevServiceCard = mutation({
  args: {},
  handler: async (ctx) => {
    // Get the service_card content type
    const serviceCardType = await ctx.db
      .query("contentTypes")
      .withIndex("by_name", (q) => q.eq("name", "service_card"))
      .first();

    if (!serviceCardType) {
      throw new Error("Service card content type not found");
    }

    const now = Date.now();

    // Create English version
    const enCard = await ctx.db.insert("content", {
      identifier: "service-webdev",
      language: "en",
      contentTypeId: serviceCardType._id,
      status: "published" as const,
      data: {
        title: "Web & Mobile Development",
        description: "Modern, responsive websites and mobile applications built with cutting-edge technologies. Custom development solutions including e-commerce, web apps, and mobile apps with SEO optimization.",
        icon: "Code",
      },
      version: 1,
      createdBy: "system",
      createdAt: now,
      updatedAt: now,
    });

    // Create Spanish version
    const esCard = await ctx.db.insert("content", {
      identifier: "service-webdev",
      language: "es",
      contentTypeId: serviceCardType._id,
      status: "published" as const,
      data: {
        title: "Desarrollo Web y Móvil",
        description: "Sitios web modernos y responsivos y aplicaciones móviles construidas con tecnologías de vanguardia. Soluciones de desarrollo personalizado incluyendo comercio electrónico, aplicaciones web y móviles con optimización SEO.",
        icon: "Code",
      },
      version: 1,
      createdBy: "system",
      createdAt: now,
      updatedAt: now,
    });

    return { enCard, esCard };
  },
});

// Initialize about page content (value cards and timeline)
export const initializeAboutContent = mutation({
  args: {},
  handler: async (ctx) => {
    // Get content types
    const valueCardType = await ctx.db
      .query("contentTypes")
      .withIndex("by_name", (q) => q.eq("name", "value_card"))
      .first();

    const timelineItemType = await ctx.db
      .query("contentTypes")
      .withIndex("by_name", (q) => q.eq("name", "timeline_item"))
      .first();

    if (!valueCardType || !timelineItemType) {
      throw new Error("Required content types not found. Please initialize content types first.");
    }

    const now = Date.now();
    const results = [];

    // Create value cards
    const values = [
      {
        identifier: "value-mission",
        en: { title: "Our Mission", description: "To provide cutting-edge technology and security solutions that protect and empower businesses across Equatorial Guinea.", icon: "Target" },
        es: { title: "Nuestra Misión", description: "Proporcionar soluciones tecnológicas y de seguridad de vanguardia que protejan y empoderen a las empresas en Guinea Ecuatorial.", icon: "Target" }
      },
      {
        identifier: "value-excellence",
        en: { title: "Excellence", description: "We maintain the highest standards in everything we do, from initial consultation to ongoing support.", icon: "Award" },
        es: { title: "Excelencia", description: "Mantenemos los más altos estándares en todo lo que hacemos, desde la consulta inicial hasta el soporte continuo.", icon: "Award" }
      },
      {
        identifier: "value-partnership",
        en: { title: "Partnership", description: "We work closely with our clients to understand their unique needs and deliver tailored solutions.", icon: "Users" },
        es: { title: "Asociación", description: "Trabajamos estrechamente con nuestros clientes para entender sus necesidades únicas y entregar soluciones personalizadas.", icon: "Users" }
      },
      {
        identifier: "value-reliability",
        en: { title: "Reliability", description: "Our 24/7 support and monitoring ensure your systems are always protected and operational.", icon: "Clock" },
        es: { title: "Confiabilidad", description: "Nuestro soporte y monitoreo 24/7 aseguran que sus sistemas estén siempre protegidos y operativos.", icon: "Clock" }
      }
    ];

    for (let i = 0; i < values.length; i++) {
      const value = values[i];

      // Create English version
      const enCard = await ctx.db.insert("content", {
        identifier: value.identifier,
        language: "en",
        contentTypeId: valueCardType._id,
        status: "published" as const,
        data: { ...value.en, order: i },
        version: 1,
        createdBy: "system",
        createdAt: now,
        updatedAt: now,
      });

      // Create Spanish version
      const esCard = await ctx.db.insert("content", {
        identifier: value.identifier,
        language: "es",
        contentTypeId: valueCardType._id,
        status: "published" as const,
        data: { ...value.es, order: i },
        version: 1,
        createdBy: "system",
        createdAt: now,
        updatedAt: now,
      });

      results.push({ identifier: value.identifier, enCard, esCard });
    }

    // Create timeline items
    const timeline = [
      {
        identifier: "timeline-2009",
        en: { year: "2009", title: "Company Founded", description: "OfficeTech Guinea was established with a vision to bring advanced technology solutions to the region." },
        es: { year: "2009", title: "Fundación de la Empresa", description: "OfficeTech Guinea fue establecida con la visión de traer soluciones tecnológicas avanzadas a la región." }
      },
      {
        identifier: "timeline-2012",
        en: { year: "2012", title: "First Major Contract", description: "Secured our first major cybersecurity implementation for a leading financial institution." },
        es: { year: "2012", title: "Primer Contrato Mayor", description: "Aseguramos nuestra primera implementación mayor de ciberseguridad para una institución financiera líder." }
      },
      {
        identifier: "timeline-2016",
        en: { year: "2016", title: "Expansion", description: "Expanded our services to include comprehensive network infrastructure and surveillance systems." },
        es: { year: "2016", title: "Expansión", description: "Expandimos nuestros servicios para incluir infraestructura de red integral y sistemas de vigilancia." }
      },
      {
        identifier: "timeline-2020",
        en: { year: "2020", title: "Digital Transformation", description: "Helped over 100 businesses transition to digital-first operations during the pandemic." },
        es: { year: "2020", title: "Transformación Digital", description: "Ayudamos a más de 100 empresas a hacer la transición a operaciones digitales durante la pandemia." }
      },
      {
        identifier: "timeline-2024",
        en: { year: "2024", title: "Innovation Leader", description: "Recognized as the leading technology solutions provider in Equatorial Guinea." },
        es: { year: "2024", title: "Líder en Innovación", description: "Reconocidos como el proveedor líder de soluciones tecnológicas en Guinea Ecuatorial." }
      }
    ];

    for (let i = 0; i < timeline.length; i++) {
      const item = timeline[i];

      // Create English version
      const enItem = await ctx.db.insert("content", {
        identifier: item.identifier,
        language: "en",
        contentTypeId: timelineItemType._id,
        status: "published" as const,
        data: { ...item.en, order: i },
        version: 1,
        createdBy: "system",
        createdAt: now,
        updatedAt: now,
      });

      // Create Spanish version
      const esItem = await ctx.db.insert("content", {
        identifier: item.identifier,
        language: "es",
        contentTypeId: timelineItemType._id,
        status: "published" as const,
        data: { ...item.es, order: i },
        version: 1,
        createdBy: "system",
        createdAt: now,
        updatedAt: now,
      });

      results.push({ identifier: item.identifier, enItem, esItem });
    }

    return results;
  },
});

// Delete content by ID
export const deleteContent = mutation({
  args: { id: v.id("content") },
  handler: async (ctx, args) => {
    // TODO: Add authentication check

    const content = await ctx.db.get(args.id);
    if (!content) {
      throw new Error("Content not found");
    }

    // Create history entry before deletion
    const now = Date.now();
    await ctx.db.insert("contentHistory", {
      contentId: content._id,
      data: content.data,
      version: content.version,
      changedBy: "system", // TODO: Get from auth
      changeType: "deleted" as const,
      changeNote: "Content deleted",
      createdAt: now,
    });

    // Delete the content
    await ctx.db.delete(args.id);

    return { success: true };
  },
});

// Create new content with auto-generated identifier
export const createNewContent = mutation({
  args: {
    contentTypeName: v.string(),
    language: v.string(),
    data: v.any(),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check

    // Get content type
    const contentType = await ctx.db
      .query("contentTypes")
      .withIndex("by_name", (q) => q.eq("name", args.contentTypeName))
      .first();

    if (!contentType) {
      throw new Error(`Content type "${args.contentTypeName}" not found`);
    }

    // Generate unique identifier
    const baseIdentifier = args.contentTypeName.replace(/_/g, '-');
    const timestamp = Date.now();
    const identifier = `${baseIdentifier}-${timestamp}`;

    const now = Date.now();

    const contentId = await ctx.db.insert("content", {
      identifier,
      language: args.language,
      contentTypeId: contentType._id,
      status: args.status || "draft",
      data: args.data,
      version: 1,
      createdBy: "system", // TODO: Get from auth
      createdAt: now,
      updatedAt: now,
    });

    // Create history entry
    await ctx.db.insert("contentHistory", {
      contentId,
      data: args.data,
      version: 1,
      changedBy: "system", // TODO: Get from auth
      changeType: "created" as const,
      changeNote: "Content created",
      createdAt: now,
    });

    return contentId;
  },
});

// Initialize service cards with full data
export const initializeServiceCards = mutation({
  args: {},
  handler: async (ctx) => {
    // Get service_card content type
    const serviceCardType = await ctx.db
      .query("contentTypes")
      .withIndex("by_name", (q) => q.eq("name", "service_card"))
      .first();

    if (!serviceCardType) {
      throw new Error("service_card content type not found. Please initialize content types first.");
    }

    const now = Date.now();
    const results = [];

    // Define comprehensive service cards
    const services = [
      {
        identifier: "service-cybersecurity",
        slug: "cybersecurity-solutions",
        en: {
          title: "Cybersecurity Solutions",
          description: "Comprehensive protection against digital threats with advanced monitoring, threat detection, and response systems.",
          fullDescription: `<h2>Advanced Cybersecurity Protection</h2>
          <p>In a world where cyberattacks are becoming increasingly sophisticated, prevention, detection, response, recovery, and continuous improvement are the keys to staying one step ahead. Our cybersecurity solutions provide comprehensive protection for your digital assets.</p>

          <h3>Our Cybersecurity Services Include:</h3>
          <ul>
            <li>24/7 Security Monitoring and Threat Detection</li>
            <li>Firewall Configuration and Management</li>
            <li>Vulnerability Assessments and Penetration Testing</li>
            <li>Incident Response and Recovery</li>
            <li>Security Awareness Training</li>
            <li>Compliance and Risk Management</li>
          </ul>

          <p>Protect your digital world today with our industry-leading cybersecurity solutions!</p>`,
          icon: "Shield",
          features: ["24/7 Monitoring", "Threat Detection", "Incident Response", "Security Training"],
          benefits: ["Reduced Security Risks", "Compliance Assurance", "Business Continuity", "Peace of Mind"]
        },
        es: {
          title: "Soluciones de Ciberseguridad",
          description: "Protección integral contra amenazas digitales con monitoreo avanzado, detección de amenazas y sistemas de respuesta.",
          fullDescription: `<h2>Protección Avanzada de Ciberseguridad</h2>
          <p>En un mundo donde los ciberataques se vuelven cada vez más sofisticados, la prevención, detección, respuesta, recuperación y mejora continua son las claves para mantenerse un paso adelante. Nuestras soluciones de ciberseguridad brindan protección integral para sus activos digitales.</p>

          <h3>Nuestros Servicios de Ciberseguridad Incluyen:</h3>
          <ul>
            <li>Monitoreo de Seguridad 24/7 y Detección de Amenazas</li>
            <li>Configuración y Gestión de Firewall</li>
            <li>Evaluaciones de Vulnerabilidad y Pruebas de Penetración</li>
            <li>Respuesta a Incidentes y Recuperación</li>
            <li>Capacitación en Conciencia de Seguridad</li>
            <li>Gestión de Cumplimiento y Riesgos</li>
          </ul>

          <p>¡Proteja su mundo digital hoy con nuestras soluciones de ciberseguridad líderes en la industria!</p>`,
          icon: "Shield",
          features: ["Monitoreo 24/7", "Detección de Amenazas", "Respuesta a Incidentes", "Capacitación en Seguridad"],
          benefits: ["Riesgos de Seguridad Reducidos", "Garantía de Cumplimiento", "Continuidad del Negocio", "Tranquilidad"]
        }
      },
      {
        identifier: "service-network",
        slug: "network-infrastructure",
        en: {
          title: "Network Infrastructure",
          description: "Reliable internet connectivity and network infrastructure designed for optimal performance and scalability.",
          fullDescription: `<h2>Comprehensive Network Solutions</h2>
          <p>Our network infrastructure services provide reliable, high-performance connectivity solutions for businesses of all sizes. From basic internet connectivity to complex enterprise networks, we have the expertise to design and implement the perfect solution for your needs.</p>

          <h3>Network Services Include:</h3>
          <ul>
            <li>MPLS (Multi-Protocol Label Switching) Networks</li>
            <li>SD-WAN (Software-Defined Wide Area Network)</li>
            <li>VPL (Layer 2 VPN) Solutions</li>
            <li>Managed Enterprise Internet Services</li>
            <li>Network Design and Implementation</li>
            <li>24/7 Network Monitoring and Support</li>
          </ul>

          <p>Whether you're managing a few branches or a global network, our solutions ensure smooth communication and improved productivity.</p>`,
          icon: "Wifi",
          features: ["High-Speed Connectivity", "Scalable Solutions", "24/7 Support", "Enterprise-Grade Security"],
          benefits: ["Improved Productivity", "Cost Efficiency", "Reliable Performance", "Future-Ready Infrastructure"]
        },
        es: {
          title: "Infraestructura de Red",
          description: "Conectividad confiable a internet e infraestructura de red diseñada para rendimiento óptimo y escalabilidad.",
          fullDescription: `<h2>Soluciones Integrales de Red</h2>
          <p>Nuestros servicios de infraestructura de red proporcionan soluciones de conectividad confiables y de alto rendimiento para empresas de todos los tamaños. Desde conectividad básica a internet hasta redes empresariales complejas, tenemos la experiencia para diseñar e implementar la solución perfecta para sus necesidades.</p>

          <h3>Los Servicios de Red Incluyen:</h3>
          <ul>
            <li>Redes MPLS (Multi-Protocol Label Switching)</li>
            <li>SD-WAN (Software-Defined Wide Area Network)</li>
            <li>Soluciones VPL (Layer 2 VPN)</li>
            <li>Servicios de Internet Empresarial Gestionado</li>
            <li>Diseño e Implementación de Redes</li>
            <li>Monitoreo y Soporte de Red 24/7</li>
          </ul>

          <p>Ya sea que esté gestionando unas pocas sucursales o una red global, nuestras soluciones aseguran comunicación fluida y productividad mejorada.</p>`,
          icon: "Wifi",
          features: ["Conectividad de Alta Velocidad", "Soluciones Escalables", "Soporte 24/7", "Seguridad de Nivel Empresarial"],
          benefits: ["Productividad Mejorada", "Eficiencia de Costos", "Rendimiento Confiable", "Infraestructura Lista para el Futuro"]
        }
      },
      {
        identifier: "service-surveillance",
        slug: "electronic-surveillance",
        en: {
          title: "Electronic Surveillance",
          description: "Comprehensive electronic security solutions to protect your home, business, and family with advanced monitoring systems.",
          fullDescription: `<h2>Advanced Electronic Security Solutions</h2>
          <p>At OfficeTech, we specialize in providing comprehensive electronic security solutions to protect what matters most: your home, your business, and your family. We offer a wide range of products and services designed to give you peace of mind and control at all times.</p>

          <h3>Featured Products:</h3>
          <ul>
            <li><strong>Security Cameras:</strong> IP cameras with night vision and 4K resolution, wireless cameras for easy installation</li>
            <li><strong>Access Control Systems:</strong> Biometric systems, key cards, and smart locks</li>
            <li><strong>Alarm Systems:</strong> Motion detectors, door/window sensors, and emergency alerts</li>
            <li><strong>Video Surveillance:</strong> Real-time monitoring and cloud storage solutions</li>
          </ul>

          <h3>Services:</h3>
          <ul>
            <li>Professional installation of security systems</li>
            <li>24/7 monitoring for alarms and cameras</li>
            <li>Maintenance and specialized technical support</li>
          </ul>`,
          icon: "Camera",
          features: ["24/7 Monitoring", "Professional Installation", "Cloud Storage", "Mobile Access"],
          benefits: ["Enhanced Security", "Peace of Mind", "Remote Monitoring", "Professional Support"]
        },
        es: {
          title: "Vigilancia Electrónica",
          description: "Soluciones integrales de seguridad electrónica para proteger su hogar, negocio y familia con sistemas de monitoreo avanzados.",
          fullDescription: `<h2>Soluciones Avanzadas de Seguridad Electrónica</h2>
          <p>En OfficeTech, nos especializamos en brindar soluciones integrales de seguridad electrónica para proteger lo que más importa: su hogar, su negocio y su familia. Ofrecemos una amplia gama de productos y servicios diseñados para brindarle tranquilidad y control en todo momento.</p>

          <h3>Productos Destacados:</h3>
          <ul>
            <li><strong>Cámaras de Seguridad:</strong> Cámaras IP con visión nocturna y resolución 4K, cámaras inalámbricas para instalación fácil</li>
            <li><strong>Sistemas de Control de Acceso:</strong> Sistemas biométricos, tarjetas de acceso y cerraduras inteligentes</li>
            <li><strong>Sistemas de Alarma:</strong> Detectores de movimiento, sensores de puertas/ventanas y alertas de emergencia</li>
            <li><strong>Videovigilancia:</strong> Monitoreo en tiempo real y soluciones de almacenamiento en la nube</li>
          </ul>

          <h3>Servicios:</h3>
          <ul>
            <li>Instalación profesional de sistemas de seguridad</li>
            <li>Monitoreo 24/7 para alarmas y cámaras</li>
            <li>Mantenimiento y soporte técnico especializado</li>
          </ul>`,
          icon: "Camera",
          features: ["Monitoreo 24/7", "Instalación Profesional", "Almacenamiento en la Nube", "Acceso Móvil"],
          benefits: ["Seguridad Mejorada", "Tranquilidad", "Monitoreo Remoto", "Soporte Profesional"]
        }
      },
      {
        identifier: "service-training",
        slug: "it-training",
        en: {
          title: "IT Training & Development",
          description: "Professional development programs to enhance your team's technical skills and cybersecurity awareness.",
          fullDescription: `<h2>Corporate PC Skills Development</h2>
          <p>At OfficeTech, we understand that in today's fast-paced business environment, having strong computer skills is essential for success. That's why we offer comprehensive training programs designed to empower your team with the knowledge and expertise they need to excel in the workplace.</p>

          <h3>Training Programs Include:</h3>
          <ul>
            <li><strong>Microsoft Office Suite:</strong> Word, Excel, PowerPoint, Outlook, and Teams</li>
            <li><strong>Windows Operating Systems:</strong> Navigation, file management, and troubleshooting</li>
            <li><strong>Cybersecurity Awareness:</strong> Best practices for online safety and data protection</li>
            <li><strong>Network Fundamentals:</strong> Basic networking concepts and troubleshooting</li>
            <li><strong>Custom Training:</strong> Tailored programs for specific business needs</li>
          </ul>

          <p>Whether you're looking to enhance productivity, streamline operations, or simply stay ahead of the curve, our tailored training solutions are here to help.</p>`,
          icon: "GraduationCap",
          features: ["Certified Instructors", "Hands-on Learning", "Flexible Scheduling", "Custom Curriculum"],
          benefits: ["Improved Productivity", "Enhanced Skills", "Better Security Awareness", "Competitive Advantage"]
        },
        es: {
          title: "Capacitación y Desarrollo en TI",
          description: "Programas de desarrollo profesional para mejorar las habilidades técnicas de su equipo y la conciencia de ciberseguridad.",
          fullDescription: `<h2>Desarrollo de Habilidades Corporativas en PC</h2>
          <p>En OfficeTech, entendemos que en el entorno empresarial acelerado de hoy, tener habilidades sólidas en computación es esencial para el éxito. Por eso ofrecemos programas de capacitación integrales diseñados para empoderar a su equipo con el conocimiento y la experiencia que necesitan para sobresalir en el lugar de trabajo.</p>

          <h3>Los Programas de Capacitación Incluyen:</h3>
          <ul>
            <li><strong>Suite de Microsoft Office:</strong> Word, Excel, PowerPoint, Outlook y Teams</li>
            <li><strong>Sistemas Operativos Windows:</strong> Navegación, gestión de archivos y solución de problemas</li>
            <li><strong>Conciencia de Ciberseguridad:</strong> Mejores prácticas para seguridad en línea y protección de datos</li>
            <li><strong>Fundamentos de Redes:</strong> Conceptos básicos de redes y solución de problemas</li>
            <li><strong>Capacitación Personalizada:</strong> Programas adaptados para necesidades empresariales específicas</li>
          </ul>

          <p>Ya sea que busque mejorar la productividad, optimizar operaciones o simplemente mantenerse a la vanguardia, nuestras soluciones de capacitación personalizadas están aquí para ayudar.</p>`,
          icon: "GraduationCap",
          features: ["Instructores Certificados", "Aprendizaje Práctico", "Horarios Flexibles", "Currículo Personalizado"],
          benefits: ["Productividad Mejorada", "Habilidades Mejoradas", "Mejor Conciencia de Seguridad", "Ventaja Competitiva"]
        }
      },
      {
        identifier: "service-webdev",
        slug: "web-development",
        en: {
          title: "Web & Mobile Development",
          description: "Modern, responsive websites and mobile applications built with cutting-edge technologies and SEO optimization.",
          fullDescription: `<h2>Modern Web & Mobile Development</h2>
          <p>Our web and mobile development services provide comprehensive solutions for businesses looking to establish a strong digital presence. From simple websites to complex web applications and mobile apps, we deliver high-quality, scalable solutions.</p>

          <h3>Development Services Include:</h3>
          <ul>
            <li><strong>Website Development:</strong> Responsive, modern websites optimized for all devices</li>
            <li><strong>E-commerce Solutions:</strong> Online stores with payment integration and inventory management</li>
            <li><strong>Web Applications:</strong> Custom web apps tailored to your business needs</li>
            <li><strong>Mobile Applications:</strong> Native and cross-platform mobile app development</li>
            <li><strong>SEO Optimization:</strong> Search engine optimization for better visibility</li>
            <li><strong>Maintenance & Support:</strong> Ongoing support and updates</li>
          </ul>

          <p>We use cutting-edge technologies and follow industry best practices to ensure your digital solutions are fast, secure, and user-friendly.</p>`,
          icon: "Code",
          features: ["Responsive Design", "SEO Optimized", "Mobile-First", "Modern Technologies"],
          benefits: ["Increased Online Presence", "Better User Experience", "Higher Conversion Rates", "Scalable Solutions"]
        },
        es: {
          title: "Desarrollo Web y Móvil",
          description: "Sitios web modernos y responsivos y aplicaciones móviles construidas con tecnologías de vanguardia y optimización SEO.",
          fullDescription: `<h2>Desarrollo Web y Móvil Moderno</h2>
          <p>Nuestros servicios de desarrollo web y móvil proporcionan soluciones integrales para empresas que buscan establecer una fuerte presencia digital. Desde sitios web simples hasta aplicaciones web complejas y aplicaciones móviles, entregamos soluciones escalables de alta calidad.</p>

          <h3>Los Servicios de Desarrollo Incluyen:</h3>
          <ul>
            <li><strong>Desarrollo de Sitios Web:</strong> Sitios web responsivos y modernos optimizados para todos los dispositivos</li>
            <li><strong>Soluciones de Comercio Electrónico:</strong> Tiendas en línea con integración de pagos y gestión de inventario</li>
            <li><strong>Aplicaciones Web:</strong> Aplicaciones web personalizadas adaptadas a las necesidades de su negocio</li>
            <li><strong>Aplicaciones Móviles:</strong> Desarrollo de aplicaciones móviles nativas y multiplataforma</li>
            <li><strong>Optimización SEO:</strong> Optimización para motores de búsqueda para mejor visibilidad</li>
            <li><strong>Mantenimiento y Soporte:</strong> Soporte continuo y actualizaciones</li>
          </ul>

          <p>Utilizamos tecnologías de vanguardia y seguimos las mejores prácticas de la industria para asegurar que sus soluciones digitales sean rápidas, seguras y fáciles de usar.</p>`,
          icon: "Code",
          features: ["Diseño Responsivo", "Optimizado para SEO", "Mobile-First", "Tecnologías Modernas"],
          benefits: ["Mayor Presencia en Línea", "Mejor Experiencia de Usuario", "Tasas de Conversión Más Altas", "Soluciones Escalables"]
        }
      }
    ];

    for (let i = 0; i < services.length; i++) {
      const service = services[i];

      // Create English version
      const enCard = await ctx.db.insert("content", {
        identifier: service.identifier,
        language: "en",
        contentTypeId: serviceCardType._id,
        status: "published" as const,
        data: { ...service.en, slug: service.slug, order: i },
        version: 1,
        createdBy: "system",
        createdAt: now,
        updatedAt: now,
      });

      // Create Spanish version
      const esCard = await ctx.db.insert("content", {
        identifier: service.identifier,
        language: "es",
        contentTypeId: serviceCardType._id,
        status: "published" as const,
        data: { ...service.es, slug: service.slug, order: i },
        version: 1,
        createdBy: "system",
        createdAt: now,
        updatedAt: now,
      });

      results.push({ identifier: service.identifier, enCard, esCard });
    }

    return results;
  },
});

// Add image to web development service card
export const addWebDevImage = mutation({
  args: {},
  handler: async (ctx) => {
    const imagePath = "/img/Coding_img.jpg";

    // Update English version
    const enContent = await ctx.db
      .query("content")
      .filter((q) => q.and(
        q.eq(q.field("identifier"), "service-webdev"),
        q.eq(q.field("language"), "en")
      ))
      .first();

    if (enContent) {
      await ctx.db.patch(enContent._id, {
        data: {
          ...enContent.data,
          image: { url: imagePath }
        },
        updatedAt: Date.now()
      });
    }

    // Update Spanish version
    const esContent = await ctx.db
      .query("content")
      .filter((q) => q.and(
        q.eq(q.field("identifier"), "service-webdev"),
        q.eq(q.field("language"), "es")
      ))
      .first();

    if (esContent) {
      await ctx.db.patch(esContent._id, {
        data: {
          ...esContent.data,
          image: { url: imagePath }
        },
        updatedAt: Date.now()
      });
    }

    return { success: true, imagePath, updated: [enContent?._id, esContent?._id].filter(Boolean) };
  },
});

// Update content
export const updateContent = mutation({
  args: {
    id: v.id("content"),
    data: v.optional(v.any()),
    status: v.optional(v.union(v.literal("draft"), v.literal("published"), v.literal("archived"))),
  },
  handler: async (ctx, args) => {
    // Check authentication and permissions
    const user = await getCurrentUser(ctx);
    if (!user || !hasPermission(user, "content:write")) {
      throw new Error("Not authorized to update content");
    }

    const existing = await ctx.db.get(args.id);
    if (!existing) {
      throw new Error("Content not found");
    }

    const updates: any = {
      updatedAt: Date.now(),
    };

    if (args.data !== undefined) {
      // Merge the new data with existing data instead of replacing
      updates.data = {
        ...existing.data,
        ...args.data
      };
    }

    if (args.status !== undefined) {
      updates.status = args.status;
    }

    return await ctx.db.patch(args.id, updates);
  },
});



// Bulk operations
export const bulkUpdateContent = mutation({
  args: {
    updates: v.array(v.object({
      identifier: v.string(),
      language: v.string(),
      data: v.optional(v.any()),
      status: v.optional(v.union(v.literal("draft"), v.literal("published"))),
    })),
    changeNote: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // TODO: Add authentication check

    const results = [];
    const now = Date.now();

    for (const update of args.updates) {
      const content = await ctx.db
        .query("content")
        .withIndex("by_identifier_language", (q) =>
          q.eq("identifier", update.identifier).eq("language", update.language)
        )
        .first();

      if (content) {
        // Create history entry
        await ctx.db.insert("contentHistory", {
          contentId: content._id,
          data: content.data,
          version: content.version,
          changeType: "updated",
          changedBy: "system", // TODO: Get from auth
          changeNote: args.changeNote || "Bulk update",
          createdAt: now,
        });

        // Update content
        const updateData: any = {
          version: content.version + 1,
          updatedAt: now,
        };

        if (update.data !== undefined) updateData.data = update.data;
        if (update.status !== undefined) updateData.status = update.status;

        await ctx.db.patch(content._id, updateData);
        results.push({ identifier: update.identifier, language: update.language, status: "updated" });
      } else {
        results.push({ identifier: update.identifier, language: update.language, status: "not_found" });
      }
    }

    return results;
  },
});

// Initialize network solutions content
export const initializeNetworkSolutions = mutation({
  args: {},
  handler: async (ctx) => {
    // Get network_solution content type
    const networkSolutionType = await ctx.db
      .query("contentTypes")
      .withIndex("by_name", (q) => q.eq("name", "network_solution"))
      .first();

    if (!networkSolutionType) {
      throw new Error("network_solution content type not found. Please initialize content types first.");
    }

    const now = Date.now();
    const results = [];

    // Define network solutions from content.md
    const networkSolutions = [
      {
        identifier: "network-international-connectivity",
        slug: "international-connectivity",
        en: {
          title: "International Connectivity",
          description: "MPLS, SD-WAN and VPL solutions for global business connectivity with optimized performance and security.",
          fullDescription: `<h2>International Connectivity Solutions</h2>
          <p>Our international connectivity solutions provide businesses with reliable, high-performance global network access through advanced technologies including MPLS, SD-WAN, and VPL.</p>

          <h3>MPLS (Multi-Protocol Label Switching)</h3>
          <p>MPLS technology provides efficient data forwarding and traffic engineering capabilities, ensuring optimal performance for your critical business applications across international networks.</p>

          <h3>SD-WAN (Software-Defined Wide Area Network)</h3>
          <p>Our SD-WAN solutions offer centralized control, improved application performance, and cost-effective connectivity to multiple international locations.</p>

          <h3>VPL (Layer 2 VPN)</h3>
          <p>Virtual Private LAN services provide secure, scalable connectivity between your international offices, maintaining the same level of security as a private network.</p>`,
          icon: "Globe",
          features: ["MPLS Networks", "SD-WAN Solutions", "VPL (Layer 2 VPN)", "Global Reach", "24/7 Support"],
          benefits: ["Improved Performance", "Cost Efficiency", "Scalable Solutions", "Enhanced Security"],
          technologies: ["MPLS", "SD-WAN", "VPL", "BGP", "QoS"]
        },
        es: {
          title: "Conectividad Internacional",
          description: "Soluciones MPLS, SD-WAN y VPL para conectividad empresarial global con rendimiento optimizado y seguridad.",
          fullDescription: `<h2>Soluciones de Conectividad Internacional</h2>
          <p>Nuestras soluciones de conectividad internacional proporcionan a las empresas acceso confiable y de alto rendimiento a redes globales a través de tecnologías avanzadas incluyendo MPLS, SD-WAN y VPL.</p>

          <h3>MPLS (Multi-Protocol Label Switching)</h3>
          <p>La tecnología MPLS proporciona capacidades eficientes de reenvío de datos e ingeniería de tráfico, asegurando rendimiento óptimo para sus aplicaciones empresariales críticas a través de redes internacionales.</p>

          <h3>SD-WAN (Software-Defined Wide Area Network)</h3>
          <p>Nuestras soluciones SD-WAN ofrecen control centralizado, rendimiento mejorado de aplicaciones y conectividad rentable a múltiples ubicaciones internacionales.</p>

          <h3>VPL (Layer 2 VPN)</h3>
          <p>Los servicios de LAN privada virtual proporcionan conectividad segura y escalable entre sus oficinas internacionales, manteniendo el mismo nivel de seguridad que una red privada.</p>`,
          icon: "Globe",
          features: ["Redes MPLS", "Soluciones SD-WAN", "VPL (Layer 2 VPN)", "Alcance Global", "Soporte 24/7"],
          benefits: ["Rendimiento Mejorado", "Eficiencia de Costos", "Soluciones Escalables", "Seguridad Mejorada"],
          technologies: ["MPLS", "SD-WAN", "VPL", "BGP", "QoS"]
        }
      },
      {
        identifier: "network-national-connectivity",
        slug: "national-connectivity",
        en: {
          title: "National Connectivity",
          description: "Comprehensive network solutions across Equatorial Guinea (Bata, Malabo & Mongomo) with 24/7 support.",
          fullDescription: `<h2>National Connectivity Solutions</h2>
          <p>Our national connectivity solutions provide comprehensive network infrastructure across Equatorial Guinea, connecting businesses in Bata, Malabo, and Mongomo with reliable, high-speed connectivity.</p>

          <h3>Coverage Areas</h3>
          <ul>
            <li><strong>Bata:</strong> Complete fiber optic coverage with redundant connections</li>
            <li><strong>Malabo:</strong> High-speed internet and private network solutions</li>
            <li><strong>Mongomo:</strong> Dedicated connectivity for government and enterprise clients</li>
          </ul>

          <h3>Infrastructure</h3>
          <p>Our national network infrastructure includes fiber optic backbone, microwave links, and satellite connectivity to ensure 99.9% uptime across all locations.</p>`,
          icon: "Network",
          features: ["Nationwide Coverage", "Fiber Optic Backbone", "99.9% Uptime", "24/7 Support", "Redundant Connections"],
          benefits: ["Reliable Connectivity", "Local Support", "Cost-Effective", "Scalable Bandwidth"],
          technologies: ["Fiber Optic", "Microwave", "Satellite", "Ethernet", "IP/MPLS"]
        },
        es: {
          title: "Conectividad Nacional",
          description: "Soluciones integrales de red en Guinea Ecuatorial (Bata, Malabo y Mongomo) con soporte 24/7.",
          fullDescription: `<h2>Soluciones de Conectividad Nacional</h2>
          <p>Nuestras soluciones de conectividad nacional proporcionan infraestructura de red integral en Guinea Ecuatorial, conectando empresas en Bata, Malabo y Mongomo con conectividad confiable y de alta velocidad.</p>

          <h3>Áreas de Cobertura</h3>
          <ul>
            <li><strong>Bata:</strong> Cobertura completa de fibra óptica con conexiones redundantes</li>
            <li><strong>Malabo:</strong> Internet de alta velocidad y soluciones de red privada</li>
            <li><strong>Mongomo:</strong> Conectividad dedicada para clientes gubernamentales y empresariales</li>
          </ul>

          <h3>Infraestructura</h3>
          <p>Nuestra infraestructura de red nacional incluye backbone de fibra óptica, enlaces de microondas y conectividad satelital para asegurar 99.9% de tiempo de actividad en todas las ubicaciones.</p>`,
          icon: "Network",
          features: ["Cobertura Nacional", "Backbone de Fibra Óptica", "99.9% Tiempo Activo", "Soporte 24/7", "Conexiones Redundantes"],
          benefits: ["Conectividad Confiable", "Soporte Local", "Rentable", "Ancho de Banda Escalable"],
          technologies: ["Fibra Óptica", "Microondas", "Satélite", "Ethernet", "IP/MPLS"]
        }
      },
      {
        identifier: "network-branch-office",
        slug: "branch-office",
        en: {
          title: "Branch Office Connectivity",
          description: "Enterprise solutions to connect multiple office locations with high-speed, secure, and reliable connectivity using SD-WAN, MPLS, and cloud-based networking.",
          fullDescription: `<h2>Branch Office Connectivity Solutions</h2>
          <p>Branch Office Connectivity ensures that your enterprise stays connected, collaborative, and competitive, no matter where your teams are located.</p>

          <h3>Advanced Solutions</h3>
          <p>With advanced solutions like SD-WAN (Software-Defined Wide Area Network), MPLS (Multiprotocol Label Switching), and cloud-based networking, enterprises can achieve:</p>

          <ul>
            <li><strong>High-Speed Connectivity:</strong> Reliable and fast data transfer between branches</li>
            <li><strong>Centralized Management:</strong> Simplified control and monitoring of network performance</li>
            <li><strong>Enhanced Security:</strong> Robust encryption and secure access to protect sensitive data</li>
            <li><strong>Cost Efficiency:</strong> Optimized network usage to reduce operational expenses</li>
            <li><strong>Scalability:</strong> Flexible solutions that grow with your business needs</li>
          </ul>

          <p>Whether you're managing a few branches or a global network, investing in the right connectivity solution ensures smooth communication, improved productivity, and a unified enterprise ecosystem.</p>`,
          icon: "Building2",
          features: ["SD-WAN Technology", "MPLS Networks", "Cloud-based Networking", "Centralized Management", "High-Speed Data Transfer"],
          benefits: ["Improved Productivity", "Cost Efficiency", "Enhanced Security", "Scalable Solutions", "Unified Ecosystem"],
          technologies: ["SD-WAN", "MPLS", "Cloud Networking", "VPN", "Load Balancing"]
        },
        es: {
          title: "Conectividad de Sucursales",
          description: "Soluciones empresariales para conectar múltiples ubicaciones de oficinas con conectividad de alta velocidad, segura y confiable usando SD-WAN, MPLS y redes basadas en la nube.",
          fullDescription: `<h2>Soluciones de Conectividad de Sucursales</h2>
          <p>La Conectividad de Sucursales asegura que su empresa se mantenga conectada, colaborativa y competitiva, sin importar dónde se encuentren sus equipos.</p>

          <h3>Soluciones Avanzadas</h3>
          <p>Con soluciones avanzadas como SD-WAN (Red de Área Amplia Definida por Software), MPLS (Conmutación de Etiquetas Multiprotocolo) y redes basadas en la nube, las empresas pueden lograr:</p>

          <ul>
            <li><strong>Conectividad de Alta Velocidad:</strong> Transferencia de datos confiable y rápida entre sucursales</li>
            <li><strong>Gestión Centralizada:</strong> Control simplificado y monitoreo del rendimiento de la red</li>
            <li><strong>Seguridad Mejorada:</strong> Cifrado robusto y acceso seguro para proteger datos sensibles</li>
            <li><strong>Eficiencia de Costos:</strong> Uso optimizado de la red para reducir gastos operativos</li>
            <li><strong>Escalabilidad:</strong> Soluciones flexibles que crecen con las necesidades de su negocio</li>
          </ul>

          <p>Ya sea que esté gestionando unas pocas sucursales o una red global, invertir en la solución de conectividad correcta asegura comunicación fluida, productividad mejorada y un ecosistema empresarial unificado.</p>`,
          icon: "Building2",
          features: ["Tecnología SD-WAN", "Redes MPLS", "Redes Basadas en la Nube", "Gestión Centralizada", "Transferencia de Datos de Alta Velocidad"],
          benefits: ["Productividad Mejorada", "Eficiencia de Costos", "Seguridad Mejorada", "Soluciones Escalables", "Ecosistema Unificado"],
          technologies: ["SD-WAN", "MPLS", "Redes en la Nube", "VPN", "Balanceador de Carga"]
        }
      },
      {
        identifier: "network-managed-internet",
        slug: "managed-internet",
        en: {
          title: "Managed Enterprise Internet",
          description: "End-to-end internet infrastructure management with 24/7 monitoring, advanced security, expert support, and scalable solutions for modern businesses.",
          fullDescription: `<h2>Managed Enterprise Internet Services</h2>
          <p>In today's hyper-connected world, reliable and high-performance internet is the backbone of every successful enterprise. Managed Enterprise Internet Services offer a tailored solution to meet the complex connectivity needs of modern businesses, ensuring uninterrupted operations, enhanced security, and scalable growth.</p>

          <h3>What Are Managed Enterprise Internet Services?</h3>
          <p>Managed Enterprise Internet Services are comprehensive, end-to-end solutions designed to handle every aspect of your organization's internet infrastructure. Unlike traditional internet plans, these services go beyond basic connectivity. They include proactive monitoring, advanced security, expert support, and scalable solutions—all managed by a dedicated team of professionals.</p>

          <h3>Key Benefits</h3>
          <ul>
            <li><strong>Reliable, High-Speed Connectivity:</strong> Fast, consistent, and uninterrupted connectivity with dedicated bandwidth</li>
            <li><strong>Proactive Monitoring and Maintenance:</strong> 24/7 monitoring to detect and resolve issues before they impact operations</li>
            <li><strong>Enterprise-Grade Security:</strong> Advanced security features including firewalls, intrusion detection, and encryption</li>
            <li><strong>Scalability for Growing Businesses:</strong> Flexible solutions that grow with your business needs</li>
            <li><strong>Expert Support:</strong> Round-the-clock support from experienced professionals</li>
            <li><strong>Cost-Effective Solutions:</strong> Optimized performance and reduced operational costs</li>
          </ul>`,
          icon: "Wifi",
          features: ["24/7 Monitoring", "Dedicated Bandwidth", "Advanced Security", "Expert Support", "Proactive Maintenance"],
          benefits: ["Uninterrupted Operations", "Enhanced Security", "Scalable Growth", "Cost Efficiency", "Professional Management"],
          technologies: ["Fiber Optic", "Load Balancing", "Firewall", "IDS/IPS", "QoS"]
        },
        es: {
          title: "Internet Empresarial Gestionado",
          description: "Gestión integral de infraestructura de internet con monitoreo 24/7, seguridad avanzada, soporte experto y soluciones escalables para empresas modernas.",
          fullDescription: `<h2>Servicios de Internet Empresarial Gestionado</h2>
          <p>En el mundo hiperconectado de hoy, el internet confiable y de alto rendimiento es la columna vertebral de toda empresa exitosa. Los Servicios de Internet Empresarial Gestionado ofrecen una solución personalizada para satisfacer las complejas necesidades de conectividad de las empresas modernas, asegurando operaciones ininterrumpidas, seguridad mejorada y crecimiento escalable.</p>

          <h3>¿Qué son los Servicios de Internet Empresarial Gestionado?</h3>
          <p>Los Servicios de Internet Empresarial Gestionado son soluciones integrales de extremo a extremo diseñadas para manejar todos los aspectos de la infraestructura de internet de su organización. A diferencia de los planes de internet tradicionales, estos servicios van más allá de la conectividad básica. Incluyen monitoreo proactivo, seguridad avanzada, soporte experto y soluciones escalables, todo gestionado por un equipo dedicado de profesionales.</p>

          <h3>Beneficios Clave</h3>
          <ul>
            <li><strong>Conectividad Confiable y de Alta Velocidad:</strong> Conectividad rápida, consistente e ininterrumpida con ancho de banda dedicado</li>
            <li><strong>Monitoreo y Mantenimiento Proactivo:</strong> Monitoreo 24/7 para detectar y resolver problemas antes de que impacten las operaciones</li>
            <li><strong>Seguridad de Nivel Empresarial:</strong> Características de seguridad avanzadas incluyendo firewalls, detección de intrusiones y cifrado</li>
            <li><strong>Escalabilidad para Empresas en Crecimiento:</strong> Soluciones flexibles que crecen con las necesidades de su negocio</li>
            <li><strong>Soporte Experto:</strong> Soporte las 24 horas de profesionales experimentados</li>
            <li><strong>Soluciones Rentables:</strong> Rendimiento optimizado y costos operativos reducidos</li>
          </ul>`,
          icon: "Wifi",
          features: ["Monitoreo 24/7", "Ancho de Banda Dedicado", "Seguridad Avanzada", "Soporte Experto", "Mantenimiento Proactivo"],
          benefits: ["Operaciones Ininterrumpidas", "Seguridad Mejorada", "Crecimiento Escalable", "Eficiencia de Costos", "Gestión Profesional"],
          technologies: ["Fibra Óptica", "Balanceador de Carga", "Firewall", "IDS/IPS", "QoS"]
        }
      }
    ];

    for (let i = 0; i < networkSolutions.length; i++) {
      const solution = networkSolutions[i];

      // Create English version
      const enSolution = await ctx.db.insert("content", {
        identifier: solution.identifier,
        language: "en",
        contentTypeId: networkSolutionType._id,
        status: "published" as const,
        data: { ...solution.en, slug: solution.slug, order: i },
        version: 1,
        createdBy: "system",
        createdAt: now,
        updatedAt: now,
      });

      // Create Spanish version
      const esSolution = await ctx.db.insert("content", {
        identifier: solution.identifier,
        language: "es",
        contentTypeId: networkSolutionType._id,
        status: "published" as const,
        data: { ...solution.es, slug: solution.slug, order: i },
        version: 1,
        createdBy: "system",
        createdAt: now,
        updatedAt: now,
      });

      results.push({ identifier: solution.identifier, enSolution, esSolution });
    }

    return results;
  },
});

// Initialize training programs content
export const initializeTrainingPrograms = mutation({
  args: {},
  handler: async (ctx) => {
    // Get training_program content type
    const trainingProgramType = await ctx.db
      .query("contentTypes")
      .withIndex("by_name", (q) => q.eq("name", "training_program"))
      .first();

    if (!trainingProgramType) {
      throw new Error("training_program content type not found. Please initialize content types first.");
    }

    const now = Date.now();
    const results = [];

    // Define training programs from content.md
    const trainingPrograms = [
      {
        identifier: "training-corporate-pc-skills",
        slug: "corporate-pc-skills",
        en: {
          title: "Corporate PC Skills Development",
          description: "Comprehensive training programs designed to empower your team with the knowledge and expertise they need to excel in the workplace.",
          fullDescription: `<h2>Corporate PC Skills Development</h2>
          <p>At OfficeTech, we understand that in today's fast-paced business environment, having strong computer skills is essential for success. That's why we offer comprehensive training programs designed to empower your team with the knowledge and expertise they need to excel in the workplace.</p>

          <h3>Training Programs Include:</h3>
          <ul>
            <li><strong>Microsoft Office Suite:</strong> Word, Excel, PowerPoint, Outlook, and Teams</li>
            <li><strong>Windows Operating Systems:</strong> Navigation, file management, and troubleshooting</li>
            <li><strong>Cybersecurity Awareness:</strong> Best practices for online safety and data protection</li>
            <li><strong>Network Fundamentals:</strong> Basic networking concepts and troubleshooting</li>
            <li><strong>Custom Training:</strong> Tailored programs for specific business needs</li>
          </ul>

          <p>Whether you're looking to enhance productivity, streamline operations, or simply stay ahead of the curve, our tailored training solutions are here to help.</p>`,
          icon: "Monitor",
          duration: "2-4 weeks",
          participants: "15-20",
          level: "All Levels",
          modules: [
            "Microsoft Office Suite (Word, Excel, PowerPoint, Outlook, Teams)",
            "Windows Operating Systems navigation and troubleshooting",
            "Cybersecurity Awareness and best practices",
            "Network Fundamentals and basic troubleshooting",
            "Custom Training tailored to business needs"
          ],
          benefits: ["Improved Productivity", "Enhanced Skills", "Better Security Awareness", "Competitive Advantage"],
          targetAudience: ["Business professionals", "Office workers", "New employees", "Teams needing upskilling"],
          deliveryOptions: ["On-site Training", "Virtual Training", "Hybrid Learning", "Self-paced Modules"]
        },
        es: {
          title: "Desarrollo de Habilidades Corporativas en PC",
          description: "Programas de capacitación integrales diseñados para empoderar a su equipo con el conocimiento y la experiencia que necesitan para sobresalir en el lugar de trabajo.",
          fullDescription: `<h2>Desarrollo de Habilidades Corporativas en PC</h2>
          <p>En OfficeTech, entendemos que en el entorno empresarial acelerado de hoy, tener habilidades sólidas en computación es esencial para el éxito. Por eso ofrecemos programas de capacitación integrales diseñados para empoderar a su equipo con el conocimiento y la experiencia que necesitan para sobresalir en el lugar de trabajo.</p>

          <h3>Los Programas de Capacitación Incluyen:</h3>
          <ul>
            <li><strong>Suite de Microsoft Office:</strong> Word, Excel, PowerPoint, Outlook y Teams</li>
            <li><strong>Sistemas Operativos Windows:</strong> Navegación, gestión de archivos y solución de problemas</li>
            <li><strong>Conciencia de Ciberseguridad:</strong> Mejores prácticas para seguridad en línea y protección de datos</li>
            <li><strong>Fundamentos de Redes:</strong> Conceptos básicos de redes y solución de problemas</li>
            <li><strong>Capacitación Personalizada:</strong> Programas adaptados para necesidades empresariales específicas</li>
          </ul>

          <p>Ya sea que busque mejorar la productividad, optimizar operaciones o simplemente mantenerse a la vanguardia, nuestras soluciones de capacitación personalizadas están aquí para ayudar.</p>`,
          icon: "Monitor",
          duration: "2-4 semanas",
          participants: "15-20",
          level: "Todos los Niveles",
          modules: [
            "Suite de Microsoft Office (Word, Excel, PowerPoint, Outlook, Teams)",
            "Navegación y solución de problemas de sistemas operativos Windows",
            "Conciencia de ciberseguridad y mejores prácticas",
            "Fundamentos de redes y solución básica de problemas",
            "Capacitación personalizada adaptada a las necesidades empresariales"
          ],
          benefits: ["Productividad Mejorada", "Habilidades Mejoradas", "Mejor Conciencia de Seguridad", "Ventaja Competitiva"],
          targetAudience: ["Profesionales de negocios", "Trabajadores de oficina", "Nuevos empleados", "Equipos que necesitan actualización"],
          deliveryOptions: ["Capacitación en Sitio", "Capacitación Virtual", "Aprendizaje Híbrido", "Módulos de Ritmo Propio"]
        }
      },
      {
        identifier: "training-leadership-development",
        slug: "leadership-training",
        en: {
          title: "Leadership Development Program",
          description: "Essential interpersonal, emotional, and strategic skills needed to lead effectively, inspire others, and drive organizational success.",
          fullDescription: `<h2>Leadership Development Program</h2>
          <p>Our Soft Skills Training: Leadership Program is designed to empower your team with the essential interpersonal, emotional, and strategic skills needed to lead effectively, inspire others, and drive organizational success.</p>

          <h3>Why Leadership Soft Skills Matter</h3>
          <p>Leadership is not just about making decisions or managing tasks; it's about inspiring trust, fostering collaboration, and creating a vision that motivates others to follow. Soft skills like communication, empathy, adaptability, and problem-solving are the building blocks of effective leadership.</p>

          <h3>Key Training Modules</h3>
          <ul>
            <li>Self-Awareness and Emotional Intelligence</li>
            <li>Effective Communication</li>
            <li>Building Trust and Influence</li>
            <li>Decision-Making and Problem-Solving</li>
            <li>Team Collaboration and Empowerment</li>
            <li>Adaptability and Change Management</li>
            <li>Visionary Leadership</li>
          </ul>`,
          icon: "Target",
          duration: "3-5 days",
          participants: "10-15",
          level: "Management",
          modules: [
            "Self-Awareness and Emotional Intelligence",
            "Effective Communication and Active Listening",
            "Building Trust and Influence",
            "Decision-Making and Problem-Solving",
            "Team Collaboration and Empowerment",
            "Adaptability and Change Management",
            "Visionary Leadership"
          ],
          benefits: ["Stronger Leaders", "Improved Team Dynamics", "Higher Employee Engagement", "Better Decision-Making", "Organizational Growth"],
          targetAudience: ["Emerging leaders", "Current managers", "Executives", "Teams at all levels"],
          deliveryOptions: ["Workshops", "Virtual Training", "One-on-one Coaching", "Blended Learning"]
        },
        es: {
          title: "Programa de Desarrollo de Liderazgo",
          description: "Habilidades interpersonales, emocionales y estratégicas esenciales necesarias para liderar efectivamente, inspirar a otros y impulsar el éxito organizacional.",
          fullDescription: `<h2>Programa de Desarrollo de Liderazgo</h2>
          <p>Nuestro Programa de Capacitación en Habilidades Blandas: Liderazgo está diseñado para empoderar a su equipo con las habilidades interpersonales, emocionales y estratégicas esenciales necesarias para liderar efectivamente, inspirar a otros e impulsar el éxito organizacional.</p>

          <h3>Por Qué Importan las Habilidades Blandas de Liderazgo</h3>
          <p>El liderazgo no se trata solo de tomar decisiones o gestionar tareas; se trata de inspirar confianza, fomentar la colaboración y crear una visión que motive a otros a seguir. Las habilidades blandas como comunicación, empatía, adaptabilidad y resolución de problemas son los bloques de construcción del liderazgo efectivo.</p>

          <h3>Módulos Clave de Capacitación</h3>
          <ul>
            <li>Autoconciencia e Inteligencia Emocional</li>
            <li>Comunicación Efectiva</li>
            <li>Construir Confianza e Influencia</li>
            <li>Toma de Decisiones y Resolución de Problemas</li>
            <li>Colaboración en Equipo y Empoderamiento</li>
            <li>Adaptabilidad y Gestión del Cambio</li>
            <li>Liderazgo Visionario</li>
          </ul>`,
          icon: "Target",
          duration: "3-5 días",
          participants: "10-15",
          level: "Gerencial",
          modules: [
            "Autoconciencia e Inteligencia Emocional",
            "Comunicación Efectiva y Escucha Activa",
            "Construir Confianza e Influencia",
            "Toma de Decisiones y Resolución de Problemas",
            "Colaboración en Equipo y Empoderamiento",
            "Adaptabilidad y Gestión del Cambio",
            "Liderazgo Visionario"
          ],
          benefits: ["Líderes Más Fuertes", "Dinámicas de Equipo Mejoradas", "Mayor Compromiso de Empleados", "Mejor Toma de Decisiones", "Crecimiento Organizacional"],
          targetAudience: ["Líderes emergentes", "Gerentes actuales", "Ejecutivos", "Equipos en todos los niveles"],
          deliveryOptions: ["Talleres", "Capacitación Virtual", "Coaching Uno a Uno", "Aprendizaje Combinado"]
        }
      },
      {
        identifier: "training-time-management",
        slug: "time-management",
        en: {
          title: "Time Management Program",
          description: "Help individuals and teams take control of their schedules, prioritize effectively, and achieve more with less stress.",
          fullDescription: `<h2>Time Management Program</h2>
          <p>Our Time Management Program is designed to help individuals and teams take control of their schedules, prioritize effectively, and achieve more with less stress. In today's fast-paced world, mastering time management is essential for both personal and professional success.</p>

          <h3>Why Time Management Matters</h3>
          <p>Time management is more than just checking off tasks—it's about working smarter, not harder. Poor time management can lead to missed deadlines, burnout, and decreased productivity. On the other hand, mastering this skill can help you accomplish more in less time, reduce stress and improve focus, create space for creativity and strategic thinking, and achieve a healthier work-life balance.</p>

          <h3>Key Training Modules</h3>
          <ul>
            <li>Understanding Time Management principles</li>
            <li>Goal Setting and Prioritization (SMART goals, Eisenhower Matrix)</li>
            <li>Planning and Scheduling techniques</li>
            <li>Overcoming Procrastination</li>
            <li>Managing Distractions and focus techniques</li>
            <li>Delegation and Collaboration</li>
            <li>Stress Management and Work-Life Balance</li>
          </ul>`,
          icon: "Clock",
          duration: "2-3 days",
          participants: "15-25",
          level: "All Levels",
          modules: [
            "Understanding Time Management principles",
            "Goal Setting and Prioritization (SMART goals, Eisenhower Matrix)",
            "Planning and Scheduling techniques",
            "Overcoming Procrastination",
            "Managing Distractions and focus techniques",
            "Delegation and Collaboration",
            "Stress Management and Work-Life Balance"
          ],
          benefits: ["Increased Productivity", "Better Work-Life Balance", "Reduced Stress", "Improved Focus", "Enhanced Goal Achievement"],
          targetAudience: ["Professionals at all levels", "Team leaders", "Entrepreneurs", "Anyone seeking better time management"],
          deliveryOptions: ["Interactive Workshops", "Virtual Training", "Self-paced Learning", "Group Coaching"]
        },
        es: {
          title: "Programa de Gestión del Tiempo",
          description: "Ayudar a individuos y equipos a tomar control de sus horarios, priorizar efectivamente y lograr más con menos estrés.",
          fullDescription: `<h2>Programa de Gestión del Tiempo</h2>
          <p>Nuestro Programa de Gestión del Tiempo está diseñado para ayudar a individuos y equipos a tomar control de sus horarios, priorizar efectivamente y lograr más con menos estrés. En el mundo acelerado de hoy, dominar la gestión del tiempo es esencial tanto para el éxito personal como profesional.</p>

          <h3>Por Qué Importa la Gestión del Tiempo</h3>
          <p>La gestión del tiempo es más que solo marcar tareas—se trata de trabajar de manera más inteligente, no más dura. La mala gestión del tiempo puede llevar a fechas límite perdidas, agotamiento y productividad disminuida. Por otro lado, dominar esta habilidad puede ayudarte a lograr más en menos tiempo, reducir el estrés y mejorar el enfoque, crear espacio para la creatividad y el pensamiento estratégico, y lograr un mejor equilibrio trabajo-vida.</p>

          <h3>Módulos Clave de Capacitación</h3>
          <ul>
            <li>Entender los principios de Gestión del Tiempo</li>
            <li>Establecimiento de Metas y Priorización (metas SMART, Matriz de Eisenhower)</li>
            <li>Técnicas de Planificación y Programación</li>
            <li>Superar la Procrastinación</li>
            <li>Gestionar Distracciones y técnicas de enfoque</li>
            <li>Delegación y Colaboración</li>
            <li>Gestión del Estrés y Equilibrio Trabajo-Vida</li>
          </ul>`,
          icon: "Clock",
          duration: "2-3 días",
          participants: "15-25",
          level: "Todos los Niveles",
          modules: [
            "Entender los principios de Gestión del Tiempo",
            "Establecimiento de Metas y Priorización (metas SMART, Matriz de Eisenhower)",
            "Técnicas de Planificación y Programación",
            "Superar la Procrastinación",
            "Gestionar Distracciones y técnicas de enfoque",
            "Delegación y Colaboración",
            "Gestión del Estrés y Equilibrio Trabajo-Vida"
          ],
          benefits: ["Productividad Aumentada", "Mejor Equilibrio Trabajo-Vida", "Estrés Reducido", "Enfoque Mejorado", "Logro de Metas Mejorado"],
          targetAudience: ["Profesionales en todos los niveles", "Líderes de equipo", "Emprendedores", "Cualquiera que busque mejor gestión del tiempo"],
          deliveryOptions: ["Talleres Interactivos", "Capacitación Virtual", "Aprendizaje a Ritmo Propio", "Coaching Grupal"]
        }
      },
      {
        identifier: "training-communication-skills",
        slug: "communication-skills",
        en: {
          title: "Communication Skills Program",
          description: "Master the art of communication, fostering stronger connections, reducing misunderstandings, and driving better outcomes.",
          fullDescription: `<h2>Communication Skills Program</h2>
          <p>Our Communication Skills Program is designed to help participants master the art of communication, fostering stronger connections, reducing misunderstandings, and driving better outcomes in both personal and professional settings.</p>

          <h3>Why Communication Skills Matter</h3>
          <p>Effective communication is the foundation of successful relationships, teamwork, and leadership. Whether you're presenting to a board, collaborating with colleagues, or resolving conflicts, strong communication skills are essential. Poor communication can lead to conflicts, missed opportunities, and inefficiencies, while strong communication skills can build trust and strengthen relationships, enhance teamwork and collaboration, improve problem-solving and decision-making, and boost confidence and professional credibility.</p>

          <h3>Key Training Modules</h3>
          <ul>
            <li>Foundations of Effective Communication</li>
            <li>Active Listening techniques</li>
            <li>Verbal and Non-Verbal Communication</li>
            <li>Written Communication skills</li>
            <li>Emotional Intelligence in Communication</li>
            <li>Conflict Resolution and Difficult Conversations</li>
            <li>Public Speaking and Presentation Skills</li>
          </ul>`,
          icon: "Users",
          duration: "2-4 days",
          participants: "12-20",
          level: "All Levels",
          modules: [
            "Foundations of Effective Communication",
            "Active Listening techniques",
            "Verbal and Non-Verbal Communication",
            "Written Communication skills",
            "Emotional Intelligence in Communication",
            "Conflict Resolution and Difficult Conversations",
            "Public Speaking and Presentation Skills"
          ],
          benefits: ["Improved Relationships", "Better Team Collaboration", "Enhanced Leadership Skills", "Increased Confidence", "Reduced Conflicts"],
          targetAudience: ["Business professionals", "Team leaders", "Customer service representatives", "Anyone wanting to improve communication"],
          deliveryOptions: ["Interactive Workshops", "Role-playing Exercises", "Virtual Training", "One-on-one Coaching"]
        },
        es: {
          title: "Programa de Habilidades de Comunicación",
          description: "Dominar el arte de la comunicación, fomentando conexiones más fuertes, reduciendo malentendidos y impulsando mejores resultados.",
          fullDescription: `<h2>Programa de Habilidades de Comunicación</h2>
          <p>Nuestro Programa de Habilidades de Comunicación está diseñado para ayudar a los participantes a dominar el arte de la comunicación, fomentando conexiones más fuertes, reduciendo malentendidos e impulsando mejores resultados tanto en entornos personales como profesionales.</p>

          <h3>Por Qué Importan las Habilidades de Comunicación</h3>
          <p>La comunicación efectiva es la base de relaciones exitosas, trabajo en equipo y liderazgo. Ya sea que estés presentando a una junta, colaborando con colegas o resolviendo conflictos, las habilidades de comunicación fuertes son esenciales. La comunicación pobre puede llevar a conflictos, oportunidades perdidas e ineficiencias, mientras que las habilidades de comunicación fuertes pueden construir confianza y fortalecer relaciones, mejorar el trabajo en equipo y la colaboración, mejorar la resolución de problemas y la toma de decisiones, y aumentar la confianza y credibilidad profesional.</p>

          <h3>Módulos Clave de Capacitación</h3>
          <ul>
            <li>Fundamentos de Comunicación Efectiva</li>
            <li>Técnicas de Escucha Activa</li>
            <li>Comunicación Verbal y No Verbal</li>
            <li>Habilidades de Comunicación Escrita</li>
            <li>Inteligencia Emocional en la Comunicación</li>
            <li>Resolución de Conflictos y Conversaciones Difíciles</li>
            <li>Hablar en Público y Habilidades de Presentación</li>
          </ul>`,
          icon: "Users",
          duration: "2-4 días",
          participants: "12-20",
          level: "Todos los Niveles",
          modules: [
            "Fundamentos de Comunicación Efectiva",
            "Técnicas de Escucha Activa",
            "Comunicación Verbal y No Verbal",
            "Habilidades de Comunicación Escrita",
            "Inteligencia Emocional en la Comunicación",
            "Resolución de Conflictos y Conversaciones Difíciles",
            "Hablar en Público y Habilidades de Presentación"
          ],
          benefits: ["Relaciones Mejoradas", "Mejor Colaboración en Equipo", "Habilidades de Liderazgo Mejoradas", "Confianza Aumentada", "Conflictos Reducidos"],
          targetAudience: ["Profesionales de negocios", "Líderes de equipo", "Representantes de servicio al cliente", "Cualquiera que quiera mejorar la comunicación"],
          deliveryOptions: ["Talleres Interactivos", "Ejercicios de Juego de Roles", "Capacitación Virtual", "Coaching Uno a Uno"]
        }
      },
      {
        identifier: "training-oil-gas",
        slug: "oil-gas-training",
        en: {
          title: "Professional Oil & Gas Training",
          description: "Certified, practical, and industry-relevant training to meet global energy sector standards with internationally recognized certifications.",
          fullDescription: `<h2>Professional Oil & Gas Training Solutions</h2>
          <p>Whether you're an industry professional looking to upskill or a company investing in your workforce, we provide certified, practical, and industry-relevant training to meet global energy sector standards.</p>

          <h3>Why Choose Us?</h3>
          <ul>
            <li><strong>Industry-Expert Trainers:</strong> Learn from seasoned professionals with real-world experience</li>
            <li><strong>Hands-On & Virtual Learning:</strong> Flexible programs tailored to your needs—onsite, online, or hybrid</li>
            <li><strong>Internationally Recognized Certifications:</strong> Boost your credentials with accredited courses</li>
            <li><strong>Safety & Compliance Focus:</strong> Master critical protocols for a safer, more efficient workplace</li>
          </ul>

          <h3>Training Categories</h3>
          <ul>
            <li><strong>Offshore/Onshore Safety:</strong> OPITO, IWCF, IADC certified programs</li>
            <li><strong>Process Operations & Maintenance:</strong> Equipment operation and maintenance procedures</li>
            <li><strong>Pipeline & Refinery Operations:</strong> Specialized training for pipeline and refinery personnel</li>
            <li><strong>HSE (Health, Safety & Environment):</strong> Comprehensive safety and environmental training</li>
            <li><strong>Technical & Soft Skills Development:</strong> Both technical competencies and leadership skills</li>
          </ul>`,
          icon: "Award",
          duration: "Varies",
          participants: "8-15",
          level: "Professional",
          modules: [
            "Offshore/Onshore Safety (OPITO, IWCF, IADC)",
            "Process Operations & Maintenance",
            "Pipeline & Refinery Operations",
            "HSE (Health, Safety & Environment)",
            "Technical & Soft Skills Development"
          ],
          benefits: ["International Certifications", "Enhanced Career Prospects", "Industry Compliance", "Safety Excellence", "Technical Competency"],
          targetAudience: ["Oil & Gas professionals", "Safety officers", "Operations personnel", "Maintenance technicians"],
          deliveryOptions: ["On-site Training", "Simulation-based Learning", "Practical Workshops", "Certification Programs"]
        },
        es: {
          title: "Capacitación Profesional en Petróleo y Gas",
          description: "Capacitación certificada, práctica y relevante para la industria para cumplir con los estándares globales del sector energético con certificaciones reconocidas internacionalmente.",
          fullDescription: `<h2>Soluciones de Capacitación Profesional en Petróleo y Gas</h2>
          <p>Ya sea que seas un profesional de la industria que busca mejorar sus habilidades o una empresa que invierte en su fuerza laboral, proporcionamos capacitación certificada, práctica y relevante para la industria para cumplir con los estándares globales del sector energético.</p>

          <h3>¿Por Qué Elegirnos?</h3>
          <ul>
            <li><strong>Capacitadores Expertos de la Industria:</strong> Aprende de profesionales experimentados con experiencia del mundo real</li>
            <li><strong>Aprendizaje Práctico y Virtual:</strong> Programas flexibles adaptados a tus necesidades—en sitio, en línea o híbrido</li>
            <li><strong>Certificaciones Reconocidas Internacionalmente:</strong> Mejora tus credenciales con cursos acreditados</li>
            <li><strong>Enfoque en Seguridad y Cumplimiento:</strong> Domina protocolos críticos para un lugar de trabajo más seguro y eficiente</li>
          </ul>

          <h3>Categorías de Capacitación</h3>
          <ul>
            <li><strong>Seguridad Marina/Terrestre:</strong> Programas certificados OPITO, IWCF, IADC</li>
            <li><strong>Operaciones de Proceso y Mantenimiento:</strong> Procedimientos de operación y mantenimiento de equipos</li>
            <li><strong>Operaciones de Tuberías y Refinerías:</strong> Capacitación especializada para personal de tuberías y refinerías</li>
            <li><strong>HSE (Salud, Seguridad y Medio Ambiente):</strong> Capacitación integral de seguridad y ambiental</li>
            <li><strong>Desarrollo de Habilidades Técnicas y Blandas:</strong> Tanto competencias técnicas como habilidades de liderazgo</li>
          </ul>`,
          icon: "Award",
          duration: "Varía",
          participants: "8-15",
          level: "Profesional",
          modules: [
            "Seguridad Marina/Terrestre (OPITO, IWCF, IADC)",
            "Operaciones de Proceso y Mantenimiento",
            "Operaciones de Tuberías y Refinerías",
            "HSE (Salud, Seguridad y Medio Ambiente)",
            "Desarrollo de Habilidades Técnicas y Blandas"
          ],
          benefits: ["Certificaciones Internacionales", "Perspectivas de Carrera Mejoradas", "Cumplimiento de la Industria", "Excelencia en Seguridad", "Competencia Técnica"],
          targetAudience: ["Profesionales de Petróleo y Gas", "Oficiales de seguridad", "Personal de operaciones", "Técnicos de mantenimiento"],
          deliveryOptions: ["Capacitación en Sitio", "Aprendizaje Basado en Simulación", "Talleres Prácticos", "Programas de Certificación"]
        }
      }
    ];

    for (let i = 0; i < trainingPrograms.length; i++) {
      const program = trainingPrograms[i];

      // Create English version
      const enProgram = await ctx.db.insert("content", {
        identifier: program.identifier,
        language: "en",
        contentTypeId: trainingProgramType._id,
        status: "published" as const,
        data: { ...program.en, slug: program.slug, order: i },
        version: 1,
        createdBy: "system",
        createdAt: now,
        updatedAt: now,
      });

      // Create Spanish version
      const esProgram = await ctx.db.insert("content", {
        identifier: program.identifier,
        language: "es",
        contentTypeId: trainingProgramType._id,
        status: "published" as const,
        data: { ...program.es, slug: program.slug, order: i },
        version: 1,
        createdBy: "system",
        createdAt: now,
        updatedAt: now,
      });

      results.push({ identifier: program.identifier, enProgram, esProgram });
    }

    return results;
  },
});

// Initialize footer content
export const initializeFooterContent = mutation({
  args: {},
  handler: async (ctx) => {
    // Get footer_content content type
    const footerContentType = await ctx.db
      .query("contentTypes")
      .withIndex("by_name", (q) => q.eq("name", "footer_content"))
      .first();

    if (!footerContentType) {
      throw new Error("footer_content content type not found. Please initialize content types first.");
    }

    const now = Date.now();
    const results = [];

    // Default footer content for both languages
    const footerContentData = [
      {
        identifier: "footer-content",
        language: "en",
        data: {
          companyName: "OfficeTech Guinea",
          companyDescription: "Leading technology and security solutions in Equatorial Guinea. Protecting and connecting your business.",
          ctaButtonText: "Get Started",
          ctaButtonLink: "/contact",
          quickLinksTitle: "Quick Links",
          contactTitle: "Contact",
          address: "Malabo, Equatorial Guinea",
          phone: "+240 XXX XXX XXX",
          email: "<EMAIL>",
          copyrightText: "© 2024 OfficeTech Guinea. All rights reserved.",
          socialLinks: []
        }
      },
      {
        identifier: "footer-content",
        language: "es",
        data: {
          companyName: "OfficeTech Guinea",
          companyDescription: "Líderes en soluciones tecnológicas y de seguridad en Guinea Ecuatorial. Protegiendo y conectando tu negocio.",
          ctaButtonText: "Comenzar",
          ctaButtonLink: "/contact",
          quickLinksTitle: "Enlaces",
          contactTitle: "Contacto",
          address: "Malabo, Guinea Ecuatorial",
          phone: "+240 XXX XXX XXX",
          email: "<EMAIL>",
          copyrightText: "© 2024 OfficeTech Guinea. Todos los derechos reservados.",
          socialLinks: []
        }
      }
    ];

    for (const footerData of footerContentData) {
      // Check if content already exists
      const existing = await ctx.db
        .query("content")
        .withIndex("by_identifier_language", (q) =>
          q.eq("identifier", footerData.identifier).eq("language", footerData.language)
        )
        .first();

      if (!existing) {
        const contentId = await ctx.db.insert("content", {
          identifier: footerData.identifier,
          language: footerData.language,
          data: footerData.data,
          contentTypeId: footerContentType._id,
          status: "published",
          version: 1,
          createdBy: "system",
          createdAt: now,
          updatedAt: now,
        });

        // Create history entry
        await ctx.db.insert("contentHistory", {
          contentId,
          data: footerData.data,
          version: 1,
          changedBy: "system",
          changeType: "created",
          changeNote: "Initial footer content creation",
          createdAt: now,
        });

        results.push({
          identifier: footerData.identifier,
          language: footerData.language,
          id: contentId,
          status: "created"
        });
      } else {
        results.push({
          identifier: footerData.identifier,
          language: footerData.language,
          id: existing._id,
          status: "exists"
        });
      }
    }

    return results;
  },
});

// Initialize all content from content.md
export const initializeAllContent = mutation({
  args: {},
  handler: async (ctx) => {
    const results = {
      contentTypes: null as any,
      heroSections: null as any,
      valueCards: null as any,
      timeline: null as any,
      serviceCards: null as any,
      networkSolutions: null as any,
      trainingPrograms: null as any,
      footerContent: null as any,
    };

    try {
      // 1. Initialize content types first
      console.log("Initializing content types...");
      results.contentTypes = await ctx.runMutation(api.contentTypes.initializeContentTypes, {});

      // 2. Initialize hero sections
      console.log("Initializing hero sections...");
      results.heroSections = await ctx.runMutation(api.content.initializeHeroSections, {});

      // 3. Initialize value cards
      console.log("Initializing value cards...");
      results.valueCards = await ctx.runMutation(api.content.initializeValueCards, {});

      // 4. Initialize timeline
      console.log("Initializing timeline...");
      results.timeline = await ctx.runMutation(api.content.initializeTimeline, {});

      // 5. Initialize service cards
      console.log("Initializing service cards...");
      results.serviceCards = await ctx.runMutation(api.content.initializeServiceCards, {});

      // 6. Initialize network solutions
      console.log("Initializing network solutions...");
      results.networkSolutions = await ctx.runMutation(api.content.initializeNetworkSolutions, {});

      // 7. Initialize training programs
      console.log("Initializing training programs...");
      results.trainingPrograms = await ctx.runMutation(api.content.initializeTrainingPrograms, {});

      // 8. Initialize footer content
      console.log("Initializing footer content...");
      results.footerContent = await ctx.runMutation(api.content.initializeFooterContent, {});

      console.log("All content initialization completed successfully!");
      return results;
    } catch (error) {
      console.error("Error during content initialization:", error);
      throw error;
    }
  },
});

// Debug query to see all content by type
export const debugContentByType = query({
  args: { contentTypeName: v.optional(v.string()) },
  handler: async (ctx, args) => {
    let query = ctx.db.query("content");

    if (args.contentTypeName) {
      // Get content type first
      const contentType = await ctx.db
        .query("contentTypes")
        .withIndex("by_name", (q) => q.eq("name", args.contentTypeName))
        .first();

      if (contentType) {
        query = query.filter((q) => q.eq(q.field("contentTypeId"), contentType._id));
      }
    }

    const allContent = await query.collect();

    return allContent.map(content => ({
      id: content._id,
      identifier: content.identifier,
      language: content.language,
      status: content.status,
      version: content.version,
      data: content.data,
      createdAt: content.createdAt,
      updatedAt: content.updatedAt
    }));
  },
});
