import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Content Management System
  contentTypes: defineTable({
    name: v.string(),
    label: v.string(),
    description: v.string(),
    icon: v.optional(v.string()),
    category: v.string(),
    fields: v.array(v.object({
      name: v.string(),
      label: v.string(),
      type: v.union(
        v.literal("text"),
        v.literal("richText"),
        v.literal("image"),
        v.literal("boolean"),
        v.literal("number"),
        v.literal("array"),
        v.literal("object"),
        v.literal("select"),
        v.literal("multiSelect"),
        v.literal("date"),
        v.literal("url"),
        v.literal("email"),
        v.literal("color")
      ),
      required: v.boolean(),
      description: v.optional(v.string()),
      placeholder: v.optional(v.string()),
      defaultValue: v.optional(v.any()),
      validation: v.optional(v.object({
        min: v.optional(v.number()),
        max: v.optional(v.number()),
        pattern: v.optional(v.string()),
        options: v.optional(v.array(v.string())),
      })),
    })),
    settings: v.optional(v.object({
      allowMultiple: v.boolean(),
      isSystem: v.boolean(),
      sortable: v.boolean(),
    })),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_name", ["name"])
    .index("by_category", ["category"]),

  content: defineTable({
    contentTypeId: v.id("contentTypes"),
    identifier: v.string(), // unique identifier for content (e.g., "hero-section", "about-us")
    language: v.string(), // "en" or "es"
    data: v.any(), // flexible content data
    status: v.union(v.literal("draft"), v.literal("published")),
    version: v.number(),
    createdBy: v.string(), // Clerk user ID
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_identifier", ["identifier"])
    .index("by_language", ["language"])
    .index("by_status", ["status"])
    .index("by_identifier_language", ["identifier", "language"]),

  contentHistory: defineTable({
    contentId: v.id("content"),
    data: v.any(),
    version: v.number(),
    changedBy: v.string(),
    changeNote: v.optional(v.string()),
    changeType: v.union(
      v.literal("created"),
      v.literal("updated"),
      v.literal("published"),
      v.literal("unpublished"),
      v.literal("reverted"),
      v.literal("deleted")
    ),
    previousVersion: v.optional(v.number()),
    metadata: v.optional(v.object({
      fieldsChanged: v.optional(v.array(v.string())),
      publishedAt: v.optional(v.number()),
      scheduledFor: v.optional(v.number()),
      approvedBy: v.optional(v.string()),
    })),
    createdAt: v.number(),
  })
    .index("by_content", ["contentId"])
    .index("by_version", ["contentId", "version"])
    .index("by_change_type", ["changeType"])
    .index("by_changed_by", ["changedBy"]),

  // Content drafts for better draft management
  contentDrafts: defineTable({
    contentId: v.id("content"),
    identifier: v.string(),
    language: v.string(),
    data: v.any(),
    contentTypeId: v.id("contentTypes"),
    version: v.number(),
    createdBy: v.string(),
    lastModifiedBy: v.string(),
    changeNote: v.optional(v.string()),
    scheduledPublishAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_content", ["contentId"])
    .index("by_identifier_language", ["identifier", "language"])
    .index("by_created_by", ["createdBy"])
    .index("by_scheduled", ["scheduledPublishAt"]),

  // User role change history
  userRoleHistory: defineTable({
    userId: v.id("users"),
    previousRole: v.string(),
    newRole: v.string(),
    changedBy: v.id("users"),
    reason: v.optional(v.string()),
    createdAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_changed_by", ["changedBy"]),

  // User status change history
  userStatusHistory: defineTable({
    userId: v.id("users"),
    previousStatus: v.boolean(),
    newStatus: v.boolean(),
    changedBy: v.id("users"),
    reason: v.optional(v.string()),
    createdAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_changed_by", ["changedBy"]),

  // Admin invitations
  adminInvitations: defineTable({
    email: v.string(),
    role: v.union(
      v.literal("content_editor"),
      v.literal("admin")
    ),
    message: v.optional(v.string()),
    invitedBy: v.id("users"),
    status: v.union(
      v.literal("pending"),
      v.literal("accepted"),
      v.literal("expired"),
      v.literal("revoked")
    ),
    createdAt: v.number(),
    expiresAt: v.number(),
    acceptedAt: v.optional(v.number()),
  })
    .index("by_email", ["email"])
    .index("by_status", ["status"])
    .index("by_invited_by", ["invitedBy"]),

  // Media Management
  media: defineTable({
    filename: v.string(),
    originalName: v.string(),
    mimeType: v.string(),
    size: v.number(),
    url: v.string(),
    alt: v.optional(v.string()),
    tags: v.array(v.string()),
    uploadedBy: v.string(),
    createdAt: v.number(),
  })
    .index("by_tags", ["tags"])
    .index("by_uploaded_by", ["uploadedBy"]),

  // User Management
  users: defineTable({
    clerkId: v.string(),
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    role: v.union(
      v.literal("super_admin"),
      v.literal("admin"),
      v.literal("content_editor"),
      v.literal("viewer")
    ),
    isActive: v.boolean(),
    lastLogin: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_clerk_id", ["clerkId"])
    .index("by_email", ["email"])
    .index("by_role", ["role"]),

  // Contact Forms and Leads
  contactForms: defineTable({
    name: v.string(),
    email: v.string(),
    phone: v.optional(v.string()),
    company: v.optional(v.string()),
    service: v.optional(v.string()),
    message: v.string(),
    source: v.optional(v.string()),
    status: v.union(v.literal("new"), v.literal("in_progress"), v.literal("resolved"), v.literal("closed")),
    notes: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
    updatedBy: v.optional(v.id("users")),
  })
    .index("by_status", ["status"])
    .index("by_email", ["email"])
    .index("by_created_at", ["createdAt"]),

  // Site Settings
  siteSettings: defineTable({
    key: v.string(),
    value: v.any(),
    description: v.optional(v.string()),
    updatedBy: v.string(),
    updatedAt: v.number(),
  })
    .index("by_key", ["key"]),

  // Analytics
  pageViews: defineTable({
    path: v.string(),
    title: v.optional(v.string()),
    referrer: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    sessionId: v.string(),
    timestamp: v.number(),
    date: v.string(), // YYYY-MM-DD format for easy querying
  })
    .index("by_timestamp", ["timestamp"])
    .index("by_date", ["date"])
    .index("by_path", ["path"])
    .index("by_session", ["sessionId"]),

  contentInteractions: defineTable({
    contentId: v.string(),
    contentType: v.string(),
    action: v.string(), // view, edit, publish, delete
    sessionId: v.string(),
    userId: v.optional(v.id("users")),
    metadata: v.optional(v.any()),
    timestamp: v.number(),
    date: v.string(),
  })
    .index("by_timestamp", ["timestamp"])
    .index("by_date", ["date"])
    .index("by_content", ["contentId"])
    .index("by_user", ["userId"])
    .index("by_action", ["action"]),

  formSubmissions: defineTable({
    formType: v.string(),
    formId: v.string(),
    success: v.boolean(),
    sessionId: v.string(),
    metadata: v.optional(v.any()),
    timestamp: v.number(),
    date: v.string(),
  })
    .index("by_timestamp", ["timestamp"])
    .index("by_date", ["date"])
    .index("by_form_type", ["formType"])
    .index("by_success", ["success"]),

});
