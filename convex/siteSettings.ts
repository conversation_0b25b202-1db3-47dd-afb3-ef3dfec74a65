import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Helper function to get current user and check permissions
async function getCurrentUser(ctx: any) {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Not authenticated");
  }

  const user = await ctx.db
    .query("users")
    .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
    .first();

  if (!user) {
    throw new Error("User not found");
  }

  if (!user.isActive) {
    throw new Error("User account is deactivated");
  }

  return user;
}

function hasSettingsPermission(user: any, action: "read" | "write"): boolean {
  const permissions: Record<string, string[]> = {
    viewer: ["settings:read"],
    content_editor: ["settings:read"],
    admin: ["settings:read", "settings:write"],
    super_admin: ["*"],
  };

  const userPermissions = permissions[user.role] || [];
  const requiredPermission = `settings:${action}`;
  return userPermissions.includes("*") || userPermissions.includes(requiredPermission);
}

// Get all site settings (admin only)
export const getAllSettings = query({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);

    if (!hasSettingsPermission(user, "read")) {
      throw new Error("Insufficient permissions to read settings");
    }

    const settings = await ctx.db.query("siteSettings").collect();

    // Convert to key-value object for easier use
    const settingsObject = settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {} as Record<string, any>);

    return {
      settings: settingsObject,
      lastUpdated: Math.max(...settings.map(s => s.updatedAt), 0),
    };
  },
});

// Get public site settings (no authentication required)
export const getPublicSettings = query({
  args: {},
  handler: async (ctx) => {
    const settings = await ctx.db.query("siteSettings").collect();

    // Define which settings are public (safe to expose to unauthenticated users)
    const publicSettingKeys = [
      // General public info
      "general.site_name",
      "general.site_description",
      "general.site_url",
      "general.admin_email",
      "general.support_email",
      "general.phone",
      "general.address",
      "general.language",
      "general.currency",
      "general.timezone",

      // SEO settings (most are public)
      "seo.meta_title",
      "seo.meta_description",
      "seo.meta_keywords",
      "seo.og_image",
      "seo.twitter_handle",
      "seo.google_analytics_id",
      "seo.google_tag_manager_id",
      "seo.facebook_pixel_id",

      // Social media links and settings
      "social.facebook_url",
      "social.twitter_url",
      "social.linkedin_url",
      "social.instagram_url",
      "social.youtube_url",
      "social.show_social_header",
      "social.show_social_footer",
      "social.show_social_contact",
      "social.social_open_new_tab",

      // Contact settings
      "contact.show_phone",
      "contact.show_email",
      "contact.show_address",
      "contact.business_hours",
      "contact.emergency_support",
      "contact.contact_form_enabled",
      "contact.contact_form_recipient",
      "contact.contact_form_subject",

      // Public feature flags
      "features.maintenance_mode",
      "features.registration_enabled",
      "features.comments_enabled",
      "features.analytics_enabled",
      "features.newsletter_enabled",
      "features.contact_form_enabled",
      "features.search_enabled",
      "features.user_profiles_public",
      "features.api_access_enabled",
      "features.debug_mode",

      // Performance settings that affect frontend
      "performance.cache_duration",
      "performance.image_optimization",
      "performance.lazy_loading",
      "performance.compression_enabled",
      "performance.compression_level",
      "performance.image_quality",
      "performance.max_image_width",
      "performance.enable_caching",
      "performance.cache_api_responses",
      "performance.webp_conversion",
      "performance.preload_critical_resources",
      "performance.minify_css",
      "performance.minify_js",

      // Branding settings
      "branding.primary_color",
      "branding.secondary_color",
      "branding.accent_color",
      "branding.logo_url",
      "branding.favicon_url",
      "branding.font_family",
    ];

    // Filter to only include public settings
    const publicSettings = settings.filter(setting =>
      publicSettingKeys.includes(setting.key)
    );

    // Convert to key-value object for easier use
    const settingsObject = publicSettings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {} as Record<string, any>);

    return {
      settings: settingsObject,
      lastUpdated: Math.max(...publicSettings.map(s => s.updatedAt), 0),
    };
  },
});

// Get specific setting by key (admin only)
export const getSetting = query({
  args: { key: v.string() },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!hasSettingsPermission(user, "read")) {
      throw new Error("Insufficient permissions to read settings");
    }

    const setting = await ctx.db
      .query("siteSettings")
      .withIndex("by_key", (q) => q.eq("key", args.key))
      .first();

    return setting?.value || null;
  },
});

// Get specific public setting by key (no authentication required)
export const getPublicSetting = query({
  args: { key: v.string() },
  handler: async (ctx, args) => {
    // Define which settings are public
    const publicSettingKeys = [
      // General public info
      "general.site_name", "general.site_description", "general.site_url",
      "general.admin_email", "general.support_email", "general.phone", "general.address",
      "general.language", "general.currency", "general.timezone",
      // SEO settings
      "seo.meta_title", "seo.meta_description", "seo.meta_keywords", "seo.og_image", "seo.twitter_handle",
      "seo.google_analytics_id", "seo.google_tag_manager_id", "seo.facebook_pixel_id",
      // Social media
      "social.facebook_url", "social.twitter_url", "social.linkedin_url", "social.instagram_url", "social.youtube_url",
      "social.show_social_header", "social.show_social_footer", "social.show_social_contact", "social.social_open_new_tab",
      // Contact settings
      "contact.show_phone", "contact.show_email", "contact.show_address", "contact.business_hours", "contact.emergency_support",
      "contact.contact_form_enabled", "contact.contact_form_recipient", "contact.contact_form_subject",
      // Feature flags
      "features.maintenance_mode", "features.registration_enabled", "features.comments_enabled", "features.analytics_enabled",
      "features.newsletter_enabled", "features.contact_form_enabled", "features.search_enabled", "features.user_profiles_public",
      "features.api_access_enabled", "features.debug_mode",
      // Performance settings
      "performance.cache_duration", "performance.image_optimization", "performance.lazy_loading", "performance.compression_enabled",
      "performance.compression_level", "performance.image_quality", "performance.max_image_width", "performance.enable_caching",
      "performance.cache_api_responses", "performance.webp_conversion", "performance.preload_critical_resources",
      "performance.minify_css", "performance.minify_js",
      // Branding
      "branding.primary_color", "branding.secondary_color", "branding.accent_color", "branding.logo_url",
      "branding.favicon_url", "branding.font_family",
    ];

    // Check if the requested key is public
    if (!publicSettingKeys.includes(args.key)) {
      throw new Error("Setting is not publicly accessible");
    }

    const setting = await ctx.db
      .query("siteSettings")
      .withIndex("by_key", (q) => q.eq("key", args.key))
      .first();

    return setting?.value || null;
  },
});

// Update or create a setting
export const updateSetting = mutation({
  args: {
    key: v.string(),
    value: v.any(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    
    if (!hasSettingsPermission(user, "write")) {
      throw new Error("Insufficient permissions to update settings");
    }

    const existingSetting = await ctx.db
      .query("siteSettings")
      .withIndex("by_key", (q) => q.eq("key", args.key))
      .first();

    const now = Date.now();

    if (existingSetting) {
      await ctx.db.patch(existingSetting._id, {
        value: args.value,
        description: args.description || existingSetting.description,
        updatedBy: user.clerkId,
        updatedAt: now,
      });
    } else {
      await ctx.db.insert("siteSettings", {
        key: args.key,
        value: args.value,
        description: args.description,
        updatedBy: user.clerkId,
        updatedAt: now,
      });
    }

    return { success: true };
  },
});

// Update multiple settings at once
export const updateMultipleSettings = mutation({
  args: {
    settings: v.array(v.object({
      key: v.string(),
      value: v.any(),
      description: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    
    if (!hasSettingsPermission(user, "write")) {
      throw new Error("Insufficient permissions to update settings");
    }

    const now = Date.now();

    for (const setting of args.settings) {
      const existingSetting = await ctx.db
        .query("siteSettings")
        .withIndex("by_key", (q) => q.eq("key", setting.key))
        .first();

      if (existingSetting) {
        await ctx.db.patch(existingSetting._id, {
          value: setting.value,
          description: setting.description || existingSetting.description,
          updatedBy: user.clerkId,
          updatedAt: now,
        });
      } else {
        await ctx.db.insert("siteSettings", {
          key: setting.key,
          value: setting.value,
          description: setting.description,
          updatedBy: user.clerkId,
          updatedAt: now,
        });
      }
    }

    return { success: true };
  },
});

// Delete a setting
export const deleteSetting = mutation({
  args: { key: v.string() },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    
    if (!hasSettingsPermission(user, "write")) {
      throw new Error("Insufficient permissions to delete settings");
    }

    const setting = await ctx.db
      .query("siteSettings")
      .withIndex("by_key", (q) => q.eq("key", args.key))
      .first();

    if (setting) {
      await ctx.db.delete(setting._id);
    }

    return { success: true };
  },
});

// Get settings by category (admin only)
export const getSettingsByCategory = query({
  args: { category: v.string() },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!hasSettingsPermission(user, "read")) {
      throw new Error("Insufficient permissions to read settings");
    }

    const settings = await ctx.db.query("siteSettings").collect();

    // Filter settings by category prefix
    const categorySettings = settings.filter(setting =>
      setting.key.startsWith(`${args.category}.`)
    );

    const settingsObject = categorySettings.reduce((acc, setting) => {
      // Remove category prefix from key
      const key = setting.key.replace(`${args.category}.`, '');
      acc[key] = setting.value;
      return acc;
    }, {} as Record<string, any>);

    return settingsObject;
  },
});

// Get public settings by category (no authentication required)
export const getPublicSettingsByCategory = query({
  args: { category: v.string() },
  handler: async (ctx, args) => {
    // Define which settings are public
    const publicSettingKeys = [
      // General public info
      "general.site_name", "general.site_description", "general.site_url",
      "general.admin_email", "general.support_email", "general.phone", "general.address",
      "general.language", "general.currency", "general.timezone",
      // SEO settings
      "seo.meta_title", "seo.meta_description", "seo.meta_keywords", "seo.og_image", "seo.twitter_handle",
      "seo.google_analytics_id", "seo.google_tag_manager_id", "seo.facebook_pixel_id",
      // Social media
      "social.facebook_url", "social.twitter_url", "social.linkedin_url", "social.instagram_url", "social.youtube_url",
      "social.show_social_header", "social.show_social_footer", "social.show_social_contact", "social.social_open_new_tab",
      // Contact settings
      "contact.show_phone", "contact.show_email", "contact.show_address", "contact.business_hours", "contact.emergency_support",
      "contact.contact_form_enabled", "contact.contact_form_recipient", "contact.contact_form_subject",
      // Feature flags
      "features.maintenance_mode", "features.registration_enabled", "features.comments_enabled", "features.analytics_enabled",
      "features.newsletter_enabled", "features.contact_form_enabled", "features.search_enabled", "features.user_profiles_public",
      "features.api_access_enabled", "features.debug_mode",
      // Performance settings
      "performance.cache_duration", "performance.image_optimization", "performance.lazy_loading", "performance.compression_enabled",
      "performance.compression_level", "performance.image_quality", "performance.max_image_width", "performance.enable_caching",
      "performance.cache_api_responses", "performance.webp_conversion", "performance.preload_critical_resources",
      "performance.minify_css", "performance.minify_js",
      // Branding
      "branding.primary_color", "branding.secondary_color", "branding.accent_color", "branding.logo_url",
      "branding.favicon_url", "branding.font_family",
    ];

    const settings = await ctx.db.query("siteSettings").collect();

    // Filter settings by category prefix and public accessibility
    const categorySettings = settings.filter(setting =>
      setting.key.startsWith(`${args.category}.`) && publicSettingKeys.includes(setting.key)
    );

    const settingsObject = categorySettings.reduce((acc, setting) => {
      // Remove category prefix from key
      const key = setting.key.replace(`${args.category}.`, '');
      acc[key] = setting.value;
      return acc;
    }, {} as Record<string, any>);

    return settingsObject;
  },
});

// Initialize default settings
export const initializeDefaultSettings = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    
    if (!hasSettingsPermission(user, "write")) {
      throw new Error("Insufficient permissions to initialize settings");
    }

    const defaultSettings = [
      // General Settings
      { key: "general.site_name", value: "OfficeTech Guinea", description: "Website name" },
      { key: "general.site_description", value: "Leading technology and security solutions in Guinea", description: "Website description" },
      { key: "general.site_url", value: "https://officetech.gn", description: "Website URL" },
      { key: "general.admin_email", value: "<EMAIL>", description: "Admin email address" },
      { key: "general.support_email", value: "<EMAIL>", description: "Support email address" },
      { key: "general.phone", value: "+224 123 456 789", description: "Main phone number" },
      { key: "general.address", value: "Conakry, Guinea", description: "Business address" },
      { key: "general.timezone", value: "Africa/Conakry", description: "Default timezone" },
      { key: "general.language", value: "en", description: "Default language" },
      { key: "general.currency", value: "USD", description: "Default currency" },

      // SEO Settings
      { key: "seo.meta_title", value: "OfficeTech Guinea - Technology & Security Solutions", description: "Default meta title" },
      { key: "seo.meta_description", value: "Leading provider of technology and security solutions in Guinea. Network infrastructure, enterprise services, and IT support.", description: "Default meta description" },
      { key: "seo.meta_keywords", value: "technology, security, network, Guinea, enterprise, IT support", description: "Default meta keywords" },
      { key: "seo.og_image", value: "/images/og-default.jpg", description: "Default Open Graph image" },
      { key: "seo.twitter_handle", value: "@officetechgn", description: "Twitter handle" },
      { key: "seo.google_analytics_id", value: "", description: "Google Analytics tracking ID" },
      { key: "seo.google_tag_manager_id", value: "", description: "Google Tag Manager ID" },
      { key: "seo.facebook_pixel_id", value: "", description: "Facebook Pixel ID" },

      // Social Media
      { key: "social.facebook_url", value: "https://facebook.com/officetechguinea", description: "Facebook page URL" },
      { key: "social.twitter_url", value: "https://twitter.com/officetechgn", description: "Twitter profile URL" },
      { key: "social.linkedin_url", value: "https://linkedin.com/company/officetech-guinea", description: "LinkedIn company URL" },
      { key: "social.instagram_url", value: "https://instagram.com/officetechguinea", description: "Instagram profile URL" },
      { key: "social.youtube_url", value: "https://youtube.com/c/officetechguinea", description: "YouTube channel URL" },

      // Contact Settings
      { key: "contact.show_phone", value: true, description: "Show phone number on website" },
      { key: "contact.show_email", value: true, description: "Show email address on website" },
      { key: "contact.show_address", value: true, description: "Show physical address on website" },
      { key: "contact.business_hours", value: "Monday-Friday: 8AM-6PM", description: "Business hours display" },
      { key: "contact.emergency_support", value: "24/7 Emergency Support Available", description: "Emergency support message" },

      // Email Settings
      { key: "email.smtp_host", value: "", description: "SMTP server host" },
      { key: "email.smtp_port", value: 587, description: "SMTP server port" },
      { key: "email.smtp_username", value: "", description: "SMTP username" },
      { key: "email.smtp_password", value: "", description: "SMTP password" },
      { key: "email.from_name", value: "OfficeTech Guinea", description: "Email sender name" },
      { key: "email.from_email", value: "<EMAIL>", description: "Email sender address" },

      // Feature Flags
      { key: "features.maintenance_mode", value: false, description: "Enable maintenance mode" },
      { key: "features.registration_enabled", value: true, description: "Allow new user registration" },
      { key: "features.comments_enabled", value: true, description: "Enable comments on content" },
      { key: "features.analytics_enabled", value: true, description: "Enable analytics tracking" },
      { key: "features.newsletter_enabled", value: true, description: "Enable newsletter signup" },

      // Performance Settings
      { key: "performance.cache_duration", value: 3600, description: "Cache duration in seconds" },
      { key: "performance.image_optimization", value: true, description: "Enable image optimization" },
      { key: "performance.lazy_loading", value: true, description: "Enable lazy loading" },
      { key: "performance.compression_enabled", value: true, description: "Enable content compression" },
    ];

    const now = Date.now();

    for (const setting of defaultSettings) {
      const existing = await ctx.db
        .query("siteSettings")
        .withIndex("by_key", (q) => q.eq("key", setting.key))
        .first();

      if (!existing) {
        await ctx.db.insert("siteSettings", {
          ...setting,
          updatedBy: user.clerkId,
          updatedAt: now,
        });
      }
    }

    return { success: true, initialized: defaultSettings.length };
  },
});
