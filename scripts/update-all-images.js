#!/usr/bin/env node

// <PERSON>ript to update all images with local images from /img folder
import { ConvexHttpClient } from "convex/browser";

async function updateAllImages() {
  try {
    // Get the Convex URL from environment
    const convexUrl = process.env.VITE_CONVEX_URL || process.env.CONVEX_URL;
    
    if (!convexUrl) {
      console.error("❌ CONVEX_URL not found in environment variables");
      console.log("Make sure Convex is running with 'npx convex dev'");
      process.exit(1);
    }

    console.log("🚀 Updating all images with local images...");
    console.log(`📡 Connecting to: ${convexUrl}`);

    const client = new ConvexHttpClient(convexUrl);

    // Call the setup function to update all images
    console.log("📝 Updating images in database...");
    const result = await client.mutation("setup:updateAllImages", {});

    if (result.success) {
      console.log("✅ All images updated successfully!");
      console.log(`📊 Updated ${result.updatedCount} content items`);
      result.results.forEach(message => {
        console.log(`  ✓ ${message}`);
      });
      
      console.log("\n📋 Image mappings used:");
      console.log("  • Hero sections: /img/img_presentation.jpg");
      console.log("  • About sections: /img/Immg1.jpg");
      console.log("  • Cybersecurity services: /img/cyber-security-1186529_1920.png");
      console.log("  • Network services: /img/img_cables1.jpg");
      console.log("  • Surveillance services: /img/img_field_tech.jpg");
      console.log("  • Training services: /img/img_presentation.jpg");
      console.log("  • Development services: /img/Coding_img.jpg");
      console.log("  • Support services: /img/img_typing_white.jpg");
    } else {
      console.error("❌ Failed to update images:", result.error);
      process.exit(1);
    }

  } catch (error) {
    console.error("❌ Error updating images:", error);
    process.exit(1);
  }
}

// Run the update
updateAllImages();
