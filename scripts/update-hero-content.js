#!/usr/bin/env node

// <PERSON>ript to update existing hero content with new fields
import { ConvexHttpClient } from "convex/browser";

async function updateHeroContent() {
  try {
    // Get the Convex URL from environment
    const convexUrl = process.env.VITE_CONVEX_URL || process.env.CONVEX_URL;
    
    if (!convexUrl) {
      console.error("❌ CONVEX_URL not found in environment variables");
      console.log("Make sure Convex is running with 'npx convex dev'");
      process.exit(1);
    }

    console.log("🚀 Updating hero content with new fields...");
    console.log(`📡 Connecting to: ${convexUrl}`);

    const client = new ConvexHttpClient(convexUrl);

    // Get current hero content
    console.log("📊 Getting current hero content...");
    const englishHero = await client.query("content:getContent", {
      identifier: "home-hero",
      language: "en"
    });
    
    const spanishHero = await client.query("content:getContent", {
      identifier: "home-hero", 
      language: "es"
    });

    if (!englishHero || !spanishHero) {
      console.error("❌ Hero content not found");
      process.exit(1);
    }

    console.log("📄 Current English hero data:", englishHero.data);
    console.log("📄 Current Spanish hero data:", spanishHero.data);

    // Update English hero content
    const updatedEnglishData = {
      ...englishHero.data,
      secondaryCtaText: "View Our Work",
      secondaryCtaUrl: "#work",
      badge1Text: "24/7 Support",
      badge2Text: "Secure Solutions", 
      experienceYears: "15+",
      experienceLabel: "Years\nExperience",
      heroImageAlt: "Professional IT team working on technology solutions"
    };

    // Update Spanish hero content
    const updatedSpanishData = {
      ...spanishHero.data,
      secondaryCtaText: "Ver Nuestro Trabajo",
      secondaryCtaUrl: "#trabajo",
      badge1Text: "Soporte 24/7",
      badge2Text: "Soluciones Seguras",
      experienceYears: "15+", 
      experienceLabel: "Años de\nExperiencia",
      heroImageAlt: "Equipo profesional de TI trabajando en soluciones tecnológicas"
    };

    console.log("🔧 Updating hero content with new fields...");
    const heroResult = await client.mutation("setup:updateHeroContentFields", {});

    if (heroResult.success) {
      console.log("✅ Hero content updated successfully!");
      console.log("📊 Results:", heroResult.results);
    } else {
      console.error("❌ Failed to update hero content:", heroResult.message);
      process.exit(1);
    }

    console.log("🏗️ Creating missing home page content...");
    const contentResult = await client.mutation("setup:createMissingHomePageContent", {});

    if (contentResult.success) {
      console.log("✅ Missing content created successfully!");
      console.log("📊 Content Types:", contentResult.results.contentTypes);
      console.log("📊 Content Items:", contentResult.results.content);
      if (contentResult.results.errors.length > 0) {
        console.log("⚠️ Errors:", contentResult.results.errors);
      }
      console.log("🌐 Visit your website to see all the updated sections");
    } else {
      console.error("❌ Failed to create missing content:", contentResult.message);
      process.exit(1);
    }

  } catch (error) {
    console.error("❌ Failed to update hero content:", error);
    process.exit(1);
  }
}

// Run the update
updateHeroContent();
