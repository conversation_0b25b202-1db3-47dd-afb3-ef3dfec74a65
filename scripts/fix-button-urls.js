#!/usr/bin/env node

// <PERSON><PERSON><PERSON> to fix button URLs on the homepage
import { ConvexHttpClient } from "convex/browser";

async function fixButtonUrls() {
  try {
    const convexUrl = process.env.VITE_CONVEX_URL || process.env.CONVEX_URL;
    
    if (!convexUrl) {
      console.error("❌ CONVEX_URL not found in environment variables");
      process.exit(1);
    }

    console.log("🔧 Fixing button URLs...");
    console.log(`📡 Connecting to: ${convexUrl}`);

    const client = new ConvexHttpClient(convexUrl);

    // Get current hero content
    const englishHero = await client.query("content:getContent", {
      identifier: "home-hero",
      language: "en"
    });
    
    const spanishHero = await client.query("content:getContent", {
      identifier: "home-hero", 
      language: "es"
    });

    if (!englishHero || !spanishHero) {
      console.error("❌ Hero content not found");
      process.exit(1);
    }

    console.log("📄 Current button URLs:");
    console.log("English - Primary CTA:", englishHero.data.ctaUrl);
    console.log("English - Secondary CTA:", englishHero.data.secondaryCtaUrl);
    console.log("Spanish - Primary CTA:", spanishHero.data.ctaUrl);
    console.log("Spanish - Secondary CTA:", spanishHero.data.secondaryCtaUrl);

    // Update English hero content with proper URLs
    const updatedEnglishData = {
      ...englishHero.data,
      ctaUrl: "/contact", // Keep primary CTA pointing to contact
      secondaryCtaUrl: "/services", // Change secondary CTA to services page
    };

    // Update Spanish hero content with proper URLs
    const updatedSpanishData = {
      ...spanishHero.data,
      ctaUrl: "/contact", // Keep primary CTA pointing to contact
      secondaryCtaUrl: "/services", // Change secondary CTA to services page
    };

    console.log("🔧 Fixing button URLs...");
    const result = await client.mutation("setup:fixButtonUrls", {});

    if (result.success) {
      console.log("✅ Button URLs updated successfully!");
      console.log("📊 Results:", result.results);
      console.log("📄 New button URLs:");
      console.log("Primary CTA (Get Started):", result.urls.primaryCta);
      console.log("Secondary CTA (View Our Work):", result.urls.secondaryCta);
      console.log("🌐 Visit your website to test the buttons");
    } else {
      console.error("❌ Failed to fix button URLs:", result.message);
      process.exit(1);
    }

  } catch (error) {
    console.error("❌ Failed to fix button URLs:", error);
    process.exit(1);
  }
}

// Run the fix
fixButtonUrls();
