#!/usr/bin/env node

// <PERSON>rip<PERSON> to test that all content sections are loading correctly
import { ConvexHttpClient } from "convex/browser";

async function testContentLoading() {
  try {
    // Get the Convex URL from environment
    const convexUrl = process.env.VITE_CONVEX_URL || process.env.CONVEX_URL;
    
    if (!convexUrl) {
      console.error("❌ CONVEX_URL not found in environment variables");
      process.exit(1);
    }

    console.log("🧪 Testing content loading...");
    console.log(`📡 Connecting to: ${convexUrl}`);

    const client = new ConvexHttpClient(convexUrl);

    // Test content sections that should be available
    const contentSections = [
      { identifier: "home-hero", language: "en", name: "Hero Section (English)" },
      { identifier: "home-hero", language: "es", name: "Hero Section (Spanish)" },
      { identifier: "who-we-are", language: "en", name: "Who We Are (English)" },
      { identifier: "who-we-are", language: "es", name: "Who We Are (Spanish)" },
      { identifier: "services-banner", language: "en", name: "Services Banner (English)" },
      { identifier: "services-banner", language: "es", name: "Services Banner (Spanish)" },
      { identifier: "key-features", language: "en", name: "Key Features (English)" },
      { identifier: "key-features", language: "es", name: "Key Features (Spanish)" },
      { identifier: "trusted-partners", language: "en", name: "Trusted Partners (English)" },
      { identifier: "trusted-partners", language: "es", name: "Trusted Partners (Spanish)" },
    ];

    const results = {
      success: [],
      missing: [],
      errors: [],
    };

    console.log("📊 Testing content sections...");
    
    for (const section of contentSections) {
      try {
        const content = await client.query("content:getContent", {
          identifier: section.identifier,
          language: section.language
        });

        if (content) {
          results.success.push({
            name: section.name,
            identifier: section.identifier,
            language: section.language,
            hasData: Object.keys(content.data || {}).length > 0,
            dataKeys: Object.keys(content.data || {}),
          });
          console.log(`✅ ${section.name} - Found with ${Object.keys(content.data || {}).length} fields`);
        } else {
          results.missing.push(section);
          console.log(`❌ ${section.name} - Not found`);
        }
      } catch (error) {
        results.errors.push({ section, error: error.message });
        console.log(`⚠️ ${section.name} - Error: ${error.message}`);
      }
    }

    // Test content types (optional)
    console.log("\n📋 Testing content types...");
    try {
      const contentTypes = await client.query("contentTypes:list", {});
      console.log(`Found ${contentTypes.length} content types:`);
      contentTypes.forEach(type => {
        console.log(`  - ${type.name}: ${type.label}`);
      });
    } catch (error) {
      console.log("ℹ️ Content types query not available (this is normal)");
    }

    // Summary
    console.log("\n📈 Test Summary:");
    console.log(`✅ Successfully loaded: ${results.success.length} sections`);
    console.log(`❌ Missing sections: ${results.missing.length}`);
    console.log(`⚠️ Errors: ${results.errors.length}`);

    if (results.missing.length > 0) {
      console.log("\n❌ Missing sections:");
      results.missing.forEach(section => {
        console.log(`  - ${section.name}`);
      });
    }

    if (results.errors.length > 0) {
      console.log("\n⚠️ Errors:");
      results.errors.forEach(({ section, error }) => {
        console.log(`  - ${section.name}: ${error}`);
      });
    }

    // Test inline editing capability
    console.log("\n🔧 Testing inline editing capability...");
    const heroContent = results.success.find(s => s.identifier === "home-hero" && s.language === "en");
    if (heroContent) {
      console.log("✅ Hero content available for editing");
      console.log(`   Available fields: ${heroContent.dataKeys.join(", ")}`);
      
      // Check if required fields for inline editing are present
      const requiredHeroFields = ["title", "subtitle", "ctaText", "secondaryCtaText"];
      const missingFields = requiredHeroFields.filter(field => !heroContent.dataKeys.includes(field));
      
      if (missingFields.length === 0) {
        console.log("✅ All required hero fields available for inline editing");
      } else {
        console.log(`⚠️ Missing hero fields: ${missingFields.join(", ")}`);
      }
    }

    const overallSuccess = results.missing.length === 0 && results.errors.length === 0;
    
    if (overallSuccess) {
      console.log("\n🎉 All content sections are properly loaded and ready for inline editing!");
      console.log("🌐 Visit http://localhost:8081/ to test the website");
      console.log("🔧 Visit http://localhost:8081/admin to test admin functionality");
    } else {
      console.log("\n⚠️ Some issues found. Please check the missing sections and errors above.");
    }

    return overallSuccess;

  } catch (error) {
    console.error("❌ Failed to test content loading:", error);
    return false;
  }
}

// Run the test
testContentLoading().then(success => {
  process.exit(success ? 0 : 1);
});
