#!/usr/bin/env node

// Simple script to initialize the CMS with content
// This can be run manually or as part of setup

import { ConvexHttpClient } from "convex/browser";

async function initializeCMS() {
  try {
    // Get the Convex URL from environment
    const convexUrl = process.env.VITE_CONVEX_URL || process.env.CONVEX_URL;
    
    if (!convexUrl) {
      console.error("❌ CONVEX_URL not found in environment variables");
      console.log("Make sure Convex is running with 'npx convex dev'");
      process.exit(1);
    }

    console.log("🚀 Initializing CMS...");
    console.log(`📡 Connecting to: ${convexUrl}`);

    const client = new ConvexHttpClient(convexUrl);

    // Check current status
    console.log("📊 Checking current CMS status...");
    const status = await client.mutation("setup:checkCMSStatus", {});
    
    console.log(`📈 Current status:
  - Content Types: ${status.contentTypesCount}
  - Content Items: ${status.contentItemsCount}
  - Initialized: ${status.isInitialized ? '✅' : '❌'}`);

    if (status.isInitialized) {
      console.log("✅ CMS is already initialized!");
      console.log("\n📋 Existing content types:");
      status.contentTypes.forEach(ct => {
        console.log(`  - ${ct.name} (${ct.label})`);
      });
      
      console.log("\n📄 Existing content items:");
      const grouped = status.contentItems.reduce((acc, item) => {
        if (!acc[item.identifier]) acc[item.identifier] = [];
        acc[item.identifier].push(item);
        return acc;
      }, {});
      
      Object.entries(grouped).forEach(([identifier, items]) => {
        const languages = items.map(item => `${item.language}(${item.status})`).join(', ');
        console.log(`  - ${identifier}: ${languages}`);
      });
      
      return;
    }

    // Initialize CMS
    console.log("🔧 Initializing CMS with default content...");
    const result = await client.mutation("setup:initializeCMS", {});

    console.log("\n✅ CMS Initialization Complete!");
    console.log(`📊 Results:
  - Content Types Created: ${result.contentTypes.filter(ct => ct.status === 'created').length}
  - Content Types Existing: ${result.contentTypes.filter(ct => ct.status === 'exists').length}
  - Content Items Created: ${result.content.filter(c => c.status === 'created').length}`);

    if (result.errors && result.errors.length > 0) {
      console.log("\n⚠️  Errors encountered:");
      result.errors.forEach(error => {
        console.log(`  - ${error}`);
      });
    }

    console.log("\n🎉 Your CMS is ready to use!");
    console.log("🌐 Visit your website to see the content in action");
    console.log("⚙️  Visit /admin to manage content");

  } catch (error) {
    console.error("❌ Failed to initialize CMS:", error);
    process.exit(1);
  }
}

// Run the initialization
initializeCMS();
