#!/bin/bash

# Deployment script for OfficeTech Guinea website
# Usage: ./scripts/deploy.sh [environment] [platform]
# Example: ./scripts/deploy.sh production vercel

set -e

# Configuration
ENVIRONMENT=${1:-staging}
PLATFORM=${2:-vercel}
PROJECT_NAME="officetech-guinea"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    
    case $PLATFORM in
        vercel)
            if ! command -v vercel &> /dev/null; then
                log_error "Vercel CLI is not installed. Run: npm i -g vercel"
                exit 1
            fi
            ;;
        netlify)
            if ! command -v netlify &> /dev/null; then
                log_error "Netlify CLI is not installed. Run: npm i -g netlify-cli"
                exit 1
            fi
            ;;
        docker)
            if ! command -v docker &> /dev/null; then
                log_error "Docker is not installed"
                exit 1
            fi
            ;;
    esac
}

# Build the application
build_app() {
    log_info "Building application for $ENVIRONMENT..."
    
    # Install dependencies
    npm ci
    
    # Run tests
    log_info "Running tests..."
    npm run test
    
    # Run linting
    log_info "Running linting..."
    npm run lint
    
    # Build the application
    log_info "Building application..."
    if [ "$ENVIRONMENT" = "production" ]; then
        npm run build
    else
        npm run build:dev
    fi
    
    log_info "Build completed successfully!"
}

# Deploy to Vercel
deploy_vercel() {
    log_info "Deploying to Vercel..."
    
    if [ "$ENVIRONMENT" = "production" ]; then
        vercel --prod --yes
    else
        vercel --yes
    fi
    
    log_info "Deployment to Vercel completed!"
}

# Deploy to Netlify
deploy_netlify() {
    log_info "Deploying to Netlify..."
    
    if [ "$ENVIRONMENT" = "production" ]; then
        netlify deploy --prod --dir=dist
    else
        netlify deploy --dir=dist
    fi
    
    log_info "Deployment to Netlify completed!"
}

# Deploy using Docker
deploy_docker() {
    log_info "Deploying using Docker..."
    
    # Build Docker image
    docker build -t $PROJECT_NAME:$ENVIRONMENT .
    
    # Stop existing container if running
    docker stop $PROJECT_NAME-$ENVIRONMENT 2>/dev/null || true
    docker rm $PROJECT_NAME-$ENVIRONMENT 2>/dev/null || true
    
    # Run new container
    docker run -d \
        --name $PROJECT_NAME-$ENVIRONMENT \
        -p 8080:80 \
        --restart unless-stopped \
        $PROJECT_NAME:$ENVIRONMENT
    
    log_info "Docker deployment completed!"
}

# Deploy to AWS S3 + CloudFront
deploy_aws() {
    log_info "Deploying to AWS S3..."
    
    if [ -z "$AWS_S3_BUCKET" ]; then
        log_error "AWS_S3_BUCKET environment variable is not set"
        exit 1
    fi
    
    # Sync files to S3
    aws s3 sync dist/ s3://$AWS_S3_BUCKET --delete
    
    # Invalidate CloudFront cache if distribution ID is provided
    if [ -n "$AWS_CLOUDFRONT_DISTRIBUTION_ID" ]; then
        log_info "Invalidating CloudFront cache..."
        aws cloudfront create-invalidation \
            --distribution-id $AWS_CLOUDFRONT_DISTRIBUTION_ID \
            --paths "/*"
    fi
    
    log_info "AWS deployment completed!"
}

# Deploy to GitHub Pages
deploy_github_pages() {
    log_info "Deploying to GitHub Pages..."
    
    # Install gh-pages if not already installed
    if ! npm list gh-pages &> /dev/null; then
        npm install --save-dev gh-pages
    fi
    
    # Deploy to gh-pages branch
    npx gh-pages -d dist
    
    log_info "GitHub Pages deployment completed!"
}

# Run pre-deployment checks
pre_deployment_checks() {
    log_info "Running pre-deployment checks..."
    
    # Check if environment variables are set
    if [ "$ENVIRONMENT" = "production" ]; then
        if [ -z "$VITE_CLERK_PUBLISHABLE_KEY" ]; then
            log_warn "VITE_CLERK_PUBLISHABLE_KEY is not set"
        fi
        
        if [ -z "$VITE_CONVEX_URL" ]; then
            log_warn "VITE_CONVEX_URL is not set"
        fi
    fi
    
    # Check if dist directory exists
    if [ ! -d "dist" ]; then
        log_error "dist directory not found. Please run build first."
        exit 1
    fi
    
    log_info "Pre-deployment checks passed!"
}

# Post-deployment tasks
post_deployment_tasks() {
    log_info "Running post-deployment tasks..."
    
    # Run smoke tests
    if [ "$PLATFORM" != "docker" ]; then
        log_info "Running smoke tests..."
        # Add smoke test commands here
        # curl -f https://your-domain.com/health || exit 1
    fi
    
    # Send notification (if configured)
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚀 $PROJECT_NAME deployed to $ENVIRONMENT on $PLATFORM\"}" \
            $SLACK_WEBHOOK_URL
    fi
    
    log_info "Post-deployment tasks completed!"
}

# Main deployment function
main() {
    log_info "Starting deployment process..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Platform: $PLATFORM"
    
    check_dependencies
    build_app
    pre_deployment_checks
    
    case $PLATFORM in
        vercel)
            deploy_vercel
            ;;
        netlify)
            deploy_netlify
            ;;
        docker)
            deploy_docker
            ;;
        aws)
            deploy_aws
            ;;
        github-pages)
            deploy_github_pages
            ;;
        *)
            log_error "Unknown platform: $PLATFORM"
            log_info "Supported platforms: vercel, netlify, docker, aws, github-pages"
            exit 1
            ;;
    esac
    
    post_deployment_tasks
    
    log_info "Deployment completed successfully! 🎉"
}

# Show usage if no arguments provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 [environment] [platform]"
    echo ""
    echo "Environments: staging, production"
    echo "Platforms: vercel, netlify, docker, aws, github-pages"
    echo ""
    echo "Examples:"
    echo "  $0 staging vercel"
    echo "  $0 production netlify"
    echo "  $0 production docker"
    exit 1
fi

# Run main function
main
