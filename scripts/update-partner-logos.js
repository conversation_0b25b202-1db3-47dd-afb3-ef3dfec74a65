#!/usr/bin/env node

// <PERSON>ript to update partner logos with local images
import { ConvexHttpClient } from "convex/browser";

async function updatePartnerLogos() {
  try {
    // Get the Convex URL from environment
    const convexUrl = process.env.VITE_CONVEX_URL || process.env.CONVEX_URL;

    if (!convexUrl) {
      console.error("❌ CONVEX_URL not found in environment variables");
      console.log("Make sure Convex is running with 'npx convex dev'");
      process.exit(1);
    }

    console.log("🚀 Updating partner logos...");
    console.log(`📡 Connecting to: ${convexUrl}`);

    const client = new ConvexHttpClient(convexUrl);

    // Call the setup function to update partner logos
    console.log("📝 Updating partner logos in database...");
    const result = await client.mutation("setup:updatePartnerLogos", {});

    if (result.success) {
      console.log("✅ Partner logos updated successfully!");
      console.log(`📊 Updated ${result.partnersUpdated} partners`);
      result.results.forEach(message => {
        console.log(`  ✓ ${message}`);
      });

      console.log("📋 New partners:");
      const partners = [
        { name: "Microsoft", logo: "/partners/part1.jpg" },
        { name: "Cisco", logo: "/partners/part2.jpg" },
        { name: "VMware", logo: "/partners/part3.jpg" },
        { name: "Dell", logo: "/partners/part4.jpg" },
        { name: "Adobe", logo: "/partners/part5.jpg" },
        { name: "Google", logo: "/partners/part6.jpg" },
      ];
      partners.forEach((partner, index) => {
        console.log(`  ${index + 1}. ${partner.name} - ${partner.logo}`);
      });
    } else {
      console.error("❌ Failed to update partner logos:", result.error);
      process.exit(1);
    }

  } catch (error) {
    console.error("❌ Error updating partner logos:", error);
    process.exit(1);
  }
}

// Run the update
updatePartnerLogos();
