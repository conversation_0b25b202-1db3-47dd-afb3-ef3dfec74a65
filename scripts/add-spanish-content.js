#!/usr/bin/env node

// Script to add Spanish translations for existing content

import { ConvexHttpClient } from "convex/browser";

async function addSpanishContent() {
  try {
    const convexUrl = process.env.VITE_CONVEX_URL || "https://uncommon-bulldog-76.convex.cloud";
    
    console.log("🚀 Adding Spanish content...");
    console.log(`📡 Connecting to: ${convexUrl}`);

    const client = new ConvexHttpClient(convexUrl);

    // Get existing content types
    const contentTypes = await client.query("contentTypes:getAllContentTypes", {});
    const contentTypeMap = {};
    contentTypes.forEach(ct => {
      contentTypeMap[ct.name] = ct._id;
    });

    // Spanish content to add
    const spanishContent = [
      {
        identifier: "home-hero",
        language: "es",
        contentTypeId: contentTypeMap["hero_section"],
        status: "published",
        data: {
          title: "Soluciones Tecnológicas Líderes en Guinea Ecuatorial",
          subtitle: "OfficeTech ofrece soluciones de vanguardia en ciberseguridad, infraestructura de red y capacitación en TI para proteger y empoderar su negocio en la era digital.",
          ctaText: "Comenzar Hoy",
          ctaUrl: "/contact",
        },
      },
      {
        identifier: "services-banner",
        language: "es",
        contentTypeId: contentTypeMap["content_section"],
        status: "published",
        data: {
          title: "Nuestros Servicios",
          subtitle: "Soluciones tecnológicas integrales para su negocio",
          content: "Ciberseguridad • Diseño Web • Vigilancia Electrónica • Capacitación en TI • Infraestructura de Red • Integración de Sistemas",
        },
      },
      {
        identifier: "contact-info",
        language: "es",
        contentTypeId: contentTypeMap["contact_info"],
        status: "published",
        data: {
          title: "Contáctanos",
          address: "Malabo, Guinea Ecuatorial<br>Distrito Central de Negocios",
          phone: "+240 123 456 789",
          email: "<EMAIL>",
          hours: "Lunes - Viernes: 8:00 AM - 6:00 PM<br>Sábado: 9:00 AM - 2:00 PM<br>Soporte de Emergencia 24/7 Disponible",
        },
      },
      {
        identifier: "service-cybersecurity",
        language: "es",
        contentTypeId: contentTypeMap["service_card"],
        status: "published",
        data: {
          title: "Soluciones de Ciberseguridad",
          description: "Protección integral contra amenazas cibernéticas con monitoreo 24/7, evaluaciones de vulnerabilidad y respuesta a incidentes.",
          icon: "🛡️",
        },
      },
      {
        identifier: "service-network",
        language: "es",
        contentTypeId: contentTypeMap["service_card"],
        status: "published",
        data: {
          title: "Infraestructura de Red",
          description: "Diseño e implementación de redes empresariales robustas con conectividad confiable y escalabilidad futura.",
          icon: "🌐",
        },
      },
      {
        identifier: "service-surveillance",
        language: "es",
        contentTypeId: contentTypeMap["service_card"],
        status: "published",
        data: {
          title: "Vigilancia Electrónica",
          description: "Sistemas de seguridad avanzados con cámaras IP, control de acceso y monitoreo remoto para proteger sus activos.",
          icon: "📹",
        },
      },
      {
        identifier: "service-training",
        language: "es",
        contentTypeId: contentTypeMap["service_card"],
        status: "published",
        data: {
          title: "Capacitación en TI",
          description: "Programas de capacitación especializados para desarrollar habilidades técnicas y mejorar la productividad del equipo.",
          icon: "🎓",
        },
      },
      {
        identifier: "cta-section",
        language: "es",
        contentTypeId: contentTypeMap["cta_section"],
        status: "published",
        data: {
          title: "¿Listo para Transformar su Negocio?",
          subtitle: "Asegurando su futuro digital con soluciones tecnológicas innovadoras y experiencia sin compromisos.",
          ctaText: "Contáctanos Hoy",
          ctaUrl: "/contact",
        },
      },
    ];

    console.log(`📝 Adding ${spanishContent.length} Spanish content items...`);

    let created = 0;
    let errors = 0;

    for (const content of spanishContent) {
      try {
        await client.mutation("content:upsertContent", {
          identifier: content.identifier,
          language: content.language,
          data: content.data,
          contentTypeId: content.contentTypeId,
          status: content.status,
        });
        console.log(`✅ Added: ${content.identifier} (es)`);
        created++;
      } catch (error) {
        console.log(`❌ Failed: ${content.identifier} - ${error.message}`);
        errors++;
      }
    }

    console.log(`\n🎉 Spanish content addition complete!`);
    console.log(`✅ Created: ${created}`);
    console.log(`❌ Errors: ${errors}`);

  } catch (error) {
    console.error("❌ Failed to add Spanish content:", error);
    process.exit(1);
  }
}

// Run the script
addSpanishContent();
