#!/usr/bin/env node

/**
 * SEO and Performance Testing Script
 * 
 * This script tests various SEO and performance aspects of the website
 * including meta tags, structured data, Core Web Vitals, and accessibility.
 */

const puppeteer = require('puppeteer');
const lighthouse = require('lighthouse');
const fs = require('fs');
const path = require('path');

const BASE_URL = process.env.BASE_URL || 'http://localhost:8081';

class SEOPerformanceTester {
  constructor() {
    this.browser = null;
    this.results = {
      seo: {},
      performance: {},
      accessibility: {},
      errors: []
    };
  }

  async init() {
    this.browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async testSEOMetaTags(url) {
    console.log(`Testing SEO meta tags for: ${url}`);
    
    const page = await this.browser.newPage();
    await page.goto(url, { waitUntil: 'networkidle0' });

    const seoData = await page.evaluate(() => {
      const getMetaContent = (selector) => {
        const meta = document.querySelector(selector);
        return meta ? meta.getAttribute('content') : null;
      };

      const getMetaProperty = (property) => {
        const meta = document.querySelector(`meta[property="${property}"]`);
        return meta ? meta.getAttribute('content') : null;
      };

      return {
        title: document.title,
        description: getMetaContent('meta[name="description"]'),
        keywords: getMetaContent('meta[name="keywords"]'),
        canonical: document.querySelector('link[rel="canonical"]')?.href,
        robots: getMetaContent('meta[name="robots"]'),
        
        // Open Graph
        ogTitle: getMetaProperty('og:title'),
        ogDescription: getMetaProperty('og:description'),
        ogImage: getMetaProperty('og:image'),
        ogUrl: getMetaProperty('og:url'),
        ogType: getMetaProperty('og:type'),
        ogSiteName: getMetaProperty('og:site_name'),
        
        // Twitter Card
        twitterCard: getMetaContent('meta[name="twitter:card"]'),
        twitterTitle: getMetaContent('meta[name="twitter:title"]'),
        twitterDescription: getMetaContent('meta[name="twitter:description"]'),
        twitterImage: getMetaContent('meta[name="twitter:image"]'),
        twitterSite: getMetaContent('meta[name="twitter:site"]'),
        
        // Structured Data
        structuredData: Array.from(document.querySelectorAll('script[type="application/ld+json"]'))
          .map(script => {
            try {
              return JSON.parse(script.textContent);
            } catch (e) {
              return null;
            }
          })
          .filter(Boolean)
      };
    });

    await page.close();
    return seoData;
  }

  async testPerformanceMetrics(url) {
    console.log(`Testing performance metrics for: ${url}`);
    
    const page = await this.browser.newPage();
    
    // Enable performance monitoring
    await page.setCacheEnabled(false);
    
    const startTime = Date.now();
    await page.goto(url, { waitUntil: 'networkidle0' });
    const loadTime = Date.now() - startTime;

    // Get Core Web Vitals
    const webVitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals = {};
        
        // Largest Contentful Paint
        if ('PerformanceObserver' in window) {
          try {
            const lcpObserver = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              const lastEntry = entries[entries.length - 1];
              vitals.lcp = lastEntry.startTime;
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
            
            // First Input Delay would need real user interaction
            // Cumulative Layout Shift
            const clsObserver = new PerformanceObserver((list) => {
              let clsValue = 0;
              const entries = list.getEntries();
              entries.forEach((entry) => {
                if (!entry.hadRecentInput) {
                  clsValue += entry.value;
                }
              });
              vitals.cls = clsValue;
            });
            clsObserver.observe({ entryTypes: ['layout-shift'] });
            
            setTimeout(() => resolve(vitals), 2000);
          } catch (error) {
            resolve({ error: error.message });
          }
        } else {
          resolve({ error: 'PerformanceObserver not supported' });
        }
      });
    });

    // Get resource loading metrics
    const resourceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      const resources = performance.getEntriesByType('resource');
      
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
        resourceCount: resources.length,
        totalResourceSize: resources.reduce((total, resource) => total + (resource.transferSize || 0), 0)
      };
    });

    await page.close();
    
    return {
      loadTime,
      webVitals,
      resourceMetrics
    };
  }

  async testAccessibility(url) {
    console.log(`Testing accessibility for: ${url}`);
    
    const page = await this.browser.newPage();
    await page.goto(url, { waitUntil: 'networkidle0' });

    const accessibilityData = await page.evaluate(() => {
      const issues = [];
      
      // Check for missing alt attributes
      const images = document.querySelectorAll('img');
      images.forEach((img, index) => {
        if (!img.alt && !img.getAttribute('aria-label')) {
          issues.push(`Image ${index + 1} missing alt text`);
        }
      });
      
      // Check for heading hierarchy
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      let previousLevel = 0;
      headings.forEach((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1));
        if (level > previousLevel + 1) {
          issues.push(`Heading level skip detected at heading ${index + 1}`);
        }
        previousLevel = level;
      });
      
      // Check for form labels
      const inputs = document.querySelectorAll('input, textarea, select');
      inputs.forEach((input, index) => {
        const hasLabel = input.labels && input.labels.length > 0;
        const hasAriaLabel = input.getAttribute('aria-label');
        const hasAriaLabelledBy = input.getAttribute('aria-labelledby');
        
        if (!hasLabel && !hasAriaLabel && !hasAriaLabelledBy) {
          issues.push(`Form input ${index + 1} missing label`);
        }
      });
      
      // Check for color contrast (basic check)
      const elements = document.querySelectorAll('*');
      let contrastIssues = 0;
      elements.forEach((element) => {
        const style = window.getComputedStyle(element);
        const color = style.color;
        const backgroundColor = style.backgroundColor;
        
        // This is a simplified check - in reality, you'd need a proper contrast ratio calculation
        if (color === backgroundColor) {
          contrastIssues++;
        }
      });
      
      if (contrastIssues > 0) {
        issues.push(`${contrastIssues} potential color contrast issues detected`);
      }
      
      return {
        issues,
        imageCount: images.length,
        headingCount: headings.length,
        formInputCount: inputs.length
      };
    });

    await page.close();
    return accessibilityData;
  }

  async runLighthouseAudit(url) {
    console.log(`Running Lighthouse audit for: ${url}`);
    
    try {
      const { lhr } = await lighthouse(url, {
        port: 9222,
        output: 'json',
        logLevel: 'info',
        onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
      });

      return {
        performance: lhr.categories.performance.score * 100,
        accessibility: lhr.categories.accessibility.score * 100,
        bestPractices: lhr.categories['best-practices'].score * 100,
        seo: lhr.categories.seo.score * 100,
        audits: {
          firstContentfulPaint: lhr.audits['first-contentful-paint'].displayValue,
          largestContentfulPaint: lhr.audits['largest-contentful-paint'].displayValue,
          cumulativeLayoutShift: lhr.audits['cumulative-layout-shift'].displayValue,
          totalBlockingTime: lhr.audits['total-blocking-time'].displayValue,
        }
      };
    } catch (error) {
      console.error('Lighthouse audit failed:', error.message);
      return { error: error.message };
    }
  }

  async testStructuredData(url) {
    console.log(`Testing structured data for: ${url}`);
    
    const page = await this.browser.newPage();
    await page.goto(url, { waitUntil: 'networkidle0' });

    const structuredData = await page.evaluate(() => {
      const scripts = document.querySelectorAll('script[type="application/ld+json"]');
      const data = [];
      
      scripts.forEach((script) => {
        try {
          const json = JSON.parse(script.textContent);
          data.push(json);
        } catch (error) {
          data.push({ error: 'Invalid JSON-LD' });
        }
      });
      
      return data;
    });

    await page.close();
    return structuredData;
  }

  async generateReport() {
    const timestamp = new Date().toISOString();
    const reportPath = path.join(__dirname, '..', 'reports', `seo-performance-${timestamp.split('T')[0]}.json`);
    
    // Ensure reports directory exists
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    const report = {
      timestamp,
      baseUrl: BASE_URL,
      results: this.results
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`Report generated: ${reportPath}`);
    
    return report;
  }

  async run() {
    try {
      await this.init();
      
      const testUrls = [
        BASE_URL,
        `${BASE_URL}/about`,
        `${BASE_URL}/services`,
        `${BASE_URL}/contact`
      ];
      
      for (const url of testUrls) {
        console.log(`\n=== Testing ${url} ===`);
        
        try {
          // Test SEO
          this.results.seo[url] = await this.testSEOMetaTags(url);
          
          // Test Performance
          this.results.performance[url] = await this.testPerformanceMetrics(url);
          
          // Test Accessibility
          this.results.accessibility[url] = await this.testAccessibility(url);
          
          // Test Structured Data
          const structuredData = await this.testStructuredData(url);
          this.results.seo[url].structuredData = structuredData;
          
          // Run Lighthouse (optional, requires Chrome to be running with --remote-debugging-port=9222)
          // const lighthouse = await this.runLighthouseAudit(url);
          // this.results.lighthouse[url] = lighthouse;
          
        } catch (error) {
          console.error(`Error testing ${url}:`, error.message);
          this.results.errors.push({
            url,
            error: error.message
          });
        }
      }
      
      // Generate report
      const report = await this.generateReport();
      
      // Print summary
      this.printSummary();
      
      return report;
      
    } catch (error) {
      console.error('Test suite failed:', error);
      throw error;
    } finally {
      await this.close();
    }
  }

  printSummary() {
    console.log('\n=== SEO & Performance Test Summary ===');
    
    Object.keys(this.results.seo).forEach(url => {
      const seo = this.results.seo[url];
      const perf = this.results.performance[url];
      const a11y = this.results.accessibility[url];
      
      console.log(`\n${url}:`);
      console.log(`  Title: ${seo.title ? '✓' : '✗'}`);
      console.log(`  Description: ${seo.description ? '✓' : '✗'}`);
      console.log(`  Open Graph: ${seo.ogTitle && seo.ogDescription ? '✓' : '✗'}`);
      console.log(`  Twitter Card: ${seo.twitterCard ? '✓' : '✗'}`);
      console.log(`  Structured Data: ${seo.structuredData.length > 0 ? '✓' : '✗'}`);
      console.log(`  Load Time: ${perf.loadTime}ms`);
      console.log(`  Accessibility Issues: ${a11y.issues.length}`);
    });
    
    if (this.results.errors.length > 0) {
      console.log('\n=== Errors ===');
      this.results.errors.forEach(error => {
        console.log(`${error.url}: ${error.error}`);
      });
    }
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  const tester = new SEOPerformanceTester();
  tester.run()
    .then(() => {
      console.log('\nSEO and Performance tests completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Tests failed:', error);
      process.exit(1);
    });
}

module.exports = SEOPerformanceTester;
