#!/usr/bin/env node

/**
 * Accessibility Testing Script
 * 
 * This script tests accessibility compliance using axe-core
 */

const puppeteer = require('puppeteer');
const { AxePuppeteer } = require('@axe-core/puppeteer');
const fs = require('fs');
const path = require('path');

const BASE_URL = process.env.BASE_URL || 'http://localhost:8081';

class AccessibilityTester {
  constructor() {
    this.browser = null;
    this.results = {
      violations: [],
      passes: [],
      incomplete: [],
      inapplicable: []
    };
  }

  async init() {
    this.browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async testPage(url) {
    console.log(`Testing accessibility for: ${url}`);
    
    const page = await this.browser.newPage();
    await page.goto(url, { waitUntil: 'networkidle0' });

    try {
      // Run axe-core accessibility tests
      const results = await new AxePuppeteer(page).analyze();
      
      return {
        url,
        violations: results.violations,
        passes: results.passes,
        incomplete: results.incomplete,
        inapplicable: results.inapplicable,
        summary: {
          violationCount: results.violations.length,
          passCount: results.passes.length,
          incompleteCount: results.incomplete.length,
          inapplicableCount: results.inapplicable.length
        }
      };
    } catch (error) {
      console.error(`Error testing ${url}:`, error.message);
      return {
        url,
        error: error.message
      };
    } finally {
      await page.close();
    }
  }

  async generateReport() {
    const timestamp = new Date().toISOString();
    const reportPath = path.join(__dirname, '..', 'reports', `accessibility-${timestamp.split('T')[0]}.json`);
    
    // Ensure reports directory exists
    const reportsDir = path.dirname(reportPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    const report = {
      timestamp,
      baseUrl: BASE_URL,
      results: this.results
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`Accessibility report generated: ${reportPath}`);
    
    return report;
  }

  printSummary() {
    console.log('\n=== Accessibility Test Summary ===');
    
    this.results.forEach(result => {
      if (result.error) {
        console.log(`\n${result.url}: ERROR - ${result.error}`);
        return;
      }
      
      console.log(`\n${result.url}:`);
      console.log(`  Violations: ${result.summary.violationCount}`);
      console.log(`  Passes: ${result.summary.passCount}`);
      console.log(`  Incomplete: ${result.summary.incompleteCount}`);
      
      if (result.violations.length > 0) {
        console.log('\n  Critical Issues:');
        result.violations.forEach(violation => {
          console.log(`    - ${violation.id}: ${violation.description}`);
          console.log(`      Impact: ${violation.impact}`);
          console.log(`      Nodes affected: ${violation.nodes.length}`);
        });
      }
    });
  }

  async run() {
    try {
      await this.init();
      
      const testUrls = [
        BASE_URL,
        `${BASE_URL}/about`,
        `${BASE_URL}/services`,
        `${BASE_URL}/contact`
      ];
      
      this.results = [];
      
      for (const url of testUrls) {
        const result = await this.testPage(url);
        this.results.push(result);
      }
      
      // Generate report
      const report = await this.generateReport();
      
      // Print summary
      this.printSummary();
      
      // Check if there are any violations
      const totalViolations = this.results.reduce((total, result) => {
        return total + (result.summary?.violationCount || 0);
      }, 0);
      
      if (totalViolations > 0) {
        console.log(`\n❌ Found ${totalViolations} accessibility violations`);
        process.exit(1);
      } else {
        console.log('\n✅ No accessibility violations found');
      }
      
      return report;
      
    } catch (error) {
      console.error('Accessibility test suite failed:', error);
      throw error;
    } finally {
      await this.close();
    }
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  const tester = new AccessibilityTester();
  tester.run()
    .then(() => {
      console.log('\nAccessibility tests completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Tests failed:', error);
      process.exit(1);
    });
}

module.exports = AccessibilityTester;
