#!/usr/bin/env node

// <PERSON>ript to audit all buttons on the homepage
import { ConvexHttpClient } from "convex/browser";

async function auditAllButtons() {
  try {
    const convexUrl = process.env.VITE_CONVEX_URL || process.env.CONVEX_URL;
    
    if (!convexUrl) {
      console.error("❌ CONVEX_URL not found in environment variables");
      process.exit(1);
    }

    console.log("🔍 Auditing all homepage buttons...");
    console.log(`📡 Connecting to: ${convexUrl}`);

    const client = new ConvexHttpClient(convexUrl);

    const buttonAudit = {
      working: [],
      broken: [],
      missing: [],
    };

    // 1. Hero Section Buttons
    console.log("\n🎯 Checking Hero Section buttons...");
    try {
      const heroEn = await client.query("content:getContent", {
        identifier: "home-hero",
        language: "en"
      });
      
      if (heroEn) {
        const primaryCta = heroEn.data.ctaUrl;
        const secondaryCta = heroEn.data.secondaryCtaUrl;
        
        console.log(`  Primary CTA: "${heroEn.data.ctaText}" -> ${primaryCta}`);
        console.log(`  Secondary CTA: "${heroEn.data.secondaryCtaText}" -> ${secondaryCta}`);
        
        if (primaryCta && primaryCta !== '#' && !primaryCta.startsWith('#')) {
          buttonAudit.working.push({
            section: "Hero",
            button: heroEn.data.ctaText,
            url: primaryCta,
            type: "Primary CTA"
          });
        } else {
          buttonAudit.broken.push({
            section: "Hero",
            button: heroEn.data.ctaText,
            url: primaryCta,
            type: "Primary CTA",
            issue: "Invalid or missing URL"
          });
        }
        
        if (secondaryCta && secondaryCta !== '#' && !secondaryCta.startsWith('#')) {
          buttonAudit.working.push({
            section: "Hero",
            button: heroEn.data.secondaryCtaText,
            url: secondaryCta,
            type: "Secondary CTA"
          });
        } else {
          buttonAudit.broken.push({
            section: "Hero",
            button: heroEn.data.secondaryCtaText,
            url: secondaryCta,
            type: "Secondary CTA",
            issue: "Invalid or missing URL"
          });
        }
      }
    } catch (error) {
      console.log(`  ❌ Error checking hero: ${error.message}`);
    }

    // 2. CTA Section Button
    console.log("\n📢 Checking CTA Section button...");
    try {
      const ctaSection = await client.query("content:getContent", {
        identifier: "cta-section",
        language: "en"
      });
      
      if (ctaSection && ctaSection.data.ctaUrl) {
        console.log(`  CTA Button: "${ctaSection.data.ctaText}" -> ${ctaSection.data.ctaUrl}`);
        buttonAudit.working.push({
          section: "CTA Section",
          button: ctaSection.data.ctaText,
          url: ctaSection.data.ctaUrl,
          type: "CTA Button"
        });
      } else {
        console.log(`  ⚠️ CTA Section button has no URL configured`);
        buttonAudit.missing.push({
          section: "CTA Section",
          button: "Contact Us Today",
          type: "CTA Button",
          issue: "No URL configured in content"
        });
      }
    } catch (error) {
      console.log(`  ⚠️ CTA Section content not found or error: ${error.message}`);
      buttonAudit.missing.push({
        section: "CTA Section",
        button: "Contact Us Today",
        type: "CTA Button",
        issue: "Content not found in CMS"
      });
    }

    // 3. Navigation Links (these should be working)
    console.log("\n🧭 Checking Navigation links...");
    const navLinks = [
      { name: "Home", url: "/" },
      { name: "Services", url: "/services" },
      { name: "Network Solutions", url: "/network-solutions" },
      { name: "About", url: "/about" },
      { name: "Contact", url: "/contact" },
    ];
    
    navLinks.forEach(link => {
      console.log(`  ${link.name} -> ${link.url}`);
      buttonAudit.working.push({
        section: "Navigation",
        button: link.name,
        url: link.url,
        type: "Navigation Link"
      });
    });

    // 4. Footer Button
    console.log("\n🦶 Checking Footer button...");
    buttonAudit.missing.push({
      section: "Footer",
      button: "Get Started",
      type: "Footer CTA",
      issue: "Hardcoded button with no URL"
    });

    // Summary
    console.log("\n📊 Button Audit Summary:");
    console.log(`✅ Working buttons: ${buttonAudit.working.length}`);
    console.log(`❌ Broken buttons: ${buttonAudit.broken.length}`);
    console.log(`⚠️ Missing URLs: ${buttonAudit.missing.length}`);

    if (buttonAudit.working.length > 0) {
      console.log("\n✅ Working Buttons:");
      buttonAudit.working.forEach(btn => {
        console.log(`  - ${btn.section}: "${btn.button}" -> ${btn.url}`);
      });
    }

    if (buttonAudit.broken.length > 0) {
      console.log("\n❌ Broken Buttons:");
      buttonAudit.broken.forEach(btn => {
        console.log(`  - ${btn.section}: "${btn.button}" -> ${btn.url} (${btn.issue})`);
      });
    }

    if (buttonAudit.missing.length > 0) {
      console.log("\n⚠️ Buttons Missing URLs:");
      buttonAudit.missing.forEach(btn => {
        console.log(`  - ${btn.section}: "${btn.button}" (${btn.issue})`);
      });
    }

    // Recommendations
    console.log("\n💡 Recommendations:");
    if (buttonAudit.broken.length > 0 || buttonAudit.missing.length > 0) {
      console.log("1. Fix CTA Section button by adding URL to content");
      console.log("2. Fix Footer button by adding proper Link component");
      console.log("3. Create missing content for CTA section if needed");
    } else {
      console.log("All buttons are working correctly!");
    }

    return {
      working: buttonAudit.working.length,
      broken: buttonAudit.broken.length,
      missing: buttonAudit.missing.length,
      total: buttonAudit.working.length + buttonAudit.broken.length + buttonAudit.missing.length
    };

  } catch (error) {
    console.error("❌ Failed to audit buttons:", error);
    return null;
  }
}

// Run the audit
auditAllButtons().then(result => {
  if (result) {
    const success = result.broken === 0 && result.missing === 0;
    console.log(`\n🎯 Audit Complete: ${result.working}/${result.total} buttons working properly`);
    process.exit(success ? 0 : 1);
  } else {
    process.exit(1);
  }
});
