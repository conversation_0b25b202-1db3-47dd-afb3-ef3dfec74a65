#!/usr/bin/env node

/**
 * Website Monitoring Script
 * 
 * This script monitors the website's health, performance, and availability
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

const MONITORING_CONFIG = {
  urls: [
    'https://officetech-guinea.com',
    'https://officetech-guinea.com/about',
    'https://officetech-guinea.com/services',
    'https://officetech-guinea.com/contact'
  ],
  interval: 60000, // 1 minute
  timeout: 10000,  // 10 seconds
  retries: 3,
  alertThresholds: {
    responseTime: 5000, // 5 seconds
    uptime: 99.9,       // 99.9%
    sslExpiry: 30       // 30 days
  }
};

class WebsiteMonitor {
  constructor() {
    this.metrics = {
      uptime: new Map(),
      responseTime: new Map(),
      errors: [],
      lastCheck: null
    };
    this.isRunning = false;
  }

  async checkUrl(url) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;
      
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: 'GET',
        timeout: MONITORING_CONFIG.timeout,
        headers: {
          'User-Agent': 'OfficeTech-Monitor/1.0'
        }
      };

      const req = client.request(options, (res) => {
        const responseTime = Date.now() - startTime;
        
        resolve({
          url,
          status: res.statusCode,
          responseTime,
          success: res.statusCode >= 200 && res.statusCode < 400,
          headers: res.headers,
          timestamp: new Date().toISOString()
        });
      });

      req.on('error', (error) => {
        const responseTime = Date.now() - startTime;
        resolve({
          url,
          status: 0,
          responseTime,
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      });

      req.on('timeout', () => {
        req.destroy();
        const responseTime = Date.now() - startTime;
        resolve({
          url,
          status: 0,
          responseTime,
          success: false,
          error: 'Request timeout',
          timestamp: new Date().toISOString()
        });
      });

      req.end();
    });
  }

  async checkSSLCertificate(hostname) {
    return new Promise((resolve) => {
      const options = {
        hostname,
        port: 443,
        method: 'GET',
        timeout: MONITORING_CONFIG.timeout
      };

      const req = https.request(options, (res) => {
        const cert = res.socket.getPeerCertificate();
        const expiryDate = new Date(cert.valid_to);
        const daysUntilExpiry = Math.floor((expiryDate - new Date()) / (1000 * 60 * 60 * 24));
        
        resolve({
          hostname,
          expiryDate: expiryDate.toISOString(),
          daysUntilExpiry,
          issuer: cert.issuer,
          subject: cert.subject,
          valid: daysUntilExpiry > 0
        });
      });

      req.on('error', (error) => {
        resolve({
          hostname,
          error: error.message,
          valid: false
        });
      });

      req.end();
    });
  }

  async runHealthCheck() {
    console.log(`[${new Date().toISOString()}] Running health check...`);
    
    const results = [];
    
    // Check each URL
    for (const url of MONITORING_CONFIG.urls) {
      let result = null;
      let attempts = 0;
      
      while (attempts < MONITORING_CONFIG.retries && (!result || !result.success)) {
        attempts++;
        result = await this.checkUrl(url);
        
        if (!result.success && attempts < MONITORING_CONFIG.retries) {
          console.log(`Retrying ${url} (attempt ${attempts + 1}/${MONITORING_CONFIG.retries})`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
      
      results.push(result);
      this.updateMetrics(result);
    }
    
    // Check SSL certificates
    const uniqueHostnames = [...new Set(MONITORING_CONFIG.urls.map(url => new URL(url).hostname))];
    for (const hostname of uniqueHostnames) {
      const sslResult = await this.checkSSLCertificate(hostname);
      this.checkSSLAlert(sslResult);
    }
    
    this.metrics.lastCheck = new Date().toISOString();
    this.generateReport(results);
    
    return results;
  }

  updateMetrics(result) {
    const { url, success, responseTime } = result;
    
    // Update uptime metrics
    if (!this.metrics.uptime.has(url)) {
      this.metrics.uptime.set(url, { total: 0, successful: 0 });
    }
    
    const uptimeData = this.metrics.uptime.get(url);
    uptimeData.total++;
    if (success) {
      uptimeData.successful++;
    }
    
    // Update response time metrics
    if (!this.metrics.responseTime.has(url)) {
      this.metrics.responseTime.set(url, []);
    }
    
    const responseTimeData = this.metrics.responseTime.get(url);
    responseTimeData.push(responseTime);
    
    // Keep only last 100 measurements
    if (responseTimeData.length > 100) {
      responseTimeData.shift();
    }
    
    // Check for alerts
    this.checkAlerts(result);
  }

  checkAlerts(result) {
    const { url, success, responseTime, status } = result;
    
    // Response time alert
    if (responseTime > MONITORING_CONFIG.alertThresholds.responseTime) {
      this.addAlert('HIGH_RESPONSE_TIME', `${url} response time: ${responseTime}ms`);
    }
    
    // Downtime alert
    if (!success) {
      this.addAlert('DOWNTIME', `${url} is down (status: ${status})`);
    }
    
    // Uptime alert
    const uptimeData = this.metrics.uptime.get(url);
    if (uptimeData && uptimeData.total > 10) {
      const uptimePercentage = (uptimeData.successful / uptimeData.total) * 100;
      if (uptimePercentage < MONITORING_CONFIG.alertThresholds.uptime) {
        this.addAlert('LOW_UPTIME', `${url} uptime: ${uptimePercentage.toFixed(2)}%`);
      }
    }
  }

  checkSSLAlert(sslResult) {
    const { hostname, daysUntilExpiry, valid } = sslResult;
    
    if (!valid) {
      this.addAlert('SSL_INVALID', `SSL certificate for ${hostname} is invalid`);
    } else if (daysUntilExpiry <= MONITORING_CONFIG.alertThresholds.sslExpiry) {
      this.addAlert('SSL_EXPIRY', `SSL certificate for ${hostname} expires in ${daysUntilExpiry} days`);
    }
  }

  addAlert(type, message) {
    const alert = {
      type,
      message,
      timestamp: new Date().toISOString()
    };
    
    this.metrics.errors.push(alert);
    console.log(`🚨 ALERT [${type}]: ${message}`);
    
    // Keep only last 100 alerts
    if (this.metrics.errors.length > 100) {
      this.metrics.errors.shift();
    }
    
    // Send notification (implement as needed)
    this.sendNotification(alert);
  }

  sendNotification(alert) {
    // Implement notification logic here
    // Examples: Slack webhook, email, SMS, etc.
    
    if (process.env.SLACK_WEBHOOK_URL) {
      this.sendSlackNotification(alert);
    }
  }

  async sendSlackNotification(alert) {
    const payload = {
      text: `🚨 Website Alert: ${alert.message}`,
      channel: '#monitoring',
      username: 'Website Monitor',
      icon_emoji: ':warning:'
    };
    
    try {
      const response = await fetch(process.env.SLACK_WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        console.error('Failed to send Slack notification:', response.statusText);
      }
    } catch (error) {
      console.error('Error sending Slack notification:', error.message);
    }
  }

  generateReport(results) {
    console.log('\n=== Health Check Report ===');
    
    results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const responseTime = result.responseTime;
      console.log(`${status} ${result.url} - ${result.status} (${responseTime}ms)`);
      
      if (!result.success && result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
    
    // Calculate overall metrics
    const totalChecks = results.length;
    const successfulChecks = results.filter(r => r.success).length;
    const overallUptime = (successfulChecks / totalChecks) * 100;
    const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / totalChecks;
    
    console.log('\n=== Overall Metrics ===');
    console.log(`Uptime: ${overallUptime.toFixed(2)}%`);
    console.log(`Average Response Time: ${avgResponseTime.toFixed(0)}ms`);
    console.log(`Total Alerts: ${this.metrics.errors.length}`);
    
    // Save metrics to file
    this.saveMetrics();
  }

  saveMetrics() {
    const metricsPath = path.join(__dirname, '..', 'reports', 'monitoring-metrics.json');
    
    // Ensure reports directory exists
    const reportsDir = path.dirname(metricsPath);
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    const metricsData = {
      lastUpdate: new Date().toISOString(),
      uptime: Object.fromEntries(this.metrics.uptime),
      responseTime: Object.fromEntries(this.metrics.responseTime),
      recentErrors: this.metrics.errors.slice(-10),
      summary: this.generateSummary()
    };
    
    fs.writeFileSync(metricsPath, JSON.stringify(metricsData, null, 2));
  }

  generateSummary() {
    const summary = {};
    
    for (const [url, uptimeData] of this.metrics.uptime) {
      const responseTimeData = this.metrics.responseTime.get(url) || [];
      const avgResponseTime = responseTimeData.length > 0 
        ? responseTimeData.reduce((sum, time) => sum + time, 0) / responseTimeData.length 
        : 0;
      
      summary[url] = {
        uptime: uptimeData.total > 0 ? (uptimeData.successful / uptimeData.total) * 100 : 0,
        averageResponseTime: Math.round(avgResponseTime),
        totalChecks: uptimeData.total,
        successfulChecks: uptimeData.successful
      };
    }
    
    return summary;
  }

  start() {
    if (this.isRunning) {
      console.log('Monitor is already running');
      return;
    }
    
    this.isRunning = true;
    console.log('Starting website monitor...');
    console.log(`Monitoring URLs: ${MONITORING_CONFIG.urls.join(', ')}`);
    console.log(`Check interval: ${MONITORING_CONFIG.interval / 1000} seconds`);
    
    // Run initial check
    this.runHealthCheck();
    
    // Schedule periodic checks
    this.intervalId = setInterval(() => {
      this.runHealthCheck();
    }, MONITORING_CONFIG.interval);
  }

  stop() {
    if (!this.isRunning) {
      console.log('Monitor is not running');
      return;
    }
    
    this.isRunning = false;
    clearInterval(this.intervalId);
    console.log('Website monitor stopped');
  }
}

// CLI interface
if (require.main === module) {
  const monitor = new WebsiteMonitor();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'start':
      monitor.start();
      break;
    case 'check':
      monitor.runHealthCheck().then(() => process.exit(0));
      break;
    case 'stop':
      monitor.stop();
      process.exit(0);
      break;
    default:
      console.log('Usage: node monitor.js [start|check|stop]');
      console.log('  start - Start continuous monitoring');
      console.log('  check - Run a single health check');
      console.log('  stop  - Stop monitoring');
      process.exit(1);
  }
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\nReceived SIGINT, stopping monitor...');
    monitor.stop();
    process.exit(0);
  });
}

module.exports = WebsiteMonitor;
