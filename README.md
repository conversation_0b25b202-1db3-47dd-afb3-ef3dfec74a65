# OfficeTech Guinea - Dynamic Content Management Website

A modern, dynamic website for OfficeTech Guinea with a comprehensive admin portal for content management. Built with React, TypeScript, Convex database, and Clerk authentication.

## 🚀 Features

### Public Website
- **Multi-language Support**: English and Spanish
- **Dynamic Content**: All text and images are editable through admin interface
- **Responsive Design**: Mobile-first approach with modern UI
- **Service Pages**: Network Solutions, Domestic Services, Training Programs, Product Reselling
- **Contact Forms**: Lead capture with backend integration
- **SEO Optimized**: Meta tags, structured data, and performance optimized

### Admin Portal
- **Role-based Access Control**: Super Admin, Admin, Content Editor, Viewer roles
- **Content Management**: Edit all website content without touching code
- **Media Management**: Upload, organize, and manage images and files
- **User Management**: Manage user roles and permissions
- **Analytics Dashboard**: Track content performance and user engagement
- **Translation Management**: Manage content in multiple languages
- **Version Control**: Content versioning with rollback capabilities

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **UI Components**: Shadcn/ui, Tailwind CSS
- **Database**: Convex (real-time database)
- **Authentication**: Clerk
- **Routing**: React Router
- **State Management**: TanStack Query
- **Icons**: Lucide React
- **Testing**: Vitest, Playwright, Testing Library
- **Deployment**: Vercel (recommended), Netlify, Docker

## 📋 Prerequisites

- Node.js 18+
- npm (recommended) or yarn/pnpm
- Convex account ([convex.dev](https://convex.dev))
- Clerk account ([clerk.com](https://clerk.com))

## 🚀 Getting Started

### 1. Clone and Install

```bash
git clone https://github.com/jayb967/officetech-eg.git
cd officetech-eg
npm install
```

### 2. Environment Setup

Create a `.env.local` file in the root directory:

```env
# Convex
VITE_CONVEX_URL=your_convex_deployment_url
CONVEX_DEPLOYMENT=dev:your-deployment-name

# Clerk
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key

# App Configuration
VITE_APP_NAME="OfficeTech Guinea"
VITE_APP_DESCRIPTION="Leading technology and security solutions company in Equatorial Guinea"
```

### 3. Database Setup

```bash
# Initialize Convex (first time setup)
npx convex dev

# This will:
# - Create a new Convex project (if needed)
# - Deploy your schema and functions
# - Provide you with the VITE_CONVEX_URL
# - Start the development server
```

### 4. Authentication Setup

1. Create a Clerk application at [clerk.com](https://clerk.com)
2. Get your publishable key and secret key from the Clerk dashboard
3. Add them to your `.env.local` file
4. Configure Clerk settings:
   - Enable email/password authentication
   - Set up social providers (optional)
   - Configure user roles and permissions

### 5. Run Development Server

```bash
npm run dev
```

Visit `http://localhost:8080` to see the website.

## 🧪 Testing

### Unit Tests
```bash
# Run unit tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in UI mode
npm run test:ui
```

### Integration Tests
```bash
# Run integration tests
npm run test:integration
```

### End-to-End Tests
```bash
# Install Playwright browsers (first time only)
npx playwright install

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui
```

### Performance & SEO Tests
```bash
# Run SEO tests
npm run test:seo

# Run accessibility tests
npm run test:accessibility

# Run Lighthouse audit (requires server running)
npm run test:lighthouse

# Run all tests
npm run test:all
```

## 🏗️ Building

### Development Build
```bash
npm run build:dev
```

### Production Build
```bash
npm run build:production
```

### Analyze Bundle Size
```bash
npm run build:analyze
```

## 🚀 Deployment

### Vercel (Recommended)

**Automatic Deployment:**
1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard:
   - `VITE_CLERK_PUBLISHABLE_KEY`
   - `VITE_CONVEX_URL`
3. Deploy automatically on push to main branch

**Manual Deployment:**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to staging
vercel

# Deploy to production
vercel --prod
```

### Other Platforms
- **Netlify**: See `docs/deployment.md` for detailed instructions
- **Docker**: Use included `Dockerfile` and `docker-compose.yml`
- **AWS S3**: Use deployment scripts in `scripts/` folder

For detailed deployment instructions, see [docs/deployment.md](docs/deployment.md).

## 🔧 Development

### Project Structure
```
src/
├── components/          # Reusable UI components
│   ├── admin/          # Admin portal components
│   ├── auth/           # Authentication components
│   └── ui/             # Base UI components (shadcn/ui)
├── pages/              # Page components
├── contexts/           # React contexts
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries
├── types/              # TypeScript type definitions
└── utils/              # Utility functions

convex/                 # Convex backend functions
├── schema.ts           # Database schema
├── auth.config.ts      # Authentication configuration
└── *.ts                # Database functions
```

### Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server |
| `npm run build` | Build for production |
| `npm run build:dev` | Build for development |
| `npm run build:production` | Build with production optimizations |
| `npm run build:analyze` | Build and analyze bundle size |
| `npm run preview` | Preview production build locally |
| `npm run lint` | Run ESLint |
| `npm test` | Run unit tests |
| `npm run test:ui` | Run tests with UI |
| `npm run test:coverage` | Run tests with coverage |
| `npm run test:e2e` | Run end-to-end tests |
| `npm run test:all` | Run all tests |

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `VITE_CONVEX_URL` | Convex deployment URL | Yes |
| `VITE_CLERK_PUBLISHABLE_KEY` | Clerk publishable key | Yes |
| `CLERK_SECRET_KEY` | Clerk secret key (server-side only) | Yes |
| `VITE_APP_NAME` | Application name | No |
| `VITE_APP_DESCRIPTION` | Application description | No |

## 🔒 Security

- **Authentication**: Clerk handles user authentication and session management
- **Authorization**: Role-based access control for admin features
- **Environment Variables**: Sensitive data stored in environment variables
- **HTTPS**: All deployments use HTTPS by default
- **Security Headers**: CSP, HSTS, and other security headers configured

## 🐛 Troubleshooting

### Common Issues

**Build Errors:**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check Node.js version
node --version  # Should be 18+
```

**Environment Variable Issues:**
```bash
# Verify variables are loaded
echo $VITE_CLERK_PUBLISHABLE_KEY

# Check if .env.local exists
ls -la .env.local
```

**Convex Connection Issues:**
```bash
# Check Convex status
npx convex dev --help

# Verify deployment
npx convex dashboard
```

**Test Failures:**
- Some tests may fail initially due to missing components or outdated mocks
- Run `npm run test:ui` to debug failing tests interactively
- E2E tests require the development server to be running

## 📚 Documentation

- [Deployment Guide](docs/deployment.md) - Detailed deployment instructions
- [SEO & Performance Guide](docs/SEO_PERFORMANCE_GUIDE.md) - SEO and performance optimization
- [Monitoring Guide](docs/monitoring.md) - Application monitoring and logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software. All rights reserved.

## 📞 Support

For support and questions:
- Create an issue in the GitHub repository
- Contact the development team
- Check the documentation in the `docs/` folder

---

**Note**: This project is configured for deployment on Vercel with proper environment variable handling. The current Vite setup is secure and production-ready for client-side applications using Clerk and Convex.
