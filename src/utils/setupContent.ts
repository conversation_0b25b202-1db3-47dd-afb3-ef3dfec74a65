// Automated content setup utility
// This creates the essential content entries from content.md

import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export const useContentSetup = () => {
  const upsertContent = useMutation(api.content.upsertContent);
  const getContentTypeByName = useQuery(api.contentTypes.getContentTypeByName, { name: "service_card" });

  const setupAllContent = async () => {
    try {
      if (!getContentTypeByName) {
        throw new Error("service_card content type not found");
      }

      const results = [];

      // Create Cybersecurity content
      results.push(await createCybersecurityContent());

      // Create Electronic Security content
      results.push(await createElectronicSecurityContent());

      // Create Network Solutions content
      results.push(await createNetworkSolutionsContent());

      return results;
    } catch (error) {
      console.error("Error setting up content:", error);
      throw error;
    }
  };

  const createCybersecurityContent = async () => {

    // Create Spanish Cybersecurity content
    await upsertContent({
      identifier: "service-cybersecurity",
      language: "es",
      contentTypeId: getContentTypeByName._id,
      data: {
        title: "Ciberseguridad",
        slug: "ciberseguridad",
        description: "Protegiendo tu Mundo Digital en Cada Fase. En la era digital, la ciberseguridad se ha convertido en un pilar fundamental para proteger la información, los sistemas y las redes contra amenazas cibernéticas.",
      fullDescription: `<h2>¿Qué es la Ciberseguridad?</h2>
<p>La ciberseguridad es el conjunto de prácticas, tecnologías y procesos diseñados para proteger sistemas, redes, dispositivos y datos de accesos no autorizados, ataques maliciosos o daños. Su objetivo es garantizar la confidencialidad, integridad y disponibilidad de la información en un entorno digital cada vez más interconectado.</p>

<h3>Las Fases de la Ciberseguridad</h3>
<h4>1. Prevención</h4>
<p>La prevención es la primera línea de defensa. En esta fase, se implementan medidas proactivas para evitar posibles ataques:</p>
<ul>
<li>Firewalls y antivirus: Herramientas esenciales para bloquear accesos no autorizados y detectar software malicioso.</li>
<li>Actualizaciones de software: Mantener sistemas y aplicaciones al día para corregir vulnerabilidades conocidas.</li>
<li>Educación y concienciación: Capacitar a los usuarios para identificar amenazas como phishing o ingeniería social.</li>
</ul>

<h4>2. Detección</h4>
<p>A pesar de las medidas preventivas, ningún sistema es 100% inmune. La detección temprana de amenazas es crucial:</p>
<ul>
<li>Monitoreo continuo: Uso de herramientas de inteligencia artificial y machine learning para identificar comportamientos sospechosos.</li>
<li>Sistemas de detección de intrusos (IDS): Alertan sobre actividades anómalas en la red.</li>
<li>Análisis de logs: Revisión de registros para identificar patrones de ataque.</li>
</ul>

<h4>3. Respuesta</h4>
<p>Cuando se detecta una amenaza, es fundamental actuar rápidamente:</p>
<ul>
<li>Contención: Aislar sistemas afectados para evitar la propagación del ataque.</li>
<li>Eradicación: Eliminar la causa del problema, como malware o vulnerabilidades explotadas.</li>
<li>Comunicación: Informar a las partes interesadas y, en algunos casos, a las autoridades competentes.</li>
</ul>

<h4>4. Recuperación</h4>
<p>Después de un incidente, es esencial restaurar la normalidad y aprender de lo ocurrido:</p>
<ul>
<li>Restauración de datos: Recuperar información a partir de copias de seguridad.</li>
<li>Evaluación post-incidente: Analizar qué sucedió y cómo se puede mejorar la seguridad.</li>
<li>Refuerzo de medidas: Implementar mejoras para evitar futuros ataques similares.</li>
</ul>

<h4>5. Mejora Continua</h4>
<p>La ciberseguridad no es un proceso estático, sino un ciclo constante de mejora:</p>
<ul>
<li>Auditorías regulares: Evaluar la efectividad de las medidas de seguridad.</li>
<li>Adaptación a nuevas amenazas: Mantenerse al tanto de las últimas tendencias en ciberataques.</li>
<li>Innovación tecnológica: Adoptar nuevas herramientas y metodologías para fortalecer la protección.</li>
</ul>`,
        icon: "Shield",
        features: ["Prevención de amenazas", "Detección temprana", "Respuesta rápida", "Recuperación completa", "Mejora continua"],
        ctaText: "Protege tu negocio",
        ctaUrl: "/contact"
      },
      status: "published"
    });

    // Create English Cybersecurity content
    await upsertContent({
      identifier: "service-cybersecurity",
      language: "en",
      contentTypeId: getContentTypeByName._id,
      data: {
        title: "Cybersecurity",
        slug: "cybersecurity",
        description: "Protecting Your Digital World at Every Stage. In the digital age, cybersecurity has become a fundamental pillar for protecting information, systems, and networks against cyber threats.",
      fullDescription: `<h2>What is Cybersecurity?</h2>
<p>Cybersecurity is the set of practices, technologies, and processes designed to protect systems, networks, devices, and data from unauthorized access, malicious attacks, or damage. Its goal is to ensure the confidentiality, integrity, and availability of information in an increasingly interconnected digital environment.</p>

<h3>The Phases of Cybersecurity</h3>
<h4>1. Prevention</h4>
<p>Prevention is the first line of defense. In this phase, proactive measures are implemented to prevent potential attacks:</p>
<ul>
<li>Firewalls and antivirus software: Essential tools to block unauthorized access and detect malicious software.</li>
<li>Software updates: Keeping systems and applications up to date to fix known vulnerabilities.</li>
<li>Education and awareness: Training users to identify threats like phishing or social engineering.</li>
</ul>

<h4>2. Detection</h4>
<p>Despite preventive measures, no system is 100% immune. Early threat detection is crucial:</p>
<ul>
<li>Continuous monitoring: Using artificial intelligence and machine learning tools to identify suspicious behavior.</li>
<li>Intrusion detection systems (IDS): Alert about anomalous activities on the network.</li>
<li>Log analysis: Reviewing records to identify attack patterns.</li>
</ul>

<h4>3. Response</h4>
<p>When a threat is detected, it is essential to act quickly:</p>
<ul>
<li>Containment: Isolating affected systems to prevent attack propagation.</li>
<li>Eradication: Eliminating the cause of the problem, such as malware or exploited vulnerabilities.</li>
<li>Communication: Informing stakeholders and, in some cases, competent authorities.</li>
</ul>

<h4>4. Recovery</h4>
<p>After an incident, it is essential to restore normalcy and learn from what happened:</p>
<ul>
<li>Data restoration: Recovering information from backups.</li>
<li>Post-incident evaluation: Analyzing what happened and how security can be improved.</li>
<li>Reinforcement of measures: Implementing improvements to prevent future similar attacks.</li>
</ul>

<h4>5. Continuous Improvement</h4>
<p>Cybersecurity is not a static process but a constant cycle of improvement:</p>
<ul>
<li>Regular audits: Assessing the effectiveness of security measures.</li>
<li>Adaptation to new threats: Staying informed about the latest trends in cyberattacks.</li>
<li>Technological innovation: Adopting new tools and methodologies to strengthen protection.</li>
</ul>`,
        icon: "Shield",
        features: ["Threat Prevention", "Early Detection", "Rapid Response", "Complete Recovery", "Continuous Improvement"],
        ctaText: "Protect Your Business",
        ctaUrl: "/contact"
      },
      status: "published"
    });

    return "Cybersecurity content created successfully";
  };

  const createElectronicSecurityContent = async () => {
    // Create Spanish Electronic Security content
    await upsertContent({
      identifier: "service-electronic-security",
      language: "es",
      contentTypeId: getContentTypeByName._id,
      data: {
        title: "Seguridad Electrónica",
        slug: "seguridad-electronica",
        description: "En Officetech nos especializamos en ofrecer soluciones integrales de seguridad electrónica para proteger lo que más importa: tu hogar, tu negocio y tu familia.",
        fullDescription: `<h2>Productos Destacados</h2>
<h3>Cámaras de Seguridad</h3>
<ul>
<li>Cámaras IP con visión nocturna y resolución 4K.</li>
<li>Cámaras inalámbricas para instalación fácil y rápida.</li>
<li>Cámaras con detección de movimiento y alertas en tiempo real.</li>
</ul>

<h3>Sistemas de Alarmas</h3>
<ul>
<li>Alarmas para hogar y negocio con sensores de movimiento y apertura.</li>
<li>Alarmas inalámbricas con conexión a aplicaciones móviles.</li>
</ul>

<h3>Control de Acceso</h3>
<ul>
<li>Cerraduras inteligentes y controles de acceso biométricos.</li>
<li>Tarjetas RFID y lectores de huellas digitales.</li>
</ul>

<h3>Servicios</h3>
<ul>
<li>Instalación profesional de sistemas de seguridad.</li>
<li>Monitoreo 24/7 para alarmas y cámaras.</li>
<li>Mantenimiento y soporte técnico especializado.</li>
</ul>`,
        icon: "Camera",
        features: ["Cámaras de Seguridad", "Sistemas de Alarmas", "Control de Acceso", "Monitoreo 24/7"],
        ctaText: "Solicitar Cotización",
        ctaUrl: "/contact"
      },
      status: "published"
    });

    // Create English Electronic Security content
    await upsertContent({
      identifier: "service-electronic-security",
      language: "en",
      contentTypeId: getContentTypeByName._id,
      data: {
        title: "Electronic Security",
        slug: "electronic-security",
        description: "At Officetech we specialize in offering comprehensive electronic security solutions to protect what matters most: your home, your business and your family.",
        fullDescription: `<h2>Featured Products</h2>
<h3>Security Cameras</h3>
<ul>
<li>IP cameras with night vision and 4K resolution.</li>
<li>Wireless cameras for easy and fast installation.</li>
<li>Cameras with motion detection and real-time alerts.</li>
</ul>

<h3>Alarm Systems</h3>
<ul>
<li>Home and business alarms with motion and opening sensors.</li>
<li>Wireless alarms with mobile app connectivity.</li>
</ul>

<h3>Access Control</h3>
<ul>
<li>Smart locks and biometric access controls.</li>
<li>RFID cards and fingerprint readers.</li>
</ul>

<h3>Services</h3>
<ul>
<li>Professional installation of security systems.</li>
<li>24/7 monitoring for alarms and cameras.</li>
<li>Specialized maintenance and technical support.</li>
</ul>`,
        icon: "Camera",
        features: ["Security Cameras", "Alarm Systems", "Access Control", "24/7 Monitoring"],
        ctaText: "Request Quote",
        ctaUrl: "/contact"
      },
      status: "published"
    });

    return "Electronic Security content created successfully";
  };

  const createNetworkSolutionsContent = async () => {
    // Create Spanish Network Solutions content
    await upsertContent({
      identifier: "service-network-solutions",
      language: "es",
      contentTypeId: getContentTypeByName._id,
      data: {
        title: "Soluciones de Red",
        slug: "soluciones-de-red",
        description: "Conectividad Internacional: MPLS, SD-WAN y VPL. Ofrecemos soluciones avanzadas para garantizar que tu empresa esté siempre conectada.",
        fullDescription: `<h2>Conectividad Internacional: MPLS, SD-WAN y VPL</h2>
<p>En un mundo cada vez más interconectado, la necesidad de una red confiable, segura y de alto rendimiento es fundamental para el éxito de cualquier negocio.</p>

<h3>MPLS (Multi-Protocol Label Switching)</h3>
<p>Ideal para empresas que requieren una red privada y segura. MPLS optimiza el tráfico de datos, reduciendo la latencia y mejorando la calidad del servicio. Perfecto para aplicaciones críticas como VoIP, videoconferencias y transferencia de grandes volúmenes de datos.</p>

<h3>SD-WAN (Software-Defined Wide Area Network)</h3>
<p>Una solución moderna y flexible que permite gestionar múltiples conexiones de red desde una plataforma centralizada. SD-WAN mejora el rendimiento, reduce costos y facilita la implementación de políticas de seguridad.</p>

<h3>VPL (Virtual Private LAN)</h3>
<p>Conecta múltiples ubicaciones como si estuvieran en la misma red local, proporcionando comunicación segura y eficiente entre sucursales.</p>`,
        icon: "Network",
        features: ["MPLS", "SD-WAN", "VPL", "Conectividad Internacional"],
        ctaText: "Consultar Solución",
        ctaUrl: "/contact"
      },
      status: "published"
    });

    // Create English Network Solutions content
    await upsertContent({
      identifier: "service-network-solutions",
      language: "en",
      contentTypeId: getContentTypeByName._id,
      data: {
        title: "Network Solutions",
        slug: "network-solutions",
        description: "International Connectivity: MPLS, SD-WAN and VPL. We offer advanced solutions to ensure your company is always connected.",
        fullDescription: `<h2>International Connectivity: MPLS, SD-WAN and VPL</h2>
<p>In an increasingly interconnected world, the need for a reliable, secure and high-performance network is fundamental to the success of any business.</p>

<h3>MPLS (Multi-Protocol Label Switching)</h3>
<p>Ideal for companies that require a private and secure network. MPLS optimizes data traffic, reducing latency and improving service quality. Perfect for critical applications such as VoIP, video conferencing and large data transfers.</p>

<h3>SD-WAN (Software-Defined Wide Area Network)</h3>
<p>A modern and flexible solution that allows managing multiple network connections from a centralized platform. SD-WAN improves performance, reduces costs and facilitates the implementation of security policies.</p>

<h3>VPL (Virtual Private LAN)</h3>
<p>Connects multiple locations as if they were on the same local network, providing secure and efficient communication between branches.</p>`,
        icon: "Network",
        features: ["MPLS", "SD-WAN", "VPL", "International Connectivity"],
        ctaText: "Consult Solution",
        ctaUrl: "/contact"
      },
      status: "published"
    });

    return "Network Solutions content created successfully";
  };

  return {
    setupAllContent,
    createCybersecurityContent,
    createElectronicSecurityContent,
    createNetworkSolutionsContent
  };
};
