import { translations, Language } from "@/i18n/translations";

export interface TranslationItem {
  key: string;
  en: string;
  es: string;
  category: string;
}

// Flatten nested translation object into a flat array
export const flattenTranslations = (
  obj: any, 
  prefix = "", 
  language: Language = "en"
): TranslationItem[] => {
  const result: TranslationItem[] = [];
  
  Object.keys(obj).forEach(key => {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    const value = obj[key];
    
    if (typeof value === 'string') {
      const enValue = getNestedValue(translations.en, fullKey) || value;
      const esValue = getNestedValue(translations.es, fullKey) || value;
      
      result.push({
        key: fullKey,
        en: enValue,
        es: esValue,
        category: prefix || 'root',
      });
    } else if (typeof value === 'object' && value !== null) {
      result.push(...flattenTranslations(value, fullKey, language));
    }
  });
  
  return result;
};

// Get nested value from object using dot notation
export const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
};

// Set nested value in object using dot notation
export const setNestedValue = (obj: any, path: string, value: any): void => {
  const keys = path.split('.');
  const lastKey = keys.pop();
  
  if (!lastKey) return;
  
  let current = obj;
  for (const key of keys) {
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[lastKey] = value;
};

// Get all translation items
export const getAllTranslations = (): TranslationItem[] => {
  return flattenTranslations(translations.en);
};

// Get translations by category
export const getTranslationsByCategory = (category: string): TranslationItem[] => {
  return getAllTranslations().filter(item => item.category === category);
};

// Get translation categories
export const getTranslationCategories = (): string[] => {
  const categories = new Set<string>();
  getAllTranslations().forEach(item => {
    categories.add(item.category);
  });
  return Array.from(categories).sort();
};

// Search translations
export const searchTranslations = (
  searchTerm: string, 
  category?: string
): TranslationItem[] => {
  let items = getAllTranslations();
  
  if (category && category !== 'all') {
    items = items.filter(item => item.category === category);
  }
  
  if (!searchTerm) return items;
  
  const term = searchTerm.toLowerCase();
  return items.filter(item =>
    item.key.toLowerCase().includes(term) ||
    item.en.toLowerCase().includes(term) ||
    item.es.toLowerCase().includes(term)
  );
};

// Get translation statistics
export const getTranslationStats = () => {
  const allItems = getAllTranslations();
  const total = allItems.length;
  const translated = allItems.filter(item => 
    item.es && item.es !== item.en && item.es.trim() !== ''
  ).length;
  const missing = total - translated;
  const percentage = total > 0 ? Math.round((translated / total) * 100) : 0;
  
  return {
    total,
    translated,
    missing,
    percentage,
  };
};

// Get category statistics
export const getCategoryStats = () => {
  const categories = getTranslationCategories();
  
  return categories.map(category => {
    const items = getTranslationsByCategory(category);
    const total = items.length;
    const translated = items.filter(item => 
      item.es && item.es !== item.en && item.es.trim() !== ''
    ).length;
    const percentage = total > 0 ? Math.round((translated / total) * 100) : 0;
    
    return {
      category,
      total,
      translated,
      missing: total - translated,
      percentage,
    };
  });
};

// Validate translation key format
export const isValidTranslationKey = (key: string): boolean => {
  // Key should be lowercase, use dots for nesting, and contain only letters, numbers, dots, and underscores
  const keyRegex = /^[a-z][a-z0-9_.]*[a-z0-9]$/;
  return keyRegex.test(key);
};

// Generate translation key suggestions
export const suggestTranslationKey = (text: string, category?: string): string => {
  // Convert text to a valid key format
  let key = text
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .replace(/^_|_$/g, ''); // Remove leading/trailing underscores
  
  if (category) {
    key = `${category}.${key}`;
  }
  
  return key;
};

// Export translations to JSON
export const exportTranslationsToJSON = (): string => {
  return JSON.stringify(translations, null, 2);
};

// Import translations from JSON (validation only, doesn't actually update)
export const validateTranslationImport = (jsonString: string): {
  valid: boolean;
  error?: string;
  data?: any;
} => {
  try {
    const data = JSON.parse(jsonString);
    
    // Basic validation
    if (!data.en || !data.es) {
      return {
        valid: false,
        error: 'Translation file must contain "en" and "es" objects',
      };
    }
    
    if (typeof data.en !== 'object' || typeof data.es !== 'object') {
      return {
        valid: false,
        error: 'Language objects must be valid objects',
      };
    }
    
    return {
      valid: true,
      data,
    };
  } catch (error) {
    return {
      valid: false,
      error: 'Invalid JSON format',
    };
  }
};

// Get missing translations
export const getMissingTranslations = (): TranslationItem[] => {
  return getAllTranslations().filter(item => 
    !item.es || item.es === item.en || item.es.trim() === ''
  );
};

// Get recently added translations (mock function - in real app would check timestamps)
export const getRecentTranslations = (limit = 10): TranslationItem[] => {
  // For now, just return the first few items
  // In a real implementation, this would check creation/modification timestamps
  return getAllTranslations().slice(0, limit);
};

// Format category name for display
export const formatCategoryName = (category: string): string => {
  return category
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};
