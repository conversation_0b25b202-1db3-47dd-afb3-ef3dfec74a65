/**
 * Integration test setup
 * Configures test environment for integration tests
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import { cleanup } from '@testing-library/react'

// Mock environment variables
process.env.VITE_CLERK_PUBLISHABLE_KEY = 'pk_test_mock'
process.env.VITE_CONVEX_URL = 'https://mock-convex.convex.cloud'

// Mock Clerk
const mockClerk = {
  user: {
    id: 'user_test123',
    emailAddresses: [{ emailAddress: '<EMAIL>' }],
    firstName: 'Test',
    lastName: 'User',
    publicMetadata: { role: 'viewer' }
  },
  session: {
    id: 'session_test123'
  }
}

// Mock Convex client
const mockConvexClient = {
  query: vi.fn(),
  mutation: vi.fn(),
  action: vi.fn(),
  subscribe: vi.fn(() => ({ unsubscribe: vi.fn() })),
  close: vi.fn()
}

// Global mocks
global.mockClerk = mockClerk
global.mockConvexClient = mockConvexClient

// Mock modules
vi.mock('@clerk/clerk-react', () => ({
  ClerkProvider: ({ children }: { children: React.ReactNode }) => children,
  useUser: () => ({ user: mockClerk.user, isLoaded: true }),
  useAuth: () => ({ 
    isLoaded: true, 
    isSignedIn: true, 
    userId: mockClerk.user.id,
    sessionId: mockClerk.session.id,
    getToken: vi.fn().mockResolvedValue('mock_token')
  }),
  useClerk: () => ({
    signOut: vi.fn(),
    openSignIn: vi.fn(),
    openSignUp: vi.fn()
  }),
  SignIn: () => <div data-testid="sign-in">Sign In</div>,
  SignUp: () => <div data-testid="sign-up">Sign Up</div>,
  UserButton: () => <div data-testid="user-button">User</div>
}))

vi.mock('convex/react', () => ({
  ConvexProvider: ({ children }: { children: React.ReactNode }) => children,
  useQuery: (query: any, args?: any) => {
    return mockConvexClient.query(query, args)
  },
  useMutation: (mutation: any) => {
    return (args: any) => mockConvexClient.mutation(mutation, args)
  },
  useAction: (action: any) => {
    return (args: any) => mockConvexClient.action(action, args)
  },
  useConvex: () => mockConvexClient
}))

// Mock React Router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({ pathname: '/', search: '', hash: '', state: null }),
    useParams: () => ({}),
    BrowserRouter: ({ children }: { children: React.ReactNode }) => children,
    Link: ({ children, to, ...props }: any) => (
      <a href={to} {...props}>{children}</a>
    ),
    NavLink: ({ children, to, ...props }: any) => (
      <a href={to} {...props}>{children}</a>
    )
  }
})

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
      changeLanguage: vi.fn()
    }
  }),
  Trans: ({ children }: { children: React.ReactNode }) => children,
  I18nextProvider: ({ children }: { children: React.ReactNode }) => children
}))

// Mock intersection observer
global.IntersectionObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
  root: null,
  rootMargin: '',
  thresholds: []
}))

// Mock resize observer
global.ResizeObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn()
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock sessionStorage
Object.defineProperty(window, 'sessionStorage', {
  value: localStorageMock
})

// Mock fetch
global.fetch = vi.fn()

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error
const originalConsoleWarn = console.warn

beforeAll(() => {
  // Suppress console errors/warnings during tests unless explicitly needed
  console.error = vi.fn()
  console.warn = vi.fn()
})

afterAll(() => {
  // Restore console methods
  console.error = originalConsoleError
  console.warn = originalConsoleWarn
})

beforeEach(() => {
  // Reset all mocks before each test
  vi.clearAllMocks()
  
  // Reset mock implementations
  mockConvexClient.query.mockResolvedValue(null)
  mockConvexClient.mutation.mockResolvedValue('mock_id')
  mockConvexClient.action.mockResolvedValue({})
  
  // Reset localStorage
  localStorageMock.getItem.mockReturnValue(null)
  localStorageMock.setItem.mockImplementation(() => {})
  localStorageMock.removeItem.mockImplementation(() => {})
  localStorageMock.clear.mockImplementation(() => {})
  
  // Reset fetch
  global.fetch.mockResolvedValue({
    ok: true,
    json: async () => ({}),
    text: async () => '',
    status: 200,
    statusText: 'OK'
  })
})

afterEach(() => {
  // Clean up DOM after each test
  cleanup()
  
  // Clear any timers
  vi.clearAllTimers()
})

// Helper functions for tests
export const createMockUser = (overrides = {}) => ({
  ...mockClerk.user,
  ...overrides
})

export const createMockContent = (overrides = {}) => ({
  _id: 'content_123',
  identifier: 'test-content',
  language: 'en',
  status: 'published',
  data: {
    title: 'Test Title',
    body: 'Test content body'
  },
  createdAt: Date.now(),
  updatedAt: Date.now(),
  ...overrides
})

export const createMockContentType = (overrides = {}) => ({
  _id: 'contentType_123',
  name: 'test-type',
  displayName: 'Test Type',
  description: 'A test content type',
  fields: [
    { name: 'title', type: 'text', required: true, label: 'Title' },
    { name: 'body', type: 'richtext', required: false, label: 'Body' }
  ],
  ...overrides
})

export const mockConvexQuery = (query: any, returnValue: any) => {
  mockConvexClient.query.mockImplementation((q, args) => {
    if (q === query) {
      return Promise.resolve(returnValue)
    }
    return Promise.resolve(null)
  })
}

export const mockConvexMutation = (mutation: any, returnValue: any) => {
  mockConvexClient.mutation.mockImplementation((m, args) => {
    if (m === mutation) {
      return Promise.resolve(returnValue)
    }
    return Promise.resolve('mock_id')
  })
}

// Export mocks for use in tests
export { mockClerk, mockConvexClient }
