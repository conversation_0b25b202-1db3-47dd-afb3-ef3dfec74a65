/**
 * Integration tests for user workflows
 * Tests complete user journeys and component interactions
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { BrowserRouter } from 'react-router-dom'
import { ConvexProvider } from 'convex/react'
import { ClerkProvider } from '@clerk/clerk-react'
import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock Convex client
const mockConvexClient = {
  query: vi.fn(),
  mutation: vi.fn(),
  action: vi.fn(),
  subscribe: vi.fn(() => ({ unsubscribe: vi.fn() }))
}

// Mock Clerk
const mockClerk = {
  publishableKey: 'pk_test_mock',
  user: {
    id: 'user_test123',
    emailAddresses: [{ emailAddress: '<EMAIL>' }],
    firstName: 'Test',
    lastName: 'User'
  }
}

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ClerkProvider publishableKey={mockClerk.publishableKey}>
    <ConvexProvider client={mockConvexClient as any}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </ConvexProvider>
  </ClerkProvider>
)

// Mock components
vi.mock('@/components/ContactForm', () => ({
  default: ({ onSubmit }: { onSubmit: (data: any) => void }) => (
    <form data-testid="contact-form" onSubmit={(e) => {
      e.preventDefault()
      const formData = new FormData(e.target as HTMLFormElement)
      onSubmit({
        name: formData.get('name'),
        email: formData.get('email'),
        message: formData.get('message')
      })
    }}>
      <input name="name" placeholder="Name" required />
      <input name="email" type="email" placeholder="Email" required />
      <textarea name="message" placeholder="Message" required />
      <button type="submit">Submit</button>
    </form>
  )
}))

vi.mock('@/components/ServiceCard', () => ({
  default: ({ title, description, onLearnMore }: any) => (
    <div data-testid="service-card">
      <h3>{title}</h3>
      <p>{description}</p>
      <button onClick={onLearnMore}>Learn More</button>
    </div>
  )
}))

vi.mock('@/components/admin/ContentEditor', () => ({
  default: ({ content, onSave }: any) => (
    <div data-testid="content-editor">
      <input 
        defaultValue={content?.title || ''} 
        placeholder="Title"
        onChange={(e) => onSave({ ...content, title: e.target.value })}
      />
      <textarea 
        defaultValue={content?.body || ''} 
        placeholder="Content"
        onChange={(e) => onSave({ ...content, body: e.target.value })}
      />
      <button onClick={() => onSave(content)}>Save</button>
    </div>
  )
}))

describe('User Workflow Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup default mock responses
    mockConvexClient.query.mockResolvedValue([])
    mockConvexClient.mutation.mockResolvedValue('mock_id')
  })

  describe('Contact Form Workflow', () => {
    it('should complete contact form submission workflow', async () => {
      const user = userEvent.setup()
      
      // Mock successful form submission
      mockConvexClient.mutation.mockResolvedValueOnce('submission_123')
      
      const ContactForm = (await import('@/components/ContactForm')).default
      
      render(
        <TestWrapper>
          <ContactForm />
        </TestWrapper>
      )

      // Fill out the form
      await user.type(screen.getByPlaceholderText('Name'), 'John Doe')
      await user.type(screen.getByPlaceholderText('Email'), '<EMAIL>')
      await user.type(screen.getByPlaceholderText('Message'), 'I need help with network setup')

      // Submit the form
      await user.click(screen.getByRole('button', { name: /submit/i }))

      // Verify the mutation was called with correct data
      await waitFor(() => {
        expect(mockConvexClient.mutation).toHaveBeenCalledWith(
          expect.any(Object), // API reference
          expect.objectContaining({
            name: 'John Doe',
            email: '<EMAIL>',
            message: 'I need help with network setup'
          })
        )
      })
    })

    it('should handle form validation errors', async () => {
      const user = userEvent.setup()
      
      const ContactForm = (await import('@/components/ContactForm')).default
      
      render(
        <TestWrapper>
          <ContactForm />
        </TestWrapper>
      )

      // Try to submit empty form
      await user.click(screen.getByRole('button', { name: /submit/i }))

      // Form should not submit due to HTML5 validation
      expect(mockConvexClient.mutation).not.toHaveBeenCalled()
    })

    it('should handle submission errors gracefully', async () => {
      const user = userEvent.setup()
      
      // Mock submission error
      mockConvexClient.mutation.mockRejectedValueOnce(new Error('Network error'))
      
      const ContactForm = (await import('@/components/ContactForm')).default
      
      render(
        <TestWrapper>
          <ContactForm />
        </TestWrapper>
      )

      // Fill and submit form
      await user.type(screen.getByPlaceholderText('Name'), 'John Doe')
      await user.type(screen.getByPlaceholderText('Email'), '<EMAIL>')
      await user.type(screen.getByPlaceholderText('Message'), 'Test message')
      await user.click(screen.getByRole('button', { name: /submit/i }))

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/error/i)).toBeInTheDocument()
      })
    })
  })

  describe('Service Discovery Workflow', () => {
    it('should navigate through service pages', async () => {
      const user = userEvent.setup()
      
      // Mock service data
      mockConvexClient.query.mockResolvedValue([
        {
          _id: 'service_1',
          title: 'Network Solutions',
          description: 'Enterprise networking services',
          slug: 'network-solutions'
        },
        {
          _id: 'service_2',
          title: 'Training Programs',
          description: 'Professional development courses',
          slug: 'training-programs'
        }
      ])

      const ServicesPage = (await import('@/pages/Services')).default
      
      render(
        <TestWrapper>
          <ServicesPage />
        </TestWrapper>
      )

      // Wait for services to load
      await waitFor(() => {
        expect(screen.getByText('Network Solutions')).toBeInTheDocument()
        expect(screen.getByText('Training Programs')).toBeInTheDocument()
      })

      // Click on a service
      await user.click(screen.getByText('Learn More'))

      // Should navigate to service detail page
      // (This would require proper routing setup in a real test)
    })
  })

  describe('Admin Content Management Workflow', () => {
    it('should complete content editing workflow', async () => {
      const user = userEvent.setup()
      
      // Mock admin user
      mockClerk.user = {
        ...mockClerk.user,
        publicMetadata: { role: 'admin' }
      }

      // Mock content data
      const mockContent = {
        _id: 'content_123',
        identifier: 'home-hero',
        title: 'Welcome to OfficeTech',
        body: 'Leading technology solutions'
      }

      mockConvexClient.query.mockResolvedValue(mockContent)
      mockConvexClient.mutation.mockResolvedValue('content_123')

      const ContentEditor = (await import('@/components/admin/ContentEditor')).default
      
      render(
        <TestWrapper>
          <ContentEditor contentId="content_123" />
        </TestWrapper>
      )

      // Wait for content to load
      await waitFor(() => {
        expect(screen.getByDisplayValue('Welcome to OfficeTech')).toBeInTheDocument()
      })

      // Edit content
      const titleInput = screen.getByPlaceholderText('Title')
      await user.clear(titleInput)
      await user.type(titleInput, 'Updated Welcome Message')

      // Save changes
      await user.click(screen.getByRole('button', { name: /save/i }))

      // Verify save mutation was called
      await waitFor(() => {
        expect(mockConvexClient.mutation).toHaveBeenCalledWith(
          expect.any(Object),
          expect.objectContaining({
            title: 'Updated Welcome Message'
          })
        )
      })
    })

    it('should handle unauthorized access', async () => {
      // Mock non-admin user
      mockClerk.user = {
        ...mockClerk.user,
        publicMetadata: { role: 'viewer' }
      }

      const AdminDashboard = (await import('@/pages/admin/Dashboard')).default
      
      render(
        <TestWrapper>
          <AdminDashboard />
        </TestWrapper>
      )

      // Should show access denied message
      await waitFor(() => {
        expect(screen.getByText(/access denied/i)).toBeInTheDocument()
      })
    })
  })

  describe('Language Switching Workflow', () => {
    it('should switch languages and update content', async () => {
      const user = userEvent.setup()
      
      // Mock content in different languages
      mockConvexClient.query
        .mockResolvedValueOnce({
          title: 'Welcome',
          body: 'English content'
        })
        .mockResolvedValueOnce({
          title: 'Bienvenido',
          body: 'Contenido en español'
        })

      const LanguageSwitcher = (await import('@/components/LanguageSwitcher')).default
      const HomePage = (await import('@/pages/Home')).default
      
      render(
        <TestWrapper>
          <LanguageSwitcher />
          <HomePage />
        </TestWrapper>
      )

      // Initial content should be in English
      await waitFor(() => {
        expect(screen.getByText('Welcome')).toBeInTheDocument()
      })

      // Switch to Spanish
      await user.click(screen.getByText('Español'))

      // Content should update to Spanish
      await waitFor(() => {
        expect(screen.getByText('Bienvenido')).toBeInTheDocument()
      })
    })
  })

  describe('Search and Filter Workflow', () => {
    it('should search and filter content', async () => {
      const user = userEvent.setup()
      
      // Mock search results
      mockConvexClient.query.mockResolvedValue([
        {
          _id: 'result_1',
          title: 'Network Security',
          description: 'Advanced security solutions'
        },
        {
          _id: 'result_2',
          title: 'Network Infrastructure',
          description: 'Enterprise network setup'
        }
      ])

      const SearchPage = (await import('@/pages/Search')).default
      
      render(
        <TestWrapper>
          <SearchPage />
        </TestWrapper>
      )

      // Perform search
      const searchInput = screen.getByPlaceholderText(/search/i)
      await user.type(searchInput, 'network')
      await user.click(screen.getByRole('button', { name: /search/i }))

      // Verify search results
      await waitFor(() => {
        expect(screen.getByText('Network Security')).toBeInTheDocument()
        expect(screen.getByText('Network Infrastructure')).toBeInTheDocument()
      })

      // Verify search query was called
      expect(mockConvexClient.query).toHaveBeenCalledWith(
        expect.any(Object),
        expect.objectContaining({
          searchTerm: 'network'
        })
      )
    })
  })

  describe('Error Boundary Workflow', () => {
    it('should handle component errors gracefully', async () => {
      // Mock component that throws an error
      const ErrorComponent = () => {
        throw new Error('Test error')
      }

      const ErrorBoundary = (await import('@/components/ErrorBoundary')).default
      
      // Suppress console.error for this test
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      render(
        <TestWrapper>
          <ErrorBoundary>
            <ErrorComponent />
          </ErrorBoundary>
        </TestWrapper>
      )

      // Should show error fallback UI
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument()
      })

      consoleSpy.mockRestore()
    })
  })

  describe('Performance and Loading States', () => {
    it('should show loading states during data fetching', async () => {
      // Mock delayed response
      mockConvexClient.query.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve([]), 1000))
      )

      const ServicesPage = (await import('@/pages/Services')).default
      
      render(
        <TestWrapper>
          <ServicesPage />
        </TestWrapper>
      )

      // Should show loading indicator
      expect(screen.getByText(/loading/i)).toBeInTheDocument()

      // Wait for content to load
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument()
      }, { timeout: 2000 })
    })

    it('should handle offline scenarios', async () => {
      // Mock network error
      mockConvexClient.query.mockRejectedValue(new Error('Network unavailable'))

      const HomePage = (await import('@/pages/Home')).default
      
      render(
        <TestWrapper>
          <HomePage />
        </TestWrapper>
      )

      // Should show offline message
      await waitFor(() => {
        expect(screen.getByText(/offline/i)).toBeInTheDocument()
      })
    })
  })
})
