/**
 * Integration tests for Convex functions
 * Tests database operations, authentication, and API endpoints
 */

import { ConvexTestingHelper } from 'convex/testing';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';

// Mock Clerk authentication
const mockClerkUser = {
  subject: 'user_test123',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User'
};

describe('Convex Integration Tests', () => {
  let convex: ConvexTestingHelper;

  beforeEach(async () => {
    convex = new ConvexTestingHelper();
    await convex.run(async (ctx) => {
      // Clear test data
      const users = await ctx.db.query('users').collect();
      for (const user of users) {
        await ctx.db.delete(user._id);
      }
      
      const content = await ctx.db.query('content').collect();
      for (const item of content) {
        await ctx.db.delete(item._id);
      }
      
      const contentTypes = await ctx.db.query('contentTypes').collect();
      for (const type of contentTypes) {
        await ctx.db.delete(type._id);
      }
    });
  });

  afterEach(async () => {
    await convex.cleanup();
  });

  describe('User Management', () => {
    it('should create a new user', async () => {
      const userId = await convex.mutation(api.users.getOrCreateUser, {
        clerkId: mockClerkUser.subject,
        email: mockClerkUser.email,
        firstName: mockClerkUser.firstName,
        lastName: mockClerkUser.lastName
      });

      expect(userId).toBeDefined();

      const user = await convex.query(api.users.getUserByClerkId, {
        clerkId: mockClerkUser.subject
      });

      expect(user).toMatchObject({
        clerkId: mockClerkUser.subject,
        email: mockClerkUser.email,
        firstName: mockClerkUser.firstName,
        lastName: mockClerkUser.lastName,
        role: 'viewer',
        isActive: true
      });
    });

    it('should update existing user on subsequent calls', async () => {
      // Create user first time
      await convex.mutation(api.users.getOrCreateUser, {
        clerkId: mockClerkUser.subject,
        email: mockClerkUser.email,
        firstName: mockClerkUser.firstName,
        lastName: mockClerkUser.lastName
      });

      const firstUser = await convex.query(api.users.getUserByClerkId, {
        clerkId: mockClerkUser.subject
      });

      // Call again with same data
      await convex.mutation(api.users.getOrCreateUser, {
        clerkId: mockClerkUser.subject,
        email: mockClerkUser.email,
        firstName: 'Updated',
        lastName: 'Name'
      });

      const updatedUser = await convex.query(api.users.getUserByClerkId, {
        clerkId: mockClerkUser.subject
      });

      expect(updatedUser?._id).toBe(firstUser?._id);
      expect(updatedUser?.lastLogin).toBeGreaterThan(firstUser?.lastLogin || 0);
    });

    it('should get all users', async () => {
      // Create multiple users
      await convex.mutation(api.users.getOrCreateUser, {
        clerkId: 'user1',
        email: '<EMAIL>'
      });

      await convex.mutation(api.users.getOrCreateUser, {
        clerkId: 'user2',
        email: '<EMAIL>'
      });

      const users = await convex.query(api.users.getAllUsers);
      expect(users).toHaveLength(2);
    });
  });

  describe('Content Types', () => {
    it('should create and retrieve content types', async () => {
      const contentTypeId = await convex.mutation(api.contentTypes.createContentType, {
        name: 'test-page',
        displayName: 'Test Page',
        description: 'A test page content type',
        fields: [
          {
            name: 'title',
            type: 'text',
            required: true,
            label: 'Title'
          },
          {
            name: 'content',
            type: 'richtext',
            required: false,
            label: 'Content'
          }
        ]
      });

      expect(contentTypeId).toBeDefined();

      const contentType = await convex.query(api.contentTypes.getContentTypeByName, {
        name: 'test-page'
      });

      expect(contentType).toMatchObject({
        name: 'test-page',
        displayName: 'Test Page',
        description: 'A test page content type'
      });

      expect(contentType?.fields).toHaveLength(2);
    });

    it('should get all content types', async () => {
      await convex.mutation(api.contentTypes.createContentType, {
        name: 'page',
        displayName: 'Page',
        fields: []
      });

      await convex.mutation(api.contentTypes.createContentType, {
        name: 'blog-post',
        displayName: 'Blog Post',
        fields: []
      });

      const contentTypes = await convex.query(api.contentTypes.getAllContentTypes);
      expect(contentTypes).toHaveLength(2);
    });
  });

  describe('Content Management', () => {
    let contentTypeId: Id<'contentTypes'>;
    let userId: Id<'users'>;

    beforeEach(async () => {
      // Create test user
      userId = await convex.mutation(api.users.getOrCreateUser, {
        clerkId: mockClerkUser.subject,
        email: mockClerkUser.email
      });

      // Create test content type
      contentTypeId = await convex.mutation(api.contentTypes.createContentType, {
        name: 'test-content',
        displayName: 'Test Content',
        fields: [
          { name: 'title', type: 'text', required: true, label: 'Title' },
          { name: 'body', type: 'richtext', required: false, label: 'Body' }
        ]
      });
    });

    it('should create and retrieve content', async () => {
      const contentData = {
        title: 'Test Title',
        body: 'Test body content'
      };

      // Mock authentication context
      convex.withIdentity(mockClerkUser);

      const contentId = await convex.mutation(api.content.createContent, {
        identifier: 'test-page',
        language: 'en',
        data: contentData,
        contentTypeId,
        status: 'published'
      });

      expect(contentId).toBeDefined();

      const content = await convex.query(api.content.getContent, {
        identifier: 'test-page',
        language: 'en'
      });

      expect(content).toMatchObject({
        identifier: 'test-page',
        language: 'en',
        data: contentData,
        status: 'published'
      });
    });

    it('should handle multilingual content', async () => {
      convex.withIdentity(mockClerkUser);

      const englishData = { title: 'English Title', body: 'English content' };
      const spanishData = { title: 'Título Español', body: 'Contenido español' };

      // Create English content
      await convex.mutation(api.content.createContent, {
        identifier: 'multilingual-page',
        language: 'en',
        data: englishData,
        contentTypeId,
        status: 'published'
      });

      // Create Spanish content
      await convex.mutation(api.content.createContent, {
        identifier: 'multilingual-page',
        language: 'es',
        data: spanishData,
        contentTypeId,
        status: 'published'
      });

      const englishContent = await convex.query(api.content.getContent, {
        identifier: 'multilingual-page',
        language: 'en'
      });

      const spanishContent = await convex.query(api.content.getContent, {
        identifier: 'multilingual-page',
        language: 'es'
      });

      expect(englishContent?.data).toEqual(englishData);
      expect(spanishContent?.data).toEqual(spanishData);
    });

    it('should fallback to English when content not found in requested language', async () => {
      convex.withIdentity(mockClerkUser);

      const englishData = { title: 'English Title', body: 'English content' };

      // Create only English content
      await convex.mutation(api.content.createContent, {
        identifier: 'fallback-test',
        language: 'en',
        data: englishData,
        contentTypeId,
        status: 'published'
      });

      // Request Spanish content (should fallback to English)
      const content = await convex.query(api.content.getContent, {
        identifier: 'fallback-test',
        language: 'es'
      });

      expect(content?.data).toEqual(englishData);
      expect(content?.language).toBe('en');
    });

    it('should update existing content', async () => {
      convex.withIdentity(mockClerkUser);

      const originalData = { title: 'Original Title', body: 'Original content' };
      const updatedData = { title: 'Updated Title', body: 'Updated content' };

      // Create content
      const contentId = await convex.mutation(api.content.createContent, {
        identifier: 'update-test',
        language: 'en',
        data: originalData,
        contentTypeId,
        status: 'published'
      });

      // Update content
      await convex.mutation(api.content.updateContent, {
        contentId,
        data: updatedData,
        status: 'published'
      });

      const content = await convex.query(api.content.getContent, {
        identifier: 'update-test',
        language: 'en'
      });

      expect(content?.data).toEqual(updatedData);
    });

    it('should handle draft and published status', async () => {
      convex.withIdentity(mockClerkUser);

      const draftData = { title: 'Draft Title', body: 'Draft content' };

      // Create draft content
      await convex.mutation(api.content.createContent, {
        identifier: 'draft-test',
        language: 'en',
        data: draftData,
        contentTypeId,
        status: 'draft'
      });

      // Should not return draft content by default
      const publishedContent = await convex.query(api.content.getContent, {
        identifier: 'draft-test',
        language: 'en'
      });

      expect(publishedContent).toBeNull();

      // Should return draft content when includeDraft is true
      const draftContent = await convex.query(api.content.getContent, {
        identifier: 'draft-test',
        language: 'en',
        includeDraft: true
      });

      expect(draftContent?.data).toEqual(draftData);
      expect(draftContent?.status).toBe('draft');
    });
  });

  describe('Contact Forms', () => {
    it('should submit contact form', async () => {
      const contactData = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        company: 'Test Company',
        service: 'Network Solutions',
        message: 'I need help with network setup',
        source: 'website',
        language: 'en'
      };

      const submissionId = await convex.mutation(api.contacts.submitContact, contactData);
      expect(submissionId).toBeDefined();

      const submission = await convex.query(api.contacts.getContactSubmission, {
        submissionId
      });

      expect(submission).toMatchObject({
        ...contactData,
        status: 'new'
      });
    });

    it('should get contact submissions with filtering', async () => {
      // Create multiple submissions
      await convex.mutation(api.contacts.submitContact, {
        name: 'User 1',
        email: '<EMAIL>',
        message: 'Message 1',
        source: 'website'
      });

      await convex.mutation(api.contacts.submitContact, {
        name: 'User 2',
        email: '<EMAIL>',
        message: 'Message 2',
        source: 'website'
      });

      const submissions = await convex.query(api.contacts.getContactSubmissions, {
        status: 'new',
        limit: 10
      });

      expect(submissions).toHaveLength(2);
      expect(submissions.every(s => s.status === 'new')).toBe(true);
    });

    it('should update contact submission status', async () => {
      const submissionId = await convex.mutation(api.contacts.submitContact, {
        name: 'Test User',
        email: '<EMAIL>',
        message: 'Test message',
        source: 'website'
      });

      await convex.mutation(api.contacts.updateContactStatus, {
        submissionId,
        status: 'contacted'
      });

      const submission = await convex.query(api.contacts.getContactSubmission, {
        submissionId
      });

      expect(submission?.status).toBe('contacted');
    });
  });

  describe('Error Handling', () => {
    it('should handle authentication errors', async () => {
      // Try to create content without authentication
      await expect(
        convex.mutation(api.content.createContent, {
          identifier: 'test',
          language: 'en',
          data: {},
          contentTypeId: 'invalid' as any,
          status: 'published'
        })
      ).rejects.toThrow('Not authenticated');
    });

    it('should handle invalid content type references', async () => {
      convex.withIdentity(mockClerkUser);

      await expect(
        convex.mutation(api.content.createContent, {
          identifier: 'test',
          language: 'en',
          data: {},
          contentTypeId: 'invalid_id' as any,
          status: 'published'
        })
      ).rejects.toThrow();
    });

    it('should handle missing required fields', async () => {
      await expect(
        convex.mutation(api.contacts.submitContact, {
          name: '',
          email: 'invalid-email',
          message: '',
          source: 'website'
        })
      ).rejects.toThrow();
    });
  });
});
