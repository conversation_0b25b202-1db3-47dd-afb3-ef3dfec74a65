/**
 * End-to-end tests for the OfficeTech Guinea website
 * Tests complete user journeys across the entire application
 */

import { test, expect, Page } from '@playwright/test'

const BASE_URL = process.env.BASE_URL || 'http://localhost:8081'

test.describe('OfficeTech Guinea Website E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up any global state or authentication if needed
    await page.goto(BASE_URL)
  })

  test.describe('Homepage', () => {
    test('should load homepage successfully', async ({ page }) => {
      await page.goto(BASE_URL)
      
      // Check page title
      await expect(page).toHaveTitle(/OfficeTech Guinea/i)
      
      // Check main navigation
      await expect(page.locator('nav')).toBeVisible()
      await expect(page.getByRole('link', { name: /home/<USER>
      await expect(page.getByRole('link', { name: /services/i })).toBeVisible()
      await expect(page.getByRole('link', { name: /about/i })).toBeVisible()
      await expect(page.getByRole('link', { name: /contact/i })).toBeVisible()
      
      // Check hero section
      await expect(page.locator('[data-testid="hero-section"]')).toBeVisible()
      
      // Check services preview
      await expect(page.locator('[data-testid="services-preview"]')).toBeVisible()
    })

    test('should have proper SEO meta tags', async ({ page }) => {
      await page.goto(BASE_URL)
      
      // Check meta description
      const metaDescription = page.locator('meta[name="description"]')
      await expect(metaDescription).toHaveAttribute('content', /.+/)
      
      // Check Open Graph tags
      const ogTitle = page.locator('meta[property="og:title"]')
      await expect(ogTitle).toHaveAttribute('content', /.+/)
      
      const ogDescription = page.locator('meta[property="og:description"]')
      await expect(ogDescription).toHaveAttribute('content', /.+/)
      
      // Check Twitter Card tags
      const twitterCard = page.locator('meta[name="twitter:card"]')
      await expect(twitterCard).toHaveAttribute('content', 'summary_large_image')
    })

    test('should be responsive on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 })
      await page.goto(BASE_URL)
      
      // Check mobile navigation
      const mobileMenuButton = page.getByRole('button', { name: /menu/i })
      await expect(mobileMenuButton).toBeVisible()
      
      // Open mobile menu
      await mobileMenuButton.click()
      await expect(page.getByRole('navigation')).toBeVisible()
      
      // Check that content is properly sized
      const heroSection = page.locator('[data-testid="hero-section"]')
      await expect(heroSection).toBeVisible()
      
      const boundingBox = await heroSection.boundingBox()
      expect(boundingBox?.width).toBeLessThanOrEqual(375)
    })
  })

  test.describe('Navigation', () => {
    test('should navigate between pages', async ({ page }) => {
      await page.goto(BASE_URL)
      
      // Navigate to Services page
      await page.getByRole('link', { name: /services/i }).click()
      await expect(page).toHaveURL(/\/services/)
      await expect(page.getByRole('heading', { name: /services/i })).toBeVisible()
      
      // Navigate to About page
      await page.getByRole('link', { name: /about/i }).click()
      await expect(page).toHaveURL(/\/about/)
      await expect(page.getByRole('heading', { name: /about/i })).toBeVisible()
      
      // Navigate to Contact page
      await page.getByRole('link', { name: /contact/i }).click()
      await expect(page).toHaveURL(/\/contact/)
      await expect(page.getByRole('heading', { name: /contact/i })).toBeVisible()
      
      // Navigate back to Home
      await page.getByRole('link', { name: /home/<USER>
      await expect(page).toHaveURL(BASE_URL)
    })

    test('should handle 404 pages gracefully', async ({ page }) => {
      await page.goto(`${BASE_URL}/non-existent-page`)
      
      // Should show 404 page
      await expect(page.getByText(/404/i)).toBeVisible()
      await expect(page.getByText(/page not found/i)).toBeVisible()
      
      // Should have link back to home
      const homeLink = page.getByRole('link', { name: /home/<USER>
      await expect(homeLink).toBeVisible()
      await homeLink.click()
      await expect(page).toHaveURL(BASE_URL)
    })
  })

  test.describe('Services Pages', () => {
    test('should display all service categories', async ({ page }) => {
      await page.goto(`${BASE_URL}/services`)
      
      // Check for main service categories
      await expect(page.getByText(/network solutions/i)).toBeVisible()
      await expect(page.getByText(/enterprise services/i)).toBeVisible()
      await expect(page.getByText(/domestic services/i)).toBeVisible()
      await expect(page.getByText(/training programs/i)).toBeVisible()
      await expect(page.getByText(/product reselling/i)).toBeVisible()
    })

    test('should navigate to specific service pages', async ({ page }) => {
      await page.goto(`${BASE_URL}/services`)
      
      // Click on Network Solutions
      await page.getByRole('link', { name: /network solutions/i }).click()
      await expect(page).toHaveURL(/\/services\/network-solutions/)
      await expect(page.getByRole('heading', { name: /network solutions/i })).toBeVisible()
      
      // Check for service details
      await expect(page.getByText(/national connectivity/i)).toBeVisible()
      await expect(page.getByText(/branch office connectivity/i)).toBeVisible()
      await expect(page.getByText(/managed enterprise internet/i)).toBeVisible()
    })

    test('should show service pricing and features', async ({ page }) => {
      await page.goto(`${BASE_URL}/services/domestic-services`)
      
      // Check for pricing information
      await expect(page.locator('[data-testid="pricing-table"]')).toBeVisible()
      
      // Check for feature lists
      await expect(page.locator('[data-testid="features-list"]')).toBeVisible()
      
      // Check for call-to-action buttons
      await expect(page.getByRole('button', { name: /get quote/i })).toBeVisible()
    })
  })

  test.describe('Contact Form', () => {
    test('should submit contact form successfully', async ({ page }) => {
      await page.goto(`${BASE_URL}/contact`)
      
      // Fill out the contact form
      await page.getByLabel(/name/i).fill('John Doe')
      await page.getByLabel(/email/i).fill('<EMAIL>')
      await page.getByLabel(/phone/i).fill('+1234567890')
      await page.getByLabel(/company/i).fill('Test Company')
      await page.getByLabel(/service/i).selectOption('Network Solutions')
      await page.getByLabel(/message/i).fill('I need help setting up enterprise network infrastructure for my company.')
      
      // Submit the form
      await page.getByRole('button', { name: /submit/i }).click()
      
      // Check for success message
      await expect(page.getByText(/thank you/i)).toBeVisible()
      await expect(page.getByText(/we will contact you/i)).toBeVisible()
    })

    test('should validate required fields', async ({ page }) => {
      await page.goto(`${BASE_URL}/contact`)
      
      // Try to submit empty form
      await page.getByRole('button', { name: /submit/i }).click()
      
      // Check for validation messages
      await expect(page.getByText(/name is required/i)).toBeVisible()
      await expect(page.getByText(/email is required/i)).toBeVisible()
      await expect(page.getByText(/message is required/i)).toBeVisible()
    })

    test('should validate email format', async ({ page }) => {
      await page.goto(`${BASE_URL}/contact`)
      
      // Fill form with invalid email
      await page.getByLabel(/name/i).fill('John Doe')
      await page.getByLabel(/email/i).fill('invalid-email')
      await page.getByLabel(/message/i).fill('Test message')
      
      await page.getByRole('button', { name: /submit/i }).click()
      
      // Check for email validation message
      await expect(page.getByText(/valid email/i)).toBeVisible()
    })
  })

  test.describe('Language Switching', () => {
    test('should switch between English and Spanish', async ({ page }) => {
      await page.goto(BASE_URL)
      
      // Check initial language (English)
      await expect(page.getByText(/welcome/i)).toBeVisible()
      
      // Switch to Spanish
      await page.getByRole('button', { name: /español/i }).click()
      
      // Check Spanish content
      await expect(page.getByText(/bienvenido/i)).toBeVisible()
      
      // Switch back to English
      await page.getByRole('button', { name: /english/i }).click()
      
      // Check English content is back
      await expect(page.getByText(/welcome/i)).toBeVisible()
    })

    test('should persist language preference', async ({ page }) => {
      await page.goto(BASE_URL)
      
      // Switch to Spanish
      await page.getByRole('button', { name: /español/i }).click()
      
      // Navigate to another page
      await page.getByRole('link', { name: /servicios/i }).click()
      
      // Should still be in Spanish
      await expect(page.getByText(/servicios/i)).toBeVisible()
      
      // Reload page
      await page.reload()
      
      // Should still be in Spanish
      await expect(page.getByText(/servicios/i)).toBeVisible()
    })
  })

  test.describe('Search Functionality', () => {
    test('should search for content', async ({ page }) => {
      await page.goto(BASE_URL)
      
      // Open search
      await page.getByRole('button', { name: /search/i }).click()
      
      // Enter search term
      await page.getByPlaceholder(/search/i).fill('network')
      await page.keyboard.press('Enter')
      
      // Check search results
      await expect(page.getByText(/search results/i)).toBeVisible()
      await expect(page.getByText(/network/i)).toBeVisible()
    })

    test('should handle empty search results', async ({ page }) => {
      await page.goto(BASE_URL)
      
      // Search for non-existent content
      await page.getByRole('button', { name: /search/i }).click()
      await page.getByPlaceholder(/search/i).fill('xyz123nonexistent')
      await page.keyboard.press('Enter')
      
      // Check no results message
      await expect(page.getByText(/no results found/i)).toBeVisible()
    })
  })

  test.describe('Performance', () => {
    test('should load pages within acceptable time', async ({ page }) => {
      const startTime = Date.now()
      await page.goto(BASE_URL)
      
      // Wait for page to be fully loaded
      await page.waitForLoadState('networkidle')
      
      const loadTime = Date.now() - startTime
      
      // Page should load within 3 seconds
      expect(loadTime).toBeLessThan(3000)
    })

    test('should have good Core Web Vitals', async ({ page }) => {
      await page.goto(BASE_URL)
      
      // Measure Largest Contentful Paint (LCP)
      const lcp = await page.evaluate(() => {
        return new Promise((resolve) => {
          new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1]
            resolve(lastEntry.startTime)
          }).observe({ entryTypes: ['largest-contentful-paint'] })
          
          // Fallback timeout
          setTimeout(() => resolve(0), 5000)
        })
      })
      
      // LCP should be under 2.5 seconds
      expect(lcp).toBeLessThan(2500)
    })
  })

  test.describe('Accessibility', () => {
    test('should be keyboard navigable', async ({ page }) => {
      await page.goto(BASE_URL)
      
      // Tab through navigation
      await page.keyboard.press('Tab')
      await expect(page.getByRole('link', { name: /home/<USER>
      
      await page.keyboard.press('Tab')
      await expect(page.getByRole('link', { name: /services/i })).toBeFocused()
      
      // Enter should activate links
      await page.keyboard.press('Enter')
      await expect(page).toHaveURL(/\/services/)
    })

    test('should have proper heading hierarchy', async ({ page }) => {
      await page.goto(BASE_URL)
      
      // Check for h1
      const h1 = page.locator('h1')
      await expect(h1).toBeVisible()
      
      // Check heading order
      const headings = await page.locator('h1, h2, h3, h4, h5, h6').allTextContents()
      expect(headings.length).toBeGreaterThan(0)
    })

    test('should have alt text for images', async ({ page }) => {
      await page.goto(BASE_URL)
      
      // Check all images have alt text
      const images = page.locator('img')
      const imageCount = await images.count()
      
      for (let i = 0; i < imageCount; i++) {
        const img = images.nth(i)
        const alt = await img.getAttribute('alt')
        expect(alt).toBeTruthy()
      }
    })
  })

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Simulate offline mode
      await page.context().setOffline(true)
      await page.goto(BASE_URL)
      
      // Should show offline message
      await expect(page.getByText(/offline/i)).toBeVisible()
      
      // Go back online
      await page.context().setOffline(false)
      await page.reload()
      
      // Should load normally
      await expect(page.getByText(/welcome/i)).toBeVisible()
    })

    test('should handle JavaScript errors gracefully', async ({ page }) => {
      // Listen for console errors
      const errors: string[] = []
      page.on('console', msg => {
        if (msg.type() === 'error') {
          errors.push(msg.text())
        }
      })
      
      await page.goto(BASE_URL)
      
      // Navigate through the site
      await page.getByRole('link', { name: /services/i }).click()
      await page.getByRole('link', { name: /about/i }).click()
      await page.getByRole('link', { name: /contact/i }).click()
      
      // Should not have critical JavaScript errors
      const criticalErrors = errors.filter(error => 
        !error.includes('favicon') && 
        !error.includes('analytics') &&
        !error.includes('third-party')
      )
      
      expect(criticalErrors).toHaveLength(0)
    })
  })
})
