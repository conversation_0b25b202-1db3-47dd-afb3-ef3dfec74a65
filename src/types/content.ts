import { z } from "zod";

// Base field types
export type FieldType = 
  | "text" 
  | "richText" 
  | "image" 
  | "boolean" 
  | "number" 
  | "array" 
  | "object"
  | "select"
  | "multiSelect"
  | "date"
  | "url"
  | "email"
  | "color";

// Field definition schema
export const FieldDefinitionSchema = z.object({
  name: z.string(),
  label: z.string(),
  type: z.enum([
    "text", 
    "richText", 
    "image", 
    "boolean", 
    "number", 
    "array", 
    "object",
    "select",
    "multiSelect",
    "date",
    "url",
    "email",
    "color"
  ]),
  required: z.boolean().default(false),
  description: z.string().optional(),
  placeholder: z.string().optional(),
  defaultValue: z.any().optional(),
  validation: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    pattern: z.string().optional(),
    options: z.array(z.string()).optional(), // For select/multiSelect
  }).optional(),
});

export type FieldDefinition = z.infer<typeof FieldDefinitionSchema>;

// Content type schema
export const ContentTypeSchema = z.object({
  name: z.string(),
  label: z.string(),
  description: z.string(),
  icon: z.string().optional(),
  category: z.string().default("general"),
  fields: z.array(FieldDefinitionSchema),
  settings: z.object({
    allowMultiple: z.boolean().default(true),
    isSystem: z.boolean().default(false),
    sortable: z.boolean().default(true),
  }).optional(),
});

export type ContentType = z.infer<typeof ContentTypeSchema>;

// Content data validation
export const ContentDataSchema = z.record(z.any());
export type ContentData = z.infer<typeof ContentDataSchema>;

// Content instance schema
export const ContentSchema = z.object({
  id: z.string(),
  contentTypeId: z.string(),
  identifier: z.string(),
  language: z.string(),
  data: ContentDataSchema,
  status: z.enum(["draft", "published"]),
  version: z.number(),
  createdBy: z.string(),
  createdAt: z.number(),
  updatedAt: z.number(),
});

export type Content = z.infer<typeof ContentSchema>;

// Predefined content types
export const PREDEFINED_CONTENT_TYPES: ContentType[] = [
  {
    name: "hero_section",
    label: "Hero Section",
    description: "Main hero section with title, subtitle, and call-to-action",
    icon: "Layout",
    category: "sections",
    fields: [
      {
        name: "title",
        label: "Title",
        type: "text",
        required: true,
        placeholder: "Enter hero title",
      },
      {
        name: "subtitle",
        label: "Subtitle",
        type: "richText",
        required: false,
        placeholder: "Enter hero subtitle",
      },
      {
        name: "heroImage",
        label: "Hero Image",
        type: "image",
        required: false,
      },
      {
        name: "heroImageAlt",
        label: "Hero Image Alt Text",
        type: "text",
        required: false,
        placeholder: "Alt text for hero image",
      },
      {
        name: "ctaText",
        label: "Primary CTA Button Text",
        type: "text",
        required: false,
        defaultValue: "Get Started Today",
      },
      {
        name: "ctaUrl",
        label: "Primary CTA Button URL",
        type: "url",
        required: false,
      },
      {
        name: "secondaryCtaText",
        label: "Secondary CTA Button Text",
        type: "text",
        required: false,
        defaultValue: "View Our Work",
      },
      {
        name: "secondaryCtaUrl",
        label: "Secondary CTA Button URL",
        type: "url",
        required: false,
      },
      {
        name: "badge1Text",
        label: "Badge 1 Text",
        type: "text",
        required: false,
        defaultValue: "24/7 Support",
      },
      {
        name: "badge2Text",
        label: "Badge 2 Text",
        type: "text",
        required: false,
        defaultValue: "Secure Solutions",
      },
      {
        name: "experienceYears",
        label: "Experience Years",
        type: "text",
        required: false,
        defaultValue: "15+",
      },
      {
        name: "experienceLabel",
        label: "Experience Label",
        type: "text",
        required: false,
        defaultValue: "Years\nExperience",
      },
      {
        name: "showVideo",
        label: "Show Video",
        type: "boolean",
        defaultValue: false,
      },
    ],
    settings: {
      allowMultiple: false,
      isSystem: true,
    },
  },
  {
    name: "text_block",
    label: "Text Block",
    description: "Simple text content block",
    icon: "Type",
    category: "content",
    fields: [
      {
        name: "title",
        label: "Title",
        type: "text",
        required: false,
      },
      {
        name: "content",
        label: "Content",
        type: "richText",
        required: true,
      },
      {
        name: "alignment",
        label: "Text Alignment",
        type: "select",
        validation: {
          options: ["left", "center", "right"],
        },
        defaultValue: "left",
      },
    ],
  },
  {
    name: "image_gallery",
    label: "Image Gallery",
    description: "Collection of images with captions",
    icon: "Images",
    category: "media",
    fields: [
      {
        name: "title",
        label: "Gallery Title",
        type: "text",
        required: false,
      },
      {
        name: "images",
        label: "Images",
        type: "array",
        required: true,
      },
      {
        name: "layout",
        label: "Layout Style",
        type: "select",
        validation: {
          options: ["grid", "masonry", "carousel"],
        },
        defaultValue: "grid",
      },
    ],
  },
  {
    name: "service_card",
    label: "Service Card",
    description: "Service or feature card with icon and description",
    icon: "Briefcase",
    category: "services",
    fields: [
      {
        name: "title",
        label: "Service Title",
        type: "text",
        required: true,
      },
      {
        name: "description",
        label: "Description",
        type: "richText",
        required: true,
      },
      {
        name: "icon",
        label: "Icon",
        type: "text",
        required: false,
        placeholder: "Lucide icon name",
      },
      {
        name: "image",
        label: "Service Image",
        type: "image",
        required: false,
      },
      {
        name: "features",
        label: "Features",
        type: "array",
        required: false,
      },
      {
        name: "ctaText",
        label: "CTA Text",
        type: "text",
        required: false,
      },
      {
        name: "ctaUrl",
        label: "CTA URL",
        type: "url",
        required: false,
      },
    ],
  },
  {
    name: "testimonial",
    label: "Testimonial",
    description: "Customer testimonial with photo and details",
    icon: "MessageSquare",
    category: "social",
    fields: [
      {
        name: "content",
        label: "Testimonial Content",
        type: "richText",
        required: true,
      },
      {
        name: "authorName",
        label: "Author Name",
        type: "text",
        required: true,
      },
      {
        name: "authorTitle",
        label: "Author Title",
        type: "text",
        required: false,
      },
      {
        name: "authorCompany",
        label: "Author Company",
        type: "text",
        required: false,
      },
      {
        name: "authorImage",
        label: "Author Photo",
        type: "image",
        required: false,
      },
      {
        name: "rating",
        label: "Rating (1-5)",
        type: "number",
        validation: {
          min: 1,
          max: 5,
        },
        defaultValue: 5,
      },
    ],
  },
  {
    name: "contact_info",
    label: "Contact Information",
    description: "Contact details block",
    icon: "Phone",
    category: "contact",
    fields: [
      {
        name: "title",
        label: "Section Title",
        type: "text",
        required: false,
      },
      {
        name: "address",
        label: "Address",
        type: "richText",
        required: false,
      },
      {
        name: "phone",
        label: "Phone Number",
        type: "text",
        required: false,
      },
      {
        name: "email",
        label: "Email Address",
        type: "email",
        required: false,
      },
      {
        name: "hours",
        label: "Business Hours",
        type: "richText",
        required: false,
      },
      {
        name: "socialLinks",
        label: "Social Media Links",
        type: "array",
        required: false,
      },
    ],
    settings: {
      allowMultiple: false,
      isSystem: true,
    },
  },
];

// Content validation utilities
export const validateContentData = (data: any, fields: FieldDefinition[]): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  for (const field of fields) {
    const value = data[field.name];

    // Check required fields
    if (field.required && (value === undefined || value === null || value === "")) {
      errors.push(`${field.label} is required`);
      continue;
    }

    // Skip validation if field is not required and empty
    if (!field.required && (value === undefined || value === null || value === "")) {
      continue;
    }

    // Type-specific validation
    switch (field.type) {
      case "number":
        if (typeof value !== "number") {
          errors.push(`${field.label} must be a number`);
        } else if (field.validation?.min !== undefined && value < field.validation.min) {
          errors.push(`${field.label} must be at least ${field.validation.min}`);
        } else if (field.validation?.max !== undefined && value > field.validation.max) {
          errors.push(`${field.label} must be at most ${field.validation.max}`);
        }
        break;

      case "email":
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          errors.push(`${field.label} must be a valid email address`);
        }
        break;

      case "url":
        try {
          new URL(value);
        } catch {
          errors.push(`${field.label} must be a valid URL`);
        }
        break;

      case "select":
        if (field.validation?.options && !field.validation.options.includes(value)) {
          errors.push(`${field.label} must be one of: ${field.validation.options.join(", ")}`);
        }
        break;

      case "multiSelect":
        if (field.validation?.options && Array.isArray(value)) {
          const invalidOptions = value.filter(v => !field.validation?.options?.includes(v));
          if (invalidOptions.length > 0) {
            errors.push(`${field.label} contains invalid options: ${invalidOptions.join(", ")}`);
          }
        }
        break;
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
