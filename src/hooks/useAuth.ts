import { useUser } from "@clerk/clerk-react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useEffect } from "react";

export type UserRole = "super_admin" | "admin" | "content_editor" | "viewer";

export interface AuthUser {
  id: string;
  clerkId: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role: UserRole;
  isActive: boolean;
  lastLogin?: number;
}

export const useAuth = () => {
  const { user: clerkUser, isLoaded, isSignedIn } = useUser();
  
  // Get or create user in our database
  const getOrCreateUser = useMutation(api.users.getOrCreateUser);
  const dbUser = useQuery(
    api.users.getUserByClerkId,
    clerkUser?.id ? { clerkId: clerkUser.id } : "skip"
  );

  // Sync user with database when Clerk user loads
  useEffect(() => {
    if (isLoaded && isSignedIn && clerkUser && !dbUser) {
      getOrCreateUser({
        clerkId: clerkUser.id,
        email: clerkUser.emailAddresses[0]?.emailAddress || "",
        firstName: clerkUser.firstName || undefined,
        lastName: clerkUser.lastName || undefined,
      });
    }
  }, [isLoaded, isSignedIn, clerkUser, dbUser, getOrCreateUser]);

  const hasRole = (requiredRole: UserRole): boolean => {
    if (!dbUser) return false;
    
    const roleHierarchy: Record<UserRole, number> = {
      viewer: 1,
      content_editor: 2,
      admin: 3,
      super_admin: 4,
    };

    return roleHierarchy[dbUser.role] >= roleHierarchy[requiredRole];
  };

  const hasPermission = (permission: string): boolean => {
    if (!dbUser) return false;

    const permissions: Record<UserRole, string[]> = {
      viewer: [
        "content:read",
        "media:read",
      ],
      content_editor: [
        "content:read",
        "content:write",
        "content:publish",
        "media:read",
        "media:write",
        "media:upload",
        "analytics:read",
        "users:read",
        "settings:read",
      ],
      admin: [
        "content:read",
        "content:write",
        "content:publish",
        "content:delete",
        "media:read",
        "media:write",
        "media:upload",
        "media:delete",
        "users:read",
        "users:write",
        "analytics:read",
        "settings:read",
        "settings:write",
      ],
      super_admin: [
        "*", // All permissions
      ],
    };

    const userPermissions = permissions[dbUser.role] || [];
    return userPermissions.includes("*") || userPermissions.includes(permission);
  };

  return {
    // Clerk user data
    clerkUser,
    isLoaded,
    isSignedIn,
    
    // Database user data
    user: dbUser,
    
    // Role checking
    hasRole,
    hasPermission,
    
    // Convenience checks
    isAdmin: hasRole("admin"),
    isSuperAdmin: hasRole("super_admin"),
    isContentEditor: hasRole("content_editor"),
    isViewer: dbUser?.role === "viewer",

    // Content permissions
    canReadContent: hasPermission("content:read"),
    canEditContent: hasPermission("content:write"),
    canPublishContent: hasPermission("content:publish"),
    canDeleteContent: hasPermission("content:delete"),

    // Media permissions
    canReadMedia: hasPermission("media:read"),
    canUploadMedia: hasPermission("media:upload"),
    canEditMedia: hasPermission("media:write"),
    canDeleteMedia: hasPermission("media:delete"),

    // User management permissions
    canReadUsers: hasPermission("users:read"),
    canManageUsers: hasPermission("users:write"),

    // Analytics permissions
    canViewAnalytics: hasPermission("analytics:read"),

    // Settings permissions
    canReadSettings: hasPermission("settings:read"),
    canManageSettings: hasPermission("settings:write"),

    // Admin access
    canAccessAdmin: hasPermission("content:read") || hasPermission("media:read"),
  };
};
