import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";

// Hook for getting all media with filtering
export const useMedia = (options?: {
  limit?: number;
  tags?: string[];
  mimeType?: string;
  uploadedBy?: string;
}) => {
  const media = useQuery(api.media.getAllMedia, options);

  return {
    media,
    isLoading: media === undefined,
  };
};

// Hook for getting single media item
export const useMediaItem = (id: Id<"media">) => {
  const media = useQuery(api.media.getMediaById, { id });

  return {
    media,
    isLoading: media === undefined,
  };
};

// Hook for searching media
export const useMediaSearch = (searchQuery: string, limit?: number) => {
  const results = useQuery(
    api.media.searchMedia,
    searchQuery ? { query: searchQuery, limit } : "skip"
  );

  return {
    results,
    isLoading: results === undefined,
  };
};

// Hook for media statistics
export const useMediaStats = () => {
  const stats = useQuery(api.media.getMediaStats);

  return {
    stats,
    isLoading: stats === undefined,
  };
};

// Hook for getting all tags
export const useMediaTags = () => {
  const tags = useQuery(api.media.getAllTags);

  return {
    tags,
    isLoading: tags === undefined,
  };
};

// Hook for media mutations
export const useMediaMutations = () => {
  const uploadMedia = useMutation(api.media.uploadMedia);
  const updateMedia = useMutation(api.media.updateMedia);
  const deleteMedia = useMutation(api.media.deleteMedia);
  const bulkTagMedia = useMutation(api.media.bulkTagMedia);
  const generateImageVariants = useMutation(api.media.generateImageVariants);
  const cleanupUnusedMedia = useMutation(api.media.cleanupUnusedMedia);

  return {
    uploadMedia,
    updateMedia,
    deleteMedia,
    bulkTagMedia,
    generateImageVariants,
    cleanupUnusedMedia,
  };
};

// Utility functions for media handling
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export const getFileTypeIcon = (mimeType: string): string => {
  if (mimeType.startsWith("image/")) return "🖼️";
  if (mimeType.startsWith("video/")) return "🎥";
  if (mimeType.startsWith("audio/")) return "🎵";
  if (mimeType.includes("pdf")) return "📄";
  if (mimeType.includes("document") || mimeType.includes("word")) return "📝";
  if (mimeType.includes("spreadsheet") || mimeType.includes("excel")) return "📊";
  if (mimeType.includes("presentation") || mimeType.includes("powerpoint")) return "📈";
  return "📁";
};

export const isImageFile = (mimeType: string): boolean => {
  return mimeType.startsWith("image/");
};

export const isVideoFile = (mimeType: string): boolean => {
  return mimeType.startsWith("video/");
};

export const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    if (!isImageFile(file.type)) {
      reject(new Error("File is not an image"));
      return;
    }

    const img = new Image();
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

export const compressImage = (
  file: File, 
  maxWidth: number = 1920, 
  maxHeight: number = 1080, 
  quality: number = 0.8
): Promise<File> => {
  return new Promise((resolve, reject) => {
    if (!isImageFile(file.type)) {
      resolve(file);
      return;
    }

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            reject(new Error("Failed to compress image"));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

export const generateThumbnail = (file: File, size: number = 200): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!isImageFile(file.type)) {
      reject(new Error("File is not an image"));
      return;
    }

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    img.onload = () => {
      // Calculate square thumbnail dimensions
      const { width, height } = img;
      const minDimension = Math.min(width, height);
      const scale = size / minDimension;
      
      canvas.width = size;
      canvas.height = size;

      // Center crop
      const sourceX = (width - minDimension) / 2;
      const sourceY = (height - minDimension) / 2;

      ctx?.drawImage(
        img,
        sourceX, sourceY, minDimension, minDimension,
        0, 0, size, size
      );

      resolve(canvas.toDataURL(file.type, 0.8));
    };

    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
};

// File upload utilities
export const validateFile = (file: File, options?: {
  maxSize?: number;
  allowedTypes?: string[];
}): { isValid: boolean; error?: string } => {
  const maxSize = options?.maxSize || 10 * 1024 * 1024; // 10MB default
  const allowedTypes = options?.allowedTypes || [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "video/mp4",
    "video/webm",
    "application/pdf",
  ];

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size must be less than ${formatFileSize(maxSize)}`,
    };
  }

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${file.type} is not allowed`,
    };
  }

  return { isValid: true };
};

export const uploadFileToStorage = async (file: File): Promise<string> => {
  // In a real implementation, this would upload to a service like:
  // - AWS S3
  // - Cloudinary
  // - Vercel Blob
  // - Supabase Storage
  
  // For now, we'll simulate with a data URL
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

// Media library management
export const useMediaLibrary = () => {
  const { media, isLoading } = useMedia();
  const { tags } = useMediaTags();
  const mutations = useMediaMutations();

  const uploadFile = async (file: File, metadata?: {
    alt?: string;
    tags?: string[];
  }) => {
    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      throw new Error(validation.error);
    }

    // Compress if image
    const processedFile = isImageFile(file.type) 
      ? await compressImage(file)
      : file;

    // Upload to storage
    const url = await uploadFileToStorage(processedFile);

    // Save to database
    return await mutations.uploadMedia({
      filename: `${Date.now()}-${file.name}`,
      originalName: file.name,
      mimeType: file.type,
      size: processedFile.size,
      url,
      alt: metadata?.alt,
      tags: metadata?.tags,
    });
  };

  const updateFile = async (id: Id<"media">, updates: {
    alt?: string;
    tags?: string[];
  }) => {
    return await mutations.updateMedia({
      id,
      ...updates,
    });
  };

  const deleteFile = async (id: Id<"media">) => {
    return await mutations.deleteMedia({ id });
  };

  const bulkTag = async (
    mediaIds: Id<"media">[], 
    tags: string[], 
    operation: "add" | "remove" | "replace"
  ) => {
    return await mutations.bulkTagMedia({
      mediaIds,
      tags,
      operation,
    });
  };

  return {
    media,
    tags,
    isLoading,
    uploadFile,
    updateFile,
    deleteFile,
    bulkTag,
  };
};
