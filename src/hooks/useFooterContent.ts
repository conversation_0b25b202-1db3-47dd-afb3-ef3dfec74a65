import { useContent } from "./useContent";
import { useLanguage } from "@/contexts/I18nContext";

// Default footer content as fallback
const getDefaultFooterContent = (language: string) => ({
  companyName: "OfficeTech Guinea",
  companyDescription: language === 'en' 
    ? 'Leading technology and security solutions in Equatorial Guinea. Protecting and connecting your business.'
    : 'Líderes en soluciones tecnológicas y de seguridad en Guinea Ecuatorial. Protegiendo y conectando tu negocio.',
  ctaButtonText: language === 'en' ? 'Get Started' : 'Comenzar',
  ctaButtonLink: '/contact',
  quickLinksTitle: language === 'en' ? 'Quick Links' : 'Enlaces',
  contactTitle: language === 'en' ? 'Contact' : 'Contacto',
  address: language === 'en' ? 'Malabo, Equatorial Guinea' : 'Malabo, Guinea Ecuatorial',
  phone: '+240 XXX XXX XXX',
  email: '<EMAIL>',
  copyrightText: language === 'en' 
    ? '© 2024 OfficeTech Guinea. All rights reserved.'
    : '© 2024 OfficeTech Guinea. Todos los derechos reservados.',
  socialLinks: []
});

export const useFooterContent = (language?: string) => {
  const { language: currentLanguage } = useLanguage();
  const lang = language || currentLanguage;

  // Only get published content for public display
  const { content, isLoading, error } = useContent("footer-content", lang, false);

  // Debug logging
  console.log('useFooterContent - Language:', lang);
  console.log('useFooterContent - Content from DB:', content);
  console.log('useFooterContent - Has content data:', !!content?.data);
  console.log('useFooterContent - Content status:', content?.status);

  // Return content from database if available, otherwise return default content
  const footerContent = content?.data || getDefaultFooterContent(lang);

  console.log('useFooterContent - Final footer content:', footerContent);

  return {
    footerContent,
    isLoading,
    error,
    hasCustomContent: !!content?.data,
    status: content?.status || 'draft'
  };
};

// Hook for managing footer content (for admin use)
export const useFooterContentManager = (language?: string) => {
  const { language: currentLanguage } = useLanguage();
  const lang = language || currentLanguage;

  // Get both published and draft content
  const { content: draftContent, isLoading, error } = useContent("footer-content", lang, true);
  const { content: publishedContent } = useContent("footer-content", lang, false);

  // Debug logging for admin manager
  console.log('useFooterContentManager - Language:', lang);
  console.log('useFooterContentManager - Draft content:', draftContent);
  console.log('useFooterContentManager - Published content:', publishedContent);

  // Prioritize published content for editing, fall back to draft if no published version exists
  const contentToEdit = publishedContent || draftContent;

  console.log('useFooterContentManager - Content to edit:', contentToEdit);

  return {
    content: contentToEdit,
    isLoading,
    error,
    hasContent: !!contentToEdit,
    defaultContent: getDefaultFooterContent(lang)
  };
};
