import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useCallback } from "react";

// Hook for getting all site settings (public version for regular users)
export const useSiteSettings = () => {
  const data = useQuery(api.siteSettings.getPublicSettings);

  return {
    settings: data?.settings || {},
    lastUpdated: data?.lastUpdated || 0,
    isLoading: data === undefined,
  };
};

// Hook for getting all site settings (admin version - requires authentication)
export const useAdminSiteSettings = () => {
  const data = useQuery(api.siteSettings.getAllSettings);

  return {
    settings: data?.settings || {},
    lastUpdated: data?.lastUpdated || 0,
    isLoading: data === undefined,
  };
};

// Hook for getting a specific setting (public version)
export const useSetting = (key: string) => {
  const setting = useQuery(api.siteSettings.getPublicSetting, { key });

  return {
    value: setting,
    isLoading: setting === undefined,
  };
};

// Hook for getting a specific setting (admin version - requires authentication)
export const useAdminSetting = (key: string) => {
  const setting = useQuery(api.siteSettings.getSetting, { key });

  return {
    value: setting,
    isLoading: setting === undefined,
  };
};

// Hook for getting settings by category (public version)
export const useSettingsByCategory = (category: string) => {
  const settings = useQuery(api.siteSettings.getPublicSettingsByCategory, { category });

  return {
    settings: settings || {},
    isLoading: settings === undefined,
  };
};

// Hook for getting settings by category (admin version - requires authentication)
export const useAdminSettingsByCategory = (category: string) => {
  const settings = useQuery(api.siteSettings.getSettingsByCategory, { category });

  return {
    settings: settings || {},
    isLoading: settings === undefined,
  };
};

// Hook for updating settings
export const useSettingsManager = () => {
  const updateSetting = useMutation(api.siteSettings.updateSetting);
  const updateMultipleSettings = useMutation(api.siteSettings.updateMultipleSettings);
  const deleteSetting = useMutation(api.siteSettings.deleteSetting);
  const initializeDefaultSettings = useMutation(api.siteSettings.initializeDefaultSettings);

  const updateSingleSetting = useCallback(async (
    key: string, 
    value: any, 
    description?: string
  ) => {
    try {
      await updateSetting({ key, value, description });
      return { success: true };
    } catch (error) {
      console.error('Failed to update setting:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }, [updateSetting]);

  const updateBulkSettings = useCallback(async (
    settings: Array<{ key: string; value: any; description?: string }>
  ) => {
    try {
      await updateMultipleSettings({ settings });
      return { success: true };
    } catch (error) {
      console.error('Failed to update settings:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }, [updateMultipleSettings]);

  const removeSetting = useCallback(async (key: string) => {
    try {
      await deleteSetting({ key });
      return { success: true };
    } catch (error) {
      console.error('Failed to delete setting:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }, [deleteSetting]);

  const initializeDefaults = useCallback(async () => {
    try {
      const result = await initializeDefaultSettings({});
      return { success: true, ...result };
    } catch (error) {
      console.error('Failed to initialize default settings:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }, [initializeDefaultSettings]);

  return {
    updateSetting: updateSingleSetting,
    updateMultipleSettings: updateBulkSettings,
    deleteSetting: removeSetting,
    initializeDefaultSettings: initializeDefaults,
  };
};

// Utility functions for working with settings
export const settingsUtils = {
  // Get nested setting value with dot notation
  getNestedValue: (settings: Record<string, any>, path: string, defaultValue?: any) => {
    const keys = path.split('.');
    let value = settings;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return defaultValue;
      }
    }
    
    return value;
  },

  // Set nested setting value with dot notation
  setNestedValue: (settings: Record<string, any>, path: string, value: any) => {
    const keys = path.split('.');
    const lastKey = keys.pop();
    
    if (!lastKey) return settings;
    
    let current = settings;
    for (const key of keys) {
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[lastKey] = value;
    return settings;
  },

  // Convert flat settings to nested object
  flatToNested: (flatSettings: Record<string, any>) => {
    const nested: Record<string, any> = {};
    
    Object.entries(flatSettings).forEach(([key, value]) => {
      settingsUtils.setNestedValue(nested, key, value);
    });
    
    return nested;
  },

  // Convert nested object to flat settings
  nestedToFlat: (nestedSettings: Record<string, any>, prefix = ''): Record<string, any> => {
    const flat: Record<string, any> = {};
    
    Object.entries(nestedSettings).forEach(([key, value]) => {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        Object.assign(flat, settingsUtils.nestedToFlat(value, fullKey));
      } else {
        flat[fullKey] = value;
      }
    });
    
    return flat;
  },

  // Validate setting value based on type
  validateSetting: (key: string, value: any): { valid: boolean; error?: string } => {
    const validations: Record<string, (val: any) => boolean> = {
      'general.site_url': (val) => typeof val === 'string' && /^https?:\/\/.+/.test(val),
      'general.admin_email': (val) => typeof val === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
      'general.support_email': (val) => typeof val === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
      'general.phone': (val) => typeof val === 'string' && val.length > 0,
      'email.smtp_port': (val) => typeof val === 'number' && val > 0 && val <= 65535,
      'performance.cache_duration': (val) => typeof val === 'number' && val >= 0,
    };

    const validator = validations[key];
    if (!validator) {
      return { valid: true }; // No specific validation
    }

    const isValid = validator(value);
    return {
      valid: isValid,
      error: isValid ? undefined : `Invalid value for ${key}`,
    };
  },

  // Get default value for a setting
  getDefaultValue: (key: string): any => {
    const defaults: Record<string, any> = {
      'general.site_name': 'OfficeTech Guinea',
      'general.site_description': 'Leading technology and security solutions in Guinea',
      'general.language': 'en',
      'general.currency': 'USD',
      'general.timezone': 'Africa/Conakry',
      'features.maintenance_mode': false,
      'features.registration_enabled': true,
      'features.analytics_enabled': true,
      'performance.cache_duration': 3600,
      'performance.image_optimization': true,
      'performance.lazy_loading': true,
    };

    return defaults[key];
  },

  // Format setting value for display
  formatValue: (key: string, value: any): string => {
    if (value === null || value === undefined) {
      return 'Not set';
    }

    if (typeof value === 'boolean') {
      return value ? 'Enabled' : 'Disabled';
    }

    if (typeof value === 'number') {
      if (key.includes('duration') || key.includes('timeout')) {
        return `${value} seconds`;
      }
      if (key.includes('port')) {
        return `Port ${value}`;
      }
      return value.toString();
    }

    if (typeof value === 'string') {
      if (key.includes('password') || key.includes('secret') || key.includes('key')) {
        return '••••••••';
      }
      if (key.includes('email')) {
        return value;
      }
      if (key.includes('url')) {
        return value;
      }
      return value;
    }

    if (Array.isArray(value)) {
      return value.join(', ');
    }

    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }

    return String(value);
  },
};

// Setting categories for organization
export const settingCategories = [
  {
    key: 'general',
    label: 'General Settings',
    description: 'Basic site information and configuration',
    icon: 'Settings',
  },
  {
    key: 'seo',
    label: 'SEO & Analytics',
    description: 'Search engine optimization and tracking',
    icon: 'TrendingUp',
  },
  {
    key: 'social',
    label: 'Social Media',
    description: 'Social media links and integration',
    icon: 'Share2',
  },
  {
    key: 'contact',
    label: 'Contact Information',
    description: 'Contact details and business hours',
    icon: 'Phone',
  },
  {
    key: 'email',
    label: 'Email Configuration',
    description: 'SMTP settings and email preferences',
    icon: 'Mail',
  },
  {
    key: 'features',
    label: 'Feature Flags',
    description: 'Enable or disable site features',
    icon: 'ToggleLeft',
  },
  {
    key: 'performance',
    label: 'Performance',
    description: 'Caching and optimization settings',
    icon: 'Zap',
  },
];

// Hook for getting settings organized by category (admin version - requires authentication)
export const useSettingsByCategories = () => {
  const { settings, isLoading } = useAdminSiteSettings();

  const categorizedSettings = settingCategories.reduce((acc, category) => {
    acc[category.key] = {};

    Object.entries(settings).forEach(([key, value]) => {
      if (key.startsWith(`${category.key}.`)) {
        const settingKey = key.replace(`${category.key}.`, '');
        acc[category.key][settingKey] = value;
      }
    });

    return acc;
  }, {} as Record<string, Record<string, any>>);

  return {
    categorizedSettings,
    categories: settingCategories,
    isLoading,
  };
};
