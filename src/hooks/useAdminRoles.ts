import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { UserRole } from "./useAuth";

// Hook for getting all users (admin only)
export const useAllUsers = (options?: {
  limit?: number;
  role?: string;
  isActive?: boolean;
}) => {
  const users = useQuery(api.adminRoles.getAllUsers, options);

  return {
    users,
    isLoading: users === undefined,
    hasError: users === null,
  };
};

// Hook for getting user role history
export const useUserRoleHistory = (userId: Id<"users">, limit?: number) => {
  const history = useQuery(api.adminRoles.getUserRoleHistory, { userId, limit });

  return {
    history,
    isLoading: history === undefined,
  };
};

// Hook for admin dashboard statistics
export const useAdminStats = () => {
  const stats = useQuery(api.adminRoles.getAdminStats);

  return {
    stats,
    isLoading: stats === undefined,
  };
};

// Hook for pending admin invitations (super admin only)
export const usePendingInvitations = (userRole?: string) => {
  const shouldSkip = userRole !== "super_admin";
  const invitations = useQuery(api.adminRoles.getPendingInvitations, shouldSkip ? "skip" : {});

  return {
    invitations: shouldSkip ? [] : invitations,
    isLoading: shouldSkip ? false : invitations === undefined,
  };
};

// Hook for admin role mutations
export const useAdminRoleMutations = () => {
  const updateUserRole = useMutation(api.adminRoles.updateUserRole);
  const updateUserStatus = useMutation(api.adminRoles.updateUserStatus);
  const createAdminInvitation = useMutation(api.adminRoles.createAdminInvitation);

  return {
    updateUserRole,
    updateUserStatus,
    createAdminInvitation,
  };
};

// Utility functions for role management
export const getRoleDisplayName = (role: UserRole): string => {
  const roleNames: Record<UserRole, string> = {
    viewer: "Viewer",
    content_editor: "Content Editor",
    admin: "Administrator",
    super_admin: "Super Administrator",
  };
  return roleNames[role] || role;
};

export const getRoleDescription = (role: UserRole): string => {
  const descriptions: Record<UserRole, string> = {
    viewer: "Can view content and media but cannot make changes",
    content_editor: "Can create, edit, and publish content and manage media",
    admin: "Can manage content, media, users, and system settings",
    super_admin: "Full system access including role management and system configuration",
  };
  return descriptions[role] || "";
};

export const getRoleColor = (role: UserRole): string => {
  const colors: Record<UserRole, string> = {
    viewer: "bg-gray-100 text-gray-800",
    content_editor: "bg-blue-100 text-blue-800",
    admin: "bg-purple-100 text-purple-800",
    super_admin: "bg-red-100 text-red-800",
  };
  return colors[role] || "bg-gray-100 text-gray-800";
};

export const canModifyRole = (currentUserRole: UserRole, targetUserRole: UserRole, newRole: UserRole): boolean => {
  // Super admins can modify anyone
  if (currentUserRole === "super_admin") return true;
  
  // Admins cannot modify super admins or create super admins
  if (currentUserRole === "admin") {
    if (targetUserRole === "super_admin" || newRole === "super_admin") return false;
    // Admins can modify viewers and content_editors, and demote other admins
    return true;
  }
  
  // Content editors and viewers cannot modify roles
  return false;
};

export const canModifyUserStatus = (currentUserRole: UserRole, targetUserRole: UserRole): boolean => {
  // Super admins can modify anyone except themselves (handled separately)
  if (currentUserRole === "super_admin") return true;
  
  // Admins cannot modify super admins or other admins
  if (currentUserRole === "admin") {
    return !["super_admin", "admin"].includes(targetUserRole);
  }
  
  // Content editors and viewers cannot modify user status
  return false;
};

export const formatUserName = (user: { firstName?: string; lastName?: string; email: string }): string => {
  if (user.firstName && user.lastName) {
    return `${user.firstName} ${user.lastName}`;
  }
  if (user.firstName) {
    return user.firstName;
  }
  return user.email;
};

export const formatTimeAgo = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const weeks = Math.floor(diff / (1000 * 60 * 60 * 24 * 7));
  const months = Math.floor(diff / (1000 * 60 * 60 * 24 * 30));
  
  if (minutes < 1) return "Just now";
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  if (weeks < 4) return `${weeks}w ago`;
  if (months < 12) return `${months}mo ago`;
  
  return new Date(timestamp).toLocaleDateString();
};

// User management utilities
export const useUserManagement = () => {
  const mutations = useAdminRoleMutations();

  const changeUserRole = async (
    userId: Id<"users">,
    newRole: UserRole,
    reason?: string
  ) => {
    return await mutations.updateUserRole({
      userId,
      newRole,
      reason,
    });
  };

  const toggleUserStatus = async (
    userId: Id<"users">,
    isActive: boolean,
    reason?: string
  ) => {
    return await mutations.updateUserStatus({
      userId,
      isActive,
      reason,
    });
  };

  const inviteAdmin = async (
    email: string,
    role: "content_editor" | "admin",
    message?: string
  ) => {
    return await mutations.createAdminInvitation({
      email,
      role,
      message,
    });
  };

  return {
    changeUserRole,
    toggleUserStatus,
    inviteAdmin,
  };
};

// Role hierarchy utilities
export const getRoleHierarchy = (): { role: UserRole; level: number; name: string; description: string }[] => {
  return [
    {
      role: "viewer",
      level: 1,
      name: "Viewer",
      description: "Read-only access to content and media",
    },
    {
      role: "content_editor",
      level: 2,
      name: "Content Editor",
      description: "Can create, edit, and publish content",
    },
    {
      role: "admin",
      level: 3,
      name: "Administrator",
      description: "Can manage users, content, and system settings",
    },
    {
      role: "super_admin",
      level: 4,
      name: "Super Administrator",
      description: "Full system access and control",
    },
  ];
};

export const isHigherRole = (role1: UserRole, role2: UserRole): boolean => {
  const hierarchy = getRoleHierarchy();
  const level1 = hierarchy.find(r => r.role === role1)?.level || 0;
  const level2 = hierarchy.find(r => r.role === role2)?.level || 0;
  return level1 > level2;
};

export const getAvailableRoles = (currentUserRole: UserRole): UserRole[] => {
  const allRoles: UserRole[] = ["viewer", "content_editor", "admin", "super_admin"];
  
  if (currentUserRole === "super_admin") {
    return allRoles;
  }
  
  if (currentUserRole === "admin") {
    return ["viewer", "content_editor", "admin"];
  }
  
  return [];
};

// Permission checking utilities
export const getPermissionsForRole = (role: UserRole): string[] => {
  const permissions: Record<UserRole, string[]> = {
    viewer: [
      "content:read",
      "media:read",
    ],
    content_editor: [
      "content:read",
      "content:write",
      "content:publish",
      "media:read",
      "media:write",
      "media:upload",
    ],
    admin: [
      "content:read",
      "content:write", 
      "content:publish",
      "content:delete",
      "media:read",
      "media:write",
      "media:upload",
      "media:delete",
      "users:read",
      "users:write",
      "analytics:read",
      "settings:read",
      "settings:write",
    ],
    super_admin: [
      "*", // All permissions
    ],
  };

  return permissions[role] || [];
};

export const formatPermissions = (permissions: string[]): string => {
  if (permissions.includes("*")) {
    return "All permissions";
  }
  
  const grouped = permissions.reduce((acc, permission) => {
    const [resource, action] = permission.split(":");
    if (!acc[resource]) acc[resource] = [];
    acc[resource].push(action);
    return acc;
  }, {} as Record<string, string[]>);

  return Object.entries(grouped)
    .map(([resource, actions]) => `${resource}: ${actions.join(", ")}`)
    .join(" | ");
};
