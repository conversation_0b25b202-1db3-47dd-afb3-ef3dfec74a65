import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { toast } from "sonner";

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  service?: string;
  message: string;
  source?: string;
}

export interface ContactFormSubmission {
  _id: Id<"contactForms">;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  service?: string;
  message: string;
  source?: string;
  status: "new" | "in_progress" | "resolved" | "closed";
  notes?: string;
  createdAt: number;
  updatedAt: number;
  updatedBy?: Id<"users">;
}

export const useContactForms = () => {
  const submitContactForm = useMutation(api.contactForms.submitContactForm);
  const getAllContactForms = useQuery(api.contactForms.getAllContactForms);
  const getContactFormStats = useQuery(api.contactForms.getContactFormStats);
  const getRecentContactForms = useQuery(api.contactForms.getRecentContactForms);
  const updateContactFormStatus = useMutation(api.contactForms.updateContactFormStatus);
  const deleteContactForm = useMutation(api.contactForms.deleteContactForm);

  const handleSubmitContactForm = async (data: ContactFormData) => {
    try {
      const contactId = await submitContactForm(data);
      toast.success("Thank you! Your message has been sent successfully.");
      return contactId;
    } catch (error) {
      console.error("Error submitting contact form:", error);
      toast.error("Failed to send message. Please try again.");
      throw error;
    }
  };

  const handleUpdateStatus = async (
    id: Id<"contactForms">, 
    status: "new" | "in_progress" | "resolved" | "closed",
    notes?: string
  ) => {
    try {
      await updateContactFormStatus({ id, status, notes });
      toast.success("Contact form status updated successfully");
    } catch (error) {
      console.error("Error updating contact form status:", error);
      toast.error("Failed to update status");
      throw error;
    }
  };

  const handleDeleteContactForm = async (id: Id<"contactForms">) => {
    try {
      await deleteContactForm({ id });
      toast.success("Contact form deleted successfully");
    } catch (error) {
      console.error("Error deleting contact form:", error);
      toast.error("Failed to delete contact form");
      throw error;
    }
  };

  return {
    // Data
    contactForms: getAllContactForms,
    contactFormStats: getContactFormStats,
    recentContactForms: getRecentContactForms,
    
    // Actions
    submitContactForm: handleSubmitContactForm,
    updateContactFormStatus: handleUpdateStatus,
    deleteContactForm: handleDeleteContactForm,
  };
};

// Hook for submitting contact forms (public use)
export const useContactFormSubmission = () => {
  const submitContactForm = useMutation(api.contactForms.submitContactForm);

  const submitForm = async (data: ContactFormData) => {
    try {
      const contactId = await submitContactForm(data);
      toast.success("Thank you! Your message has been sent successfully.");
      return contactId;
    } catch (error) {
      console.error("Error submitting contact form:", error);
      toast.error("Failed to send message. Please try again.");
      throw error;
    }
  };

  return {
    submitContactForm: submitForm,
  };
};

// Hook for admin contact form management
export const useContactFormManagement = () => {
  const contactForms = useQuery(api.contactForms.getAllContactForms);
  const stats = useQuery(api.contactForms.getContactFormStats);
  const updateStatus = useMutation(api.contactForms.updateContactFormStatus);
  const deleteForm = useMutation(api.contactForms.deleteContactForm);

  const updateContactFormStatus = async (
    id: Id<"contactForms">, 
    status: "new" | "in_progress" | "resolved" | "closed",
    notes?: string
  ) => {
    try {
      await updateStatus({ id, status, notes });
      toast.success("Status updated successfully");
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Failed to update status");
      throw error;
    }
  };

  const deleteContactForm = async (id: Id<"contactForms">) => {
    try {
      await deleteForm({ id });
      toast.success("Contact form deleted");
    } catch (error) {
      console.error("Error deleting form:", error);
      toast.error("Failed to delete form");
      throw error;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new":
        return "bg-blue-100 text-blue-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "resolved":
        return "bg-green-100 text-green-800";
      case "closed":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "new":
        return "New";
      case "in_progress":
        return "In Progress";
      case "resolved":
        return "Resolved";
      case "closed":
        return "Closed";
      default:
        return status;
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTimeAgo = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 60) {
      return `${minutes} minutes ago`;
    } else if (hours < 24) {
      return `${hours} hours ago`;
    } else {
      return `${days} days ago`;
    }
  };

  return {
    contactForms,
    stats,
    updateContactFormStatus,
    deleteContactForm,
    getStatusColor,
    getStatusLabel,
    formatDate,
    getTimeAgo,
  };
};

// Validation helpers
export const validateContactForm = (data: Partial<ContactFormData>) => {
  const errors: Record<string, string> = {};

  if (!data.name?.trim()) {
    errors.name = "Name is required";
  }

  if (!data.email?.trim()) {
    errors.email = "Email is required";
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.email = "Please enter a valid email address";
  }

  if (!data.message?.trim()) {
    errors.message = "Message is required";
  } else if (data.message.length < 10) {
    errors.message = "Message must be at least 10 characters long";
  }

  if (data.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(data.phone.replace(/\s/g, ''))) {
    errors.phone = "Please enter a valid phone number";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};
