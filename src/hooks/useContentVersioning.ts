import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";

// Hook for getting draft content
export const useDraft = (identifier: string, language: string = "en") => {
  const draft = useQuery(api.contentVersioning.getDraft, { identifier, language });

  return {
    draft,
    isLoading: draft === undefined,
    hasDraft: draft !== null,
  };
};

// Hook for getting content timeline
export const useContentTimeline = (identifier: string, language: string = "en", limit?: number) => {
  const timeline = useQuery(api.contentVersioning.getContentTimeline, { 
    identifier, 
    language, 
    limit 
  });

  return {
    timeline,
    isLoading: timeline === undefined,
  };
};

// Hook for comparing versions
export const useVersionComparison = (
  identifier: string, 
  language: string, 
  version1: number, 
  version2: number
) => {
  const comparison = useQuery(
    api.contentVersioning.compareVersions,
    version1 && version2 ? { identifier, language, version1, version2 } : "skip"
  );

  return {
    comparison,
    isLoading: comparison === undefined,
  };
};

// Hook for scheduled content
export const useScheduledContent = (limit?: number) => {
  const scheduled = useQuery(api.contentVersioning.getScheduledContent, { limit });

  return {
    scheduled,
    isLoading: scheduled === undefined,
  };
};

// Hook for versioning mutations
export const useVersioningMutations = () => {
  const createDraft = useMutation(api.contentVersioning.createDraft);
  const publishDraft = useMutation(api.contentVersioning.publishDraft);
  const schedulePublication = useMutation(api.contentVersioning.schedulePublication);
  const discardDraft = useMutation(api.contentVersioning.discardDraft);

  return {
    createDraft,
    publishDraft,
    schedulePublication,
    discardDraft,
  };
};

// Enhanced content manager with versioning
export const useVersionedContentManager = (identifier: string, language: string = "en") => {
  const { content, isLoading: isContentLoading } = useQuery(
    api.content.getContent,
    { identifier, language, includeDraft: false }
  ) ? { content: useQuery(api.content.getContent, { identifier, language, includeDraft: false }), isLoading: false } : { content: null, isLoading: true };
  
  const { draft, isLoading: isDraftLoading, hasDraft } = useDraft(identifier, language);
  const { timeline, isLoading: isTimelineLoading } = useContentTimeline(identifier, language);
  const mutations = useVersioningMutations();

  const saveDraft = async (data: any, changeNote?: string) => {
    if (!content?.contentType) {
      throw new Error("Content type not found");
    }

    return await mutations.createDraft({
      identifier,
      language,
      data,
      contentTypeId: content.contentTypeId,
      changeNote,
    });
  };

  const publishContent = async (publishNote?: string, scheduledFor?: number) => {
    if (scheduledFor) {
      return await mutations.schedulePublication({
        identifier,
        language,
        scheduledFor,
        publishNote,
      });
    } else {
      return await mutations.publishDraft({
        identifier,
        language,
        publishNote,
      });
    }
  };

  const discardChanges = async () => {
    return await mutations.discardDraft({
      identifier,
      language,
    });
  };

  const isLoading = isContentLoading || isDraftLoading;
  const currentData = draft?.data || content?.data;
  const isPublished = content?.status === "published";
  const hasUnpublishedChanges = hasDraft;

  return {
    // Data
    publishedContent: content,
    draftContent: draft,
    currentData,
    timeline,
    
    // Status
    isLoading,
    isPublished,
    hasDraft,
    hasUnpublishedChanges,
    
    // Actions
    saveDraft,
    publishContent,
    discardChanges,
    
    // Metadata
    publishedVersion: content?.version,
    draftVersion: draft?.version,
    lastModified: draft?.updatedAt || content?.updatedAt,
  };
};

// Utility functions for version management
export const formatVersionNumber = (version: number): string => {
  return `v${version}`;
};

export const getVersionStatus = (
  publishedVersion?: number,
  draftVersion?: number
): "published" | "draft" | "ahead" | "behind" => {
  if (!publishedVersion && draftVersion) return "draft";
  if (publishedVersion && !draftVersion) return "published";
  if (publishedVersion && draftVersion) {
    if (draftVersion > publishedVersion) return "ahead";
    if (draftVersion < publishedVersion) return "behind";
  }
  return "published";
};

export const getChangeTypeIcon = (changeType: string): string => {
  switch (changeType) {
    case "created":
      return "✨";
    case "updated":
      return "📝";
    case "published":
      return "🚀";
    case "unpublished":
      return "📥";
    case "reverted":
      return "↩️";
    case "deleted":
      return "🗑️";
    default:
      return "📄";
  }
};

export const getChangeTypeColor = (changeType: string): string => {
  switch (changeType) {
    case "created":
      return "text-green-600 bg-green-100";
    case "updated":
      return "text-blue-600 bg-blue-100";
    case "published":
      return "text-purple-600 bg-purple-100";
    case "unpublished":
      return "text-orange-600 bg-orange-100";
    case "reverted":
      return "text-yellow-600 bg-yellow-100";
    case "deleted":
      return "text-red-600 bg-red-100";
    default:
      return "text-gray-600 bg-gray-100";
  }
};

export const formatTimeAgo = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 1) return "Just now";
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  
  return new Date(timestamp).toLocaleDateString();
};

// Content workflow helpers
export const useContentWorkflow = () => {
  const mutations = useVersioningMutations();

  const createContentWorkflow = async (
    identifier: string,
    language: string,
    contentTypeId: Id<"contentTypes">,
    initialData: any
  ) => {
    // Create initial draft
    const draftId = await mutations.createDraft({
      identifier,
      language,
      data: initialData,
      contentTypeId,
      changeNote: "Initial content creation",
    });

    return draftId;
  };

  const reviewAndPublish = async (
    identifier: string,
    language: string,
    reviewNote?: string
  ) => {
    return await mutations.publishDraft({
      identifier,
      language,
      publishNote: reviewNote || "Content reviewed and approved",
    });
  };

  const scheduleForLater = async (
    identifier: string,
    language: string,
    publishDate: Date,
    note?: string
  ) => {
    return await mutations.schedulePublication({
      identifier,
      language,
      scheduledFor: publishDate.getTime(),
      publishNote: note,
    });
  };

  return {
    createContentWorkflow,
    reviewAndPublish,
    scheduleForLater,
  };
};
