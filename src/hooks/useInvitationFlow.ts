import { useEffect, useState } from "react";
import { useAuth } from "@clerk/clerk-react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

interface InvitationInfo {
  hasInvitation: boolean;
  role?: string;
  message?: string;
  invitedBy?: string;
  expiresAt?: number;
}

export const useInvitationFlow = () => {
  const { user, isLoaded } = useAuth();
  const [invitationChecked, setInvitationChecked] = useState(false);
  const [invitationInfo, setInvitationInfo] = useState<InvitationInfo | null>(null);

  // Only check for invitation if user is loaded and has an email
  const userEmail = user?.emailAddresses?.[0]?.emailAddress;
  const shouldCheckInvitation = isLoaded && userEmail && !invitationChecked;

  const pendingInvitation = useQuery(
    api.users.checkPendingInvitation,
    shouldCheckInvitation ? { email: userEmail } : "skip"
  );

  useEffect(() => {
    if (pendingInvitation !== undefined && shouldCheckInvitation) {
      setInvitationInfo(pendingInvitation);
      setInvitationChecked(true);
    }
  }, [pendingInvitation, shouldCheckInvitation]);

  const resetInvitationCheck = () => {
    setInvitationChecked(false);
    setInvitationInfo(null);
  };

  return {
    invitationInfo,
    isCheckingInvitation: shouldCheckInvitation && pendingInvitation === undefined,
    resetInvitationCheck,
  };
};
