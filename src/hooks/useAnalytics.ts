import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useCallback, useEffect } from "react";

// Generate session ID for tracking
const getSessionId = (): string => {
  let sessionId = sessionStorage.getItem('analytics_session_id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('analytics_session_id', sessionId);
  }
  return sessionId;
};

// Hook for dashboard analytics
export const useDashboardAnalytics = (dateRange: string = "30d") => {
  const analytics = useQuery(api.analytics.getDashboardAnalytics, { dateRange });

  return {
    analytics,
    isLoading: analytics === undefined,
  };
};

// Hook for content analytics
export const useContentAnalytics = (contentId?: string, dateRange: string = "30d") => {
  const analytics = useQuery(api.analytics.getContentAnalytics, { 
    contentId, 
    dateRange 
  });

  return {
    analytics,
    isLoading: analytics === undefined,
  };
};

// Hook for user activity analytics
export const useUserActivityAnalytics = (userId?: string, dateRange: string = "30d") => {
  const analytics = useQuery(api.analytics.getUserActivityAnalytics, { 
    userId: userId as any, 
    dateRange 
  });

  return {
    analytics,
    isLoading: analytics === undefined,
  };
};

// Hook for system metrics
export const useSystemMetrics = () => {
  const metrics = useQuery(api.analytics.getSystemMetrics);

  return {
    metrics,
    isLoading: metrics === undefined,
  };
};

// Hook for tracking analytics events
export const useAnalyticsTracking = () => {
  const trackPageView = useMutation(api.analytics.trackPageView);
  const trackContentInteraction = useMutation(api.analytics.trackContentInteraction);
  const trackFormSubmission = useMutation(api.analytics.trackFormSubmission);

  const sessionId = getSessionId();

  // Track page view
  const trackPage = useCallback(async (path: string, title?: string) => {
    try {
      await trackPageView({
        path,
        title,
        referrer: document.referrer || undefined,
        userAgent: navigator.userAgent,
        sessionId,
      });
    } catch (error) {
      console.warn('Failed to track page view:', error);
    }
  }, [trackPageView, sessionId]);

  // Track content interaction
  const trackContent = useCallback(async (
    contentId: string,
    contentType: string,
    action: string,
    metadata?: any
  ) => {
    try {
      await trackContentInteraction({
        contentId,
        contentType,
        action,
        sessionId,
        metadata,
      });
    } catch (error) {
      console.warn('Failed to track content interaction:', error);
    }
  }, [trackContentInteraction, sessionId]);

  // Track form submission
  const trackForm = useCallback(async (
    formType: string,
    formId: string,
    success: boolean,
    metadata?: any
  ) => {
    try {
      await trackFormSubmission({
        formType,
        formId,
        success,
        sessionId,
        metadata,
      });
    } catch (error) {
      console.warn('Failed to track form submission:', error);
    }
  }, [trackFormSubmission, sessionId]);

  return {
    trackPage,
    trackContent,
    trackForm,
    sessionId,
  };
};

// Hook for automatic page tracking
export const usePageTracking = () => {
  const { trackPage } = useAnalyticsTracking();

  useEffect(() => {
    // Track initial page load
    trackPage(window.location.pathname, document.title);

    // Track page changes (for SPA navigation)
    const handleLocationChange = () => {
      trackPage(window.location.pathname, document.title);
    };

    // Listen for popstate events (back/forward navigation)
    window.addEventListener('popstate', handleLocationChange);

    // For React Router, we'd also need to listen for route changes
    // This is a simplified version - in a real app you'd integrate with your router
    
    return () => {
      window.removeEventListener('popstate', handleLocationChange);
    };
  }, [trackPage]);
};

// Utility functions for analytics data processing
export const formatAnalyticsData = {
  // Format numbers with appropriate suffixes
  formatNumber: (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  },

  // Format percentage
  formatPercentage: (num: number): string => {
    return num.toFixed(1) + '%';
  },

  // Format date for charts
  formatDate: (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  },

  // Calculate percentage change
  calculateChange: (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  },

  // Get trend direction
  getTrend: (change: number): 'up' | 'down' | 'neutral' => {
    if (change > 0) return 'up';
    if (change < 0) return 'down';
    return 'neutral';
  },

  // Format time ago
  formatTimeAgo: (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  },

  // Generate chart data for time series
  generateTimeSeriesData: (data: Record<string, number>, dateRange: string) => {
    const days = dateRange === "7d" ? 7 : dateRange === "30d" ? 30 : dateRange === "90d" ? 90 : 365;
    const result = [];
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateString = date.toISOString().split('T')[0];
      
      result.push({
        date: dateString,
        value: data[dateString] || 0,
        formattedDate: formatAnalyticsData.formatDate(dateString),
      });
    }
    
    return result;
  },
};

// Analytics event types for type safety
export type AnalyticsEvent = {
  pageView: {
    path: string;
    title?: string;
  };
  contentInteraction: {
    contentId: string;
    contentType: string;
    action: 'view' | 'edit' | 'publish' | 'delete';
    metadata?: any;
  };
  formSubmission: {
    formType: string;
    formId: string;
    success: boolean;
    metadata?: any;
  };
};

// Custom hook for tracking specific events with type safety
export const useTypedAnalytics = () => {
  const { trackPage, trackContent, trackForm } = useAnalyticsTracking();

  const track = {
    pageView: (event: AnalyticsEvent['pageView']) => 
      trackPage(event.path, event.title),
    
    contentInteraction: (event: AnalyticsEvent['contentInteraction']) =>
      trackContent(event.contentId, event.contentType, event.action, event.metadata),
    
    formSubmission: (event: AnalyticsEvent['formSubmission']) =>
      trackForm(event.formType, event.formId, event.success, event.metadata),
  };

  return track;
};

// Hook for real-time analytics updates
export const useRealTimeAnalytics = (refreshInterval: number = 30000) => {
  const { analytics, isLoading } = useDashboardAnalytics();
  const { metrics } = useSystemMetrics();

  // In a real implementation, you might use WebSockets or Server-Sent Events
  // For now, we'll rely on Convex's real-time updates

  return {
    analytics,
    metrics,
    isLoading,
    lastUpdated: analytics?.lastUpdated || Date.now(),
  };
};

// Date range options for analytics
export const analyticsDateRanges = [
  { value: "7d", label: "Last 7 days" },
  { value: "30d", label: "Last 30 days" },
  { value: "90d", label: "Last 90 days" },
  { value: "1y", label: "Last year" },
];

// Chart color schemes
export const chartColors = {
  primary: "#3B82F6",
  secondary: "#10B981",
  accent: "#F59E0B",
  danger: "#EF4444",
  info: "#6366F1",
  success: "#059669",
  warning: "#D97706",
  gradient: {
    blue: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
    green: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
    purple: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
  },
};
