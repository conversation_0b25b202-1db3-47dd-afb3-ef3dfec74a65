import { createContext, useContext, ReactNode } from "react";
import { useSiteSettings } from "@/hooks/useSiteSettings";

interface SettingsContextType {
  settings: Record<string, any>;
  isLoading: boolean;
  getSetting: (key: string, defaultValue?: any) => any;
  getNestedSetting: (path: string, defaultValue?: any) => any;
}

const SettingsContext = createContext<SettingsContextType | null>(null);

interface SettingsProviderProps {
  children: ReactNode;
}

export const SettingsProvider = ({ children }: SettingsProviderProps) => {
  const { settings, isLoading } = useSiteSettings();

  const getSetting = (key: string, defaultValue?: any) => {
    return settings[key] !== undefined ? settings[key] : defaultValue;
  };

  const getNestedSetting = (path: string, defaultValue?: any) => {
    const keys = path.split('.');
    let value = settings;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return defaultValue;
      }
    }
    
    return value !== undefined ? value : defaultValue;
  };

  const contextValue: SettingsContextType = {
    settings,
    isLoading,
    getSetting,
    getNestedSetting,
  };

  return (
    <SettingsContext.Provider value={contextValue}>
      {children}
    </SettingsContext.Provider>
  );
};

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error("useSettings must be used within a SettingsProvider");
  }
  return context;
};

// Convenience hooks for specific setting categories
export const useGeneralSettings = () => {
  const { getSetting } = useSettings();
  
  return {
    siteName: getSetting("general.site_name", "OfficeTech Guinea"),
    siteDescription: getSetting("general.site_description", "Leading technology and security solutions in Guinea"),
    siteUrl: getSetting("general.site_url", "https://officetech.gn"),
    adminEmail: getSetting("general.admin_email", "<EMAIL>"),
    supportEmail: getSetting("general.support_email", "<EMAIL>"),
    phone: getSetting("general.phone", "+224 123 456 789"),
    address: getSetting("general.address", "Conakry, Guinea"),
    language: getSetting("general.language", "en"),
    currency: getSetting("general.currency", "USD"),
    timezone: getSetting("general.timezone", "Africa/Conakry"),
  };
};

export const useSEOSettings = () => {
  const { getSetting } = useSettings();
  
  return {
    metaTitle: getSetting("seo.meta_title", "OfficeTech Guinea - Technology & Security Solutions"),
    metaDescription: getSetting("seo.meta_description", "Leading provider of technology and security solutions in Guinea"),
    metaKeywords: getSetting("seo.meta_keywords", "technology, security, network, Guinea, enterprise, IT support"),
    ogImage: getSetting("seo.og_image", "/images/og-default.jpg"),
    twitterHandle: getSetting("seo.twitter_handle", "@officetechgn"),
    googleAnalyticsId: getSetting("seo.google_analytics_id", ""),
    googleTagManagerId: getSetting("seo.google_tag_manager_id", ""),
    facebookPixelId: getSetting("seo.facebook_pixel_id", ""),
  };
};

export const useSocialSettings = () => {
  const { getSetting } = useSettings();
  
  return {
    facebookUrl: getSetting("social.facebook_url", ""),
    twitterUrl: getSetting("social.twitter_url", ""),
    linkedinUrl: getSetting("social.linkedin_url", ""),
    instagramUrl: getSetting("social.instagram_url", ""),
    youtubeUrl: getSetting("social.youtube_url", ""),
    showSocialHeader: getSetting("social.show_social_header", true),
    showSocialFooter: getSetting("social.show_social_footer", true),
    showSocialContact: getSetting("social.show_social_contact", true),
    socialOpenNewTab: getSetting("social.social_open_new_tab", true),
  };
};

export const useContactSettings = () => {
  const { getSetting } = useSettings();
  
  return {
    showPhone: getSetting("contact.show_phone", true),
    showEmail: getSetting("contact.show_email", true),
    showAddress: getSetting("contact.show_address", true),
    businessHours: getSetting("contact.business_hours", "Monday-Friday: 8AM-6PM"),
    emergencySupport: getSetting("contact.emergency_support", "24/7 Emergency Support Available"),
    contactFormEnabled: getSetting("contact.contact_form_enabled", true),
    contactFormRecipient: getSetting("contact.contact_form_recipient", "<EMAIL>"),
    contactFormSubject: getSetting("contact.contact_form_subject", "New Contact Form Submission"),
  };
};

export const useFeatureFlags = () => {
  const { getSetting } = useSettings();
  
  return {
    maintenanceMode: getSetting("features.maintenance_mode", false),
    registrationEnabled: getSetting("features.registration_enabled", true),
    commentsEnabled: getSetting("features.comments_enabled", true),
    analyticsEnabled: getSetting("features.analytics_enabled", true),
    newsletterEnabled: getSetting("features.newsletter_enabled", true),
    contactFormEnabled: getSetting("features.contact_form_enabled", true),
    searchEnabled: getSetting("features.search_enabled", true),
    userProfilesPublic: getSetting("features.user_profiles_public", false),
    apiAccessEnabled: getSetting("features.api_access_enabled", false),
    debugMode: getSetting("features.debug_mode", false),
  };
};

export const usePerformanceSettings = () => {
  const { getSetting } = useSettings();
  
  return {
    cacheDuration: getSetting("performance.cache_duration", 3600),
    imageOptimization: getSetting("performance.image_optimization", true),
    lazyLoading: getSetting("performance.lazy_loading", true),
    compressionEnabled: getSetting("performance.compression_enabled", true),
    compressionLevel: getSetting("performance.compression_level", 6),
    imageQuality: getSetting("performance.image_quality", 85),
    maxImageWidth: getSetting("performance.max_image_width", 1920),
    enableCaching: getSetting("performance.enable_caching", true),
    cacheApiResponses: getSetting("performance.cache_api_responses", true),
    webpConversion: getSetting("performance.webp_conversion", true),
    preloadCriticalResources: getSetting("performance.preload_critical_resources", true),
    minifyCss: getSetting("performance.minify_css", true),
    minifyJs: getSetting("performance.minify_js", true),
  };
};

// Hook for checking if a feature is enabled
export const useFeatureFlag = (flagName: string): boolean => {
  const { getSetting } = useSettings();
  return getSetting(`features.${flagName}`, false);
};

// Hook for getting theme/branding settings
export const useBrandingSettings = () => {
  const { getSetting } = useSettings();
  
  return {
    primaryColor: getSetting("branding.primary_color", "#3B82F6"),
    secondaryColor: getSetting("branding.secondary_color", "#10B981"),
    accentColor: getSetting("branding.accent_color", "#F59E0B"),
    logoUrl: getSetting("branding.logo_url", "/images/logo.png"),
    faviconUrl: getSetting("branding.favicon_url", "/favicon.ico"),
    fontFamily: getSetting("branding.font_family", "Inter"),
  };
};

// Hook for getting email settings (for admin use)
export const useEmailSettings = () => {
  const { getSetting } = useSettings();
  
  return {
    smtpHost: getSetting("email.smtp_host", ""),
    smtpPort: getSetting("email.smtp_port", 587),
    smtpUsername: getSetting("email.smtp_username", ""),
    smtpPassword: getSetting("email.smtp_password", ""),
    smtpSecure: getSetting("email.smtp_secure", true),
    fromName: getSetting("email.from_name", "OfficeTech Guinea"),
    fromEmail: getSetting("email.from_email", "<EMAIL>"),
    replyToName: getSetting("email.reply_to_name", "OfficeTech Support"),
    replyToEmail: getSetting("email.reply_to_email", "<EMAIL>"),
  };
};
