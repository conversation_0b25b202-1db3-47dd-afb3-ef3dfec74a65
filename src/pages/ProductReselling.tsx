import Layout from '@/components/Layout';
import { Package, Shield, Truck, Award, Monitor, Smartphone, Wifi, Server } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from '@/contexts/I18nContext';

const ProductReselling = () => {
  const { t } = useTranslation();

  const categories = [
    {
      title: 'Network Equipment',
      description: 'Professional networking hardware for businesses and enterprises',
      icon: Wifi,
      products: [
        'Enterprise Routers',
        'Managed Switches',
        'Wireless Access Points',
        'Network Security Appliances'
      ],
      brands: ['Cisco', 'Ubiquiti', 'TP-Link', 'Mikrotik']
    },
    {
      title: 'Computer Hardware',
      description: 'Desktop computers, laptops, and accessories for all business needs',
      icon: Monitor,
      products: [
        'Business Laptops',
        'Desktop Computers',
        'Workstations',
        'Computer Accessories'
      ],
      brands: ['Dell', 'HP', 'Lenovo', 'ASUS']
    },
    {
      title: 'Mobile Devices',
      description: 'Smartphones, tablets, and mobile accessories for modern communication',
      icon: Smartphone,
      products: [
        'Business Smartphones',
        'Tablets',
        'Mobile Accessories',
        'Communication Devices'
      ],
      brands: ['Samsung', 'Apple', 'Huawei', 'Xiaomi']
    },
    {
      title: 'Server Solutions',
      description: 'Enterprise servers and storage solutions for data management',
      icon: Server,
      products: [
        'Rack Servers',
        'Storage Systems',
        'Server Accessories',
        'Backup Solutions'
      ],
      brands: ['Dell EMC', 'HPE', 'IBM', 'Synology']
    }
  ];

  const features = [
    {
      icon: Shield,
      title: 'Authorized Dealer',
      description: 'Official partnerships with leading technology brands worldwide.'
    },
    {
      icon: Award,
      title: 'Quality Guarantee',
      description: 'All products come with manufacturer warranties and quality assurance.'
    },
    {
      icon: Truck,
      title: 'Fast Delivery',
      description: 'Quick delivery across Guinea with professional installation services.'
    },
    {
      icon: Package,
      title: 'Complete Solutions',
      description: 'End-to-end technology solutions from procurement to implementation.'
    }
  ];

  const services = [
    {
      title: 'Product Consultation',
      description: 'Expert advice to help you choose the right technology products for your needs.',
      included: true
    },
    {
      title: 'Professional Installation',
      description: 'Certified technicians handle installation and configuration of all equipment.',
      included: true
    },
    {
      title: 'Training & Support',
      description: 'Comprehensive training and ongoing technical support for all products.',
      included: true
    },
    {
      title: 'Maintenance Services',
      description: 'Regular maintenance and updates to keep your technology running smoothly.',
      included: false
    },
    {
      title: 'Extended Warranty',
      description: 'Additional warranty coverage beyond manufacturer guarantees.',
      included: false
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-white">
        {/* Hero Section */}
        <section className="relative py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <span className="text-gradient">Product Reselling</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Quality technology products and equipment from leading global brands. 
              We provide complete solutions from procurement to implementation.
            </p>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Why Choose Our Products?
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                We partner with leading technology brands to provide you with reliable, 
                high-quality products backed by comprehensive support.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <Icon className="w-6 h-6 text-green-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 text-sm">
                        {feature.description}
                      </p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Product Categories */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Product Categories
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Explore our comprehensive range of technology products across different categories.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {categories.map((category, index) => {
                const Icon = category.icon;
                return (
                  <Card key={index} className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4 mb-4">
                        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Icon className="w-6 h-6 text-green-600" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-900 mb-2">
                            {category.title}
                          </h3>
                          <p className="text-gray-600">
                            {category.description}
                          </p>
                        </div>
                      </div>

                      <div className="mb-4">
                        <h4 className="font-semibold text-gray-900 mb-2">Products:</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {category.products.map((product, productIndex) => (
                            <div key={productIndex} className="text-sm text-gray-600 flex items-center">
                              <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-2"></div>
                              {product}
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="mb-6">
                        <h4 className="font-semibold text-gray-900 mb-2">Brands:</h4>
                        <div className="flex flex-wrap gap-2">
                          {category.brands.map((brand, brandIndex) => (
                            <Badge key={brandIndex} variant="outline">
                              {brand}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <Button className="w-full">
                        View Products
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-6">
                  Complete Service Package
                </h2>
                <p className="text-gray-600 mb-6">
                  Beyond just selling products, we provide comprehensive services to ensure 
                  you get the most value from your technology investments.
                </p>

                <div className="space-y-4">
                  {services.map((service, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className={`w-5 h-5 rounded-full flex items-center justify-center mt-0.5 ${
                        service.included ? 'bg-green-100' : 'bg-gray-100'
                      }`}>
                        <div className={`w-2 h-2 rounded-full ${
                          service.included ? 'bg-green-600' : 'bg-gray-400'
                        }`}></div>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          {service.title}
                          {service.included && (
                            <Badge className="ml-2 bg-green-100 text-green-800">Included</Badge>
                          )}
                        </h3>
                        <p className="text-gray-600 text-sm">
                          {service.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-gradient-to-br from-green-100 to-green-50 rounded-2xl p-8">
                <div className="text-center">
                  <Package className="w-16 h-16 text-green-600 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    Need a Custom Solution?
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Contact our experts to discuss your specific requirements and get a customized quote.
                  </p>
                  <div className="space-y-3">
                    <Button size="lg" className="w-full">
                      Request Quote
                    </Button>
                    <Button size="lg" variant="outline" className="w-full">
                      View Catalog
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
};

export default ProductReselling;
