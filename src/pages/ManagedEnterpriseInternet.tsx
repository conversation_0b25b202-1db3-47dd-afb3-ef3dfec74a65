import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Layout from '@/components/Layout';
import { ContentProvider } from '@/components/content/ContentProvider';
import { useLanguage } from '@/contexts/I18nContext';
import { BreadcrumbNav, generateBreadcrumbs } from '@/components/BreadcrumbNav';
import {
  Network,
  ArrowLeft,
  CheckCircle,
  Phone,
  Mail,
  Shield,
  Zap,
  Users,
  Settings,
  Monitor,
  Lock,
  Globe,
  Server,
  Clock,
  TrendingUp,
  AlertTriangle,
  Star
} from "lucide-react";

const ManagedEnterpriseInternetContent = () => {
  const { language } = useLanguage();

  const breadcrumbs = generateBreadcrumbs('network-solution', 'Managed Enterprise Internet', language, (key: string) => key);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <BreadcrumbNav items={breadcrumbs} />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 text-white">
        <div className="container mx-auto px-4 py-20">
          <div className="max-w-4xl mx-auto">
            <Link to="/network-solutions" className="inline-flex items-center text-purple-200 hover:text-white mb-6">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Network Solutions
            </Link>
            
            <div className="flex items-center mb-6">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mr-6">
                <Network className="w-8 h-8 text-purple-600" />
              </div>
              <div>
                <Badge className="mb-2 bg-purple-100 text-purple-800">Fully Managed Service</Badge>
                <h1 className="text-5xl font-bold">Managed Enterprise Internet</h1>
              </div>
            </div>
            
            <p className="text-xl text-purple-100 mb-8 leading-relaxed">
              Experience worry-free internet connectivity with our comprehensive managed enterprise 
              internet service. We handle everything from installation to 24/7 monitoring, security, 
              and performance optimization, so you can focus on your business.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-white text-purple-900 hover:bg-gray-100">
                <Phone className="w-5 h-5 mr-2" />
                Get Managed Service
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-900">
                <Mail className="w-5 h-5 mr-2" />
                Service Overview
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Service Overview */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Complete Internet Management Solution
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our managed enterprise internet service provides end-to-end management of your 
              internet connectivity, from infrastructure to security and performance optimization.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Monitor className="w-6 h-6 text-purple-600" />
                </div>
                <CardTitle className="text-xl">24/7 Monitoring</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-center mb-4">
                  Continuous network monitoring with proactive issue detection 
                  and resolution before they impact your business.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    Real-time performance monitoring
                  </li>
                  <li className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    Automated alert systems
                  </li>
                  <li className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    Proactive issue resolution
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-6 h-6 text-blue-600" />
                </div>
                <CardTitle className="text-xl">Advanced Security</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-center mb-4">
                  Enterprise-grade security with firewall management, threat detection, 
                  and comprehensive protection against cyber threats.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    Managed firewall services
                  </li>
                  <li className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    Intrusion detection & prevention
                  </li>
                  <li className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    Regular security updates
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="w-6 h-6 text-green-600" />
                </div>
                <CardTitle className="text-xl">Performance Optimization</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-center mb-4">
                  Continuous optimization of your internet performance with 
                  bandwidth management and application prioritization.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    Bandwidth optimization
                  </li>
                  <li className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    Application prioritization
                  </li>
                  <li className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    Performance reporting
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* What's Included */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Everything You Need, Fully Managed
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our comprehensive managed service includes all the components and support 
              needed for enterprise-grade internet connectivity.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Infrastructure & Hardware</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Dedicated internet connection (10 Mbps - 10 Gbps)</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Enterprise-grade router and firewall</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Redundant connection options</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Static IP addresses (up to 32)</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Professional installation and setup</span>
                </div>
              </div>

              <h3 className="text-2xl font-bold text-gray-900 mb-6 mt-10">Security Services</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Managed firewall configuration</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Intrusion detection and prevention</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">DDoS protection and mitigation</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Content filtering and web security</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Regular security updates and patches</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Monitoring & Support</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">24/7 network monitoring and alerting</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Proactive issue detection and resolution</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Performance monitoring and reporting</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Dedicated technical support team</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Monthly performance reports</span>
                </div>
              </div>

              <h3 className="text-2xl font-bold text-gray-900 mb-6 mt-10">Management Services</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Bandwidth management and optimization</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Quality of Service (QoS) configuration</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Network configuration management</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Regular maintenance and updates</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700">Capacity planning and scaling</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Levels */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Service Level Guarantees
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We stand behind our managed service with industry-leading SLAs and performance guarantees.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center border-2 border-green-200 bg-green-50">
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-green-600 mb-2">99.9%</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Network Uptime</h3>
                <p className="text-sm text-gray-600">Guaranteed network availability with automatic failover</p>
              </CardContent>
            </Card>

            <Card className="text-center border-2 border-blue-200 bg-blue-50">
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-blue-600 mb-2">&lt;15min</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Response Time</h3>
                <p className="text-sm text-gray-600">Critical issue response within 15 minutes</p>
              </CardContent>
            </Card>

            <Card className="text-center border-2 border-purple-200 bg-purple-50">
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-purple-600 mb-2">&lt;4hrs</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Resolution Time</h3>
                <p className="text-sm text-gray-600">Average issue resolution time under 4 hours</p>
              </CardContent>
            </Card>

            <Card className="text-center border-2 border-orange-200 bg-orange-50">
              <CardContent className="p-6">
                <div className="text-4xl font-bold text-orange-600 mb-2">24/7</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Support Coverage</h3>
                <p className="text-sm text-gray-600">Round-the-clock technical support and monitoring</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Why Choose Managed Enterprise Internet?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Focus on your business while we handle all aspects of your internet connectivity 
              and network infrastructure management.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Reduced IT Overhead</h3>
              <p className="text-gray-600">
                Eliminate the need for dedicated network staff and reduce IT operational costs 
                with our fully managed service.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Enhanced Security</h3>
              <p className="text-gray-600">
                Benefit from enterprise-grade security managed by certified professionals 
                with the latest threat intelligence.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Improved Performance</h3>
              <p className="text-gray-600">
                Continuous optimization and monitoring ensure peak performance and 
                maximum productivity for your business.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Expert Support</h3>
              <p className="text-gray-600">
                Access to certified network engineers and security specialists 
                available 24/7 for immediate assistance.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <AlertTriangle className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Proactive Monitoring</h3>
              <p className="text-gray-600">
                Issues are detected and resolved before they impact your business, 
                ensuring maximum uptime and reliability.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Settings className="w-8 h-8 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Scalable Solution</h3>
              <p className="text-gray-600">
                Easily scale your internet capacity and services as your business 
                grows without infrastructure concerns.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Client Testimonial */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
              ))}
            </div>
            <blockquote className="text-2xl text-gray-700 italic mb-8">
              "Since switching to OfficeTech's managed enterprise internet service, we've had 
              zero network downtime and our productivity has increased significantly. Their 
              proactive monitoring and support team are exceptional."
            </blockquote>
            <div className="flex items-center justify-center space-x-4">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
              <div className="text-left">
                <p className="font-semibold text-gray-900">Fatou Camara</p>
                <p className="text-gray-600">CTO, Guinea Financial Services</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-indigo-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Experience Worry-Free Internet Connectivity
          </h2>
          <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
            Let our experts handle your internet infrastructure while you focus on 
            growing your business. Get started with our managed service today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-purple-600 hover:bg-gray-100">
              <Phone className="w-5 h-5 mr-2" />
              Start Managed Service
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600">
              <Mail className="w-5 h-5 mr-2" />
              Service Consultation
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export const ManagedEnterpriseInternet = () => {
  const { language } = useLanguage();

  return (
    <ContentProvider key={language} defaultLanguage={language}>
      <Layout>
        <ManagedEnterpriseInternetContent />
      </Layout>
    </ContentProvider>
  );
};
