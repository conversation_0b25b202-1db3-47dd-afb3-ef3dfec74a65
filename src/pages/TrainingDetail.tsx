import { useParams, Navigate } from 'react-router-dom';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import Layout from '@/components/Layout';
import { ContentProvider } from '@/components/content/ContentProvider';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, CheckCircle, ArrowLeft, Clock, Users, GraduationCap, Target, Monitor, Award } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Skeleton } from '@/components/ui/skeleton';
import { useLanguage, useTranslation } from '@/contexts/I18nContext';
import { BreadcrumbNav, generateBreadcrumbs } from '@/components/BreadcrumbNav';

const TrainingDetailContent = () => {
  const { slug } = useParams<{ slug: string }>();
  const { language } = useLanguage();
  const { t } = useTranslation();

  // Get all training programs to find the one with matching slug
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for training programs and find the one with matching slug
  const trainingProgram = allContent?.find(item =>
    item.contentType?.name === "training_program" && item.data.slug === slug
  );

  // Get the other language version for fallback
  const otherLanguage = language === 'en' ? 'es' : 'en';
  const fallbackContent = useQuery(api.content.getContent, {
    identifier: trainingProgram?.identifier || "",
    language: otherLanguage,
    includeDraft: false
  });

  // Loading state
  if (allContent === undefined) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <Skeleton className="h-8 w-64 mb-4" />
          <Skeleton className="h-64 w-full mb-8" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    );
  }

  // Fallback training programs data for static content - matching TrainingDropdown
  const fallbackPrograms = [
    {
      id: 'corporate-pc-skills',
      title: language === 'en' ? 'Corporate PC Skills Development' : 'Desarrollo de Habilidades de PC Corporativas',
      duration: language === 'en' ? '2-4 weeks' : '2-4 semanas',
      level: language === 'en' ? 'All Levels' : 'Todos los Niveles',
      participants: '15-20',
      description: language === 'en' ? 'Essential computer skills for the modern workplace' : 'Habilidades informáticas esenciales para el lugar de trabajo moderno',
      fullDescription: language === 'en' ?
        'Comprehensive training programs designed to empower your team with the knowledge and expertise they need to excel in the workplace.' :
        'Programas de entrenamiento integrales diseñados para empoderar a tu equipo con el conocimiento y la experiencia que necesitan para sobresalir en el lugar de trabajo.',
      topics: language === 'en' ? [
        'Microsoft Office Suite (Word, Excel, PowerPoint, Outlook, Teams)',
        'Windows Operating Systems navigation and troubleshooting',
        'Cybersecurity Awareness and best practices',
        'Network Fundamentals and basic troubleshooting',
        'Custom Training tailored to business needs'
      ] : [
        'Suite de Microsoft Office (Word, Excel, PowerPoint, Outlook, Teams)',
        'Navegación de Sistemas Operativos Windows y solución de problemas',
        'Conciencia de Ciberseguridad y mejores prácticas',
        'Fundamentos de Redes y solución básica de problemas',
        'Entrenamiento Personalizado adaptado a necesidades empresariales'
      ],
      benefits: language === 'en' ? [
        'Improved Productivity',
        'Enhanced Skills',
        'Better Security Awareness',
        'Competitive Advantage'
      ] : [
        'Productividad Mejorada',
        'Habilidades Mejoradas',
        'Mejor Conciencia de Seguridad',
        'Ventaja Competitiva'
      ],
      deliveryOptions: language === 'en' ? [
        'On-site Training',
        'Virtual Training',
        'Hybrid Learning',
        'Self-paced Modules'
      ] : [
        'Entrenamiento en Sitio',
        'Entrenamiento Virtual',
        'Aprendizaje Híbrido',
        'Módulos de Ritmo Propio'
      ],
      targetAudience: language === 'en' ? [
        'Business professionals',
        'Office workers',
        'IT support staff',
        'Administrative personnel'
      ] : [
        'Profesionales de negocios',
        'Trabajadores de oficina',
        'Personal de soporte de TI',
        'Personal administrativo'
      ],
      icon: Monitor,
      price: language === 'en' ? 'Contact for pricing' : 'Contactar para precios',
      slug: 'corporate-pc-skills'
    },
    {
      id: 'leadership-training',
      title: language === 'en' ? 'Leadership Development' : 'Desarrollo de Liderazgo',
      duration: language === 'en' ? '3-6 weeks' : '3-6 semanas',
      level: language === 'en' ? 'Intermediate to Advanced' : 'Intermedio a Avanzado',
      participants: '10-15',
      description: language === 'en' ? 'Essential skills to lead effectively and inspire others' : 'Habilidades esenciales para liderar efectivamente e inspirar a otros',
      fullDescription: language === 'en' ?
        'Develop the essential skills needed to lead teams effectively, inspire others, and drive organizational success.' :
        'Desarrolla las habilidades esenciales necesarias para liderar equipos efectivamente, inspirar a otros y impulsar el éxito organizacional.',
      topics: language === 'en' ? [
        'Strategic thinking and vision development',
        'Team building and management',
        'Effective communication and presentation',
        'Decision making frameworks',
        'Change management and adaptation'
      ] : [
        'Pensamiento estratégico y desarrollo de visión',
        'Construcción y gestión de equipos',
        'Comunicación efectiva y presentación',
        'Marcos de toma de decisiones',
        'Gestión del cambio y adaptación'
      ],
      benefits: language === 'en' ? [
        'Enhanced Leadership Skills',
        'Better Team Performance',
        'Improved Decision Making',
        'Career Advancement'
      ] : [
        'Habilidades de Liderazgo Mejoradas',
        'Mejor Rendimiento del Equipo',
        'Toma de Decisiones Mejorada',
        'Avance Profesional'
      ],
      deliveryOptions: language === 'en' ? [
        'Executive Coaching',
        'Group Workshops',
        'Leadership Retreats',
        'Mentoring Programs'
      ] : [
        'Coaching Ejecutivo',
        'Talleres Grupales',
        'Retiros de Liderazgo',
        'Programas de Mentoría'
      ],
      targetAudience: language === 'en' ? [
        'Senior managers',
        'Team leaders',
        'Department heads',
        'Aspiring leaders'
      ] : [
        'Gerentes senior',
        'Líderes de equipo',
        'Jefes de departamento',
        'Líderes aspirantes'
      ],
      icon: Target,
      price: language === 'en' ? 'Contact for pricing' : 'Contactar para precios',
      slug: 'leadership-training'
    },
    {
      id: 'time-management',
      title: language === 'en' ? 'Time Management' : 'Gestión del Tiempo',
      duration: language === 'en' ? '1-2 weeks' : '1-2 semanas',
      level: language === 'en' ? 'All Levels' : 'Todos los Niveles',
      participants: '20-25',
      description: language === 'en' ? 'Take control of schedules and achieve more with less stress' : 'Toma control de horarios y logra más con menos estrés',
      fullDescription: language === 'en' ?
        'Learn proven strategies to maximize productivity, reduce stress, and achieve better work-life balance.' :
        'Aprende estrategias probadas para maximizar la productividad, reducir el estrés y lograr un mejor equilibrio trabajo-vida.',
      topics: language === 'en' ? [
        'Priority setting and goal alignment',
        'Daily and weekly planning systems',
        'Productivity tools and techniques',
        'Stress management strategies',
        'Work-life balance principles'
      ] : [
        'Establecimiento de prioridades y alineación de objetivos',
        'Sistemas de planificación diaria y semanal',
        'Herramientas y técnicas de productividad',
        'Estrategias de gestión del estrés',
        'Principios de equilibrio trabajo-vida'
      ],
      benefits: language === 'en' ? [
        'Increased Productivity',
        'Reduced Stress',
        'Better Work-Life Balance',
        'Improved Focus'
      ] : [
        'Productividad Aumentada',
        'Estrés Reducido',
        'Mejor Equilibrio Trabajo-Vida',
        'Enfoque Mejorado'
      ],
      deliveryOptions: language === 'en' ? [
        'Interactive Workshops',
        'Online Modules',
        'Personal Coaching',
        'Group Sessions'
      ] : [
        'Talleres Interactivos',
        'Módulos en Línea',
        'Coaching Personal',
        'Sesiones Grupales'
      ],
      targetAudience: language === 'en' ? [
        'Busy professionals',
        'Project managers',
        'Entrepreneurs',
        'Anyone seeking better productivity'
      ] : [
        'Profesionales ocupados',
        'Gerentes de proyecto',
        'Emprendedores',
        'Cualquiera que busque mejor productividad'
      ],
      icon: Clock,
      price: language === 'en' ? 'Contact for pricing' : 'Contactar para precios',
      slug: 'time-management'
    },
    {
      id: 'communication-skills',
      title: language === 'en' ? 'Communication Skills' : 'Habilidades de Comunicación',
      duration: language === 'en' ? '2-3 weeks' : '2-3 semanas',
      level: language === 'en' ? 'All Levels' : 'Todos los Niveles',
      participants: '15-20',
      description: language === 'en' ? 'Master the art of effective communication' : 'Domina el arte de la comunicación efectiva',
      fullDescription: language === 'en' ?
        'Develop exceptional communication skills that will enhance your professional relationships and boost your career prospects.' :
        'Desarrolla habilidades excepcionales de comunicación que mejorarán tus relaciones profesionales e impulsarán tus perspectivas de carrera.',
      topics: language === 'en' ? [
        'Verbal and presentation skills',
        'Professional writing techniques',
        'Active listening strategies',
        'Non-verbal communication awareness',
        'Conflict resolution methods'
      ] : [
        'Habilidades verbales y de presentación',
        'Técnicas de escritura profesional',
        'Estrategias de escucha activa',
        'Conciencia de comunicación no verbal',
        'Métodos de resolución de conflictos'
      ],
      benefits: language === 'en' ? [
        'Better Professional Relationships',
        'Enhanced Team Collaboration',
        'Increased Confidence',
        'Career Advancement'
      ] : [
        'Mejores Relaciones Profesionales',
        'Colaboración en Equipo Mejorada',
        'Confianza Aumentada',
        'Avance Profesional'
      ],
      deliveryOptions: language === 'en' ? [
        'Interactive Workshops',
        'Role-playing Exercises',
        'Video Analysis',
        'Peer Feedback Sessions'
      ] : [
        'Talleres Interactivos',
        'Ejercicios de Juego de Roles',
        'Análisis de Video',
        'Sesiones de Retroalimentación de Pares'
      ],
      targetAudience: language === 'en' ? [
        'All professionals',
        'Team members',
        'Customer service staff',
        'Anyone seeking better communication'
      ] : [
        'Todos los profesionales',
        'Miembros del equipo',
        'Personal de servicio al cliente',
        'Cualquiera que busque mejor comunicación'
      ],
      icon: Users,
      price: language === 'en' ? 'Contact for pricing' : 'Contactar para precios',
      slug: 'communication-skills'
    },
    {
      id: 'oil-gas-training',
      title: language === 'en' ? 'Oil & Gas Training' : 'Entrenamiento de Petróleo y Gas',
      duration: language === 'en' ? '4-8 weeks' : '4-8 semanas',
      level: language === 'en' ? 'Intermediate to Advanced' : 'Intermedio a Avanzado',
      participants: '10-15',
      description: language === 'en' ? 'Professional training for energy sector standards' : 'Entrenamiento profesional para estándares del sector energético',
      fullDescription: language === 'en' ?
        'Comprehensive training program designed specifically for professionals in the oil and gas industry.' :
        'Programa de entrenamiento integral diseñado específicamente para profesionales en la industria de petróleo y gas.',
      topics: language === 'en' ? [
        'HSE standards and safety protocols',
        'Technical equipment operation',
        'Regulatory compliance requirements',
        'Environmental management practices',
        'Quality assurance procedures'
      ] : [
        'Estándares HSE y protocolos de seguridad',
        'Operación de equipos técnicos',
        'Requisitos de cumplimiento regulatorio',
        'Prácticas de gestión ambiental',
        'Procedimientos de aseguramiento de calidad'
      ],
      benefits: language === 'en' ? [
        'Industry Certification',
        'Enhanced Safety Knowledge',
        'Career Advancement',
        'Regulatory Compliance'
      ] : [
        'Certificación de la Industria',
        'Conocimiento de Seguridad Mejorado',
        'Avance Profesional',
        'Cumplimiento Regulatorio'
      ],
      deliveryOptions: language === 'en' ? [
        'On-site Training',
        'Simulation Exercises',
        'Certification Programs',
        'Continuing Education'
      ] : [
        'Entrenamiento en Sitio',
        'Ejercicios de Simulación',
        'Programas de Certificación',
        'Educación Continua'
      ],
      targetAudience: language === 'en' ? [
        'Oil & gas professionals',
        'Safety officers',
        'Technical operators',
        'Industry newcomers'
      ] : [
        'Profesionales de petróleo y gas',
        'Oficiales de seguridad',
        'Operadores técnicos',
        'Nuevos en la industria'
      ],
      icon: Award,
      price: language === 'en' ? 'Contact for pricing' : 'Contactar para precios',
      slug: 'oil-gas-training'
    }
  ];

  // Use dynamic content if available, otherwise fallback to static data
  let program;
  if (trainingProgram) {
    // Use dynamic content from database
    program = {
      id: trainingProgram._id,
      title: trainingProgram.data.title,
      duration: trainingProgram.data.duration || (language === 'en' ? '2-4 weeks' : '2-4 semanas'),
      level: trainingProgram.data.level || (language === 'en' ? 'All Levels' : 'Todos los Niveles'),
      participants: trainingProgram.data.participants || '15-20',
      description: trainingProgram.data.description,
      fullDescription: trainingProgram.data.fullDescription || trainingProgram.data.description,
      topics: trainingProgram.data.topics || [],
      benefits: trainingProgram.data.benefits || [],
      deliveryOptions: trainingProgram.data.deliveryOptions || [],
      targetAudience: trainingProgram.data.targetAudience || [],
      icon: trainingProgram.data.icon || GraduationCap,
      price: trainingProgram.data.price || (language === 'en' ? 'Contact for pricing' : 'Contactar para precios'),
      slug: trainingProgram.data.slug
    };
  } else {
    // Fallback to static data
    program = fallbackPrograms.find(p => p.slug === slug);
  }

  if (!program) {
    return <Navigate to="/training" replace />;
  }

  const IconComponent = program.icon;

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      case 'Management': return 'bg-purple-100 text-purple-800';
      case 'Professional': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const breadcrumbs = generateBreadcrumbs('training', program.title, language, t);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-white">
      {/* Breadcrumb */}
      <BreadcrumbNav items={breadcrumbs} />

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mr-4">
                  <IconComponent className="w-8 h-8 text-purple-600" />
                </div>
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                    {program.title}
                  </h1>
                  <Badge className={`mt-2 ${getLevelColor(program.level)}`}>
                    {program.level}
                  </Badge>
                </div>
              </div>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                {program.description}
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/contact">
                  <Button
                    size="lg"
                    className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4"
                  >
                    {language === 'en' ? 'Enroll Now' : 'Inscribirse Ahora'}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link to="/training">
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-purple-600 text-purple-600 hover:bg-purple-50 px-8 py-4"
                  >
                    <ArrowLeft className="mr-2 h-5 w-5" />
                    {language === 'en' ? 'Back to Training' : 'Volver al Entrenamiento'}
                  </Button>
                </Link>
              </div>
            </div>

            {/* Program Info Cards */}
            <div className="grid grid-cols-2 gap-4">
              <Card className="bg-white/80 backdrop-blur-sm border-purple-200">
                <CardContent className="p-6 text-center">
                  <Clock className="w-8 h-8 text-purple-600 mx-auto mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-1">{language === 'en' ? 'Duration' : 'Duración'}</h3>
                  <p className="text-gray-600">{program.duration}</p>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-purple-200">
                <CardContent className="p-6 text-center">
                  <Users className="w-8 h-8 text-purple-600 mx-auto mb-3" />
                  <h3 className="font-semibold text-gray-900 mb-1">{language === 'en' ? 'Participants' : 'Participantes'}</h3>
                  <p className="text-gray-600">{program.participants}</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Program Content Grid */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Topics Covered */}
            <Card className="bg-white shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">{language === 'en' ? 'Topics Covered' : 'Temas Cubiertos'}</h3>
                <ul className="space-y-4">
                  {program.topics.map((topic, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="w-6 h-6 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{topic}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card className="bg-white shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">{language === 'en' ? 'Key Benefits' : 'Beneficios Clave'}</h3>
                <ul className="space-y-4">
                  {program.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="w-6 h-6 text-purple-600 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Delivery Options */}
            <Card className="bg-white shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">{language === 'en' ? 'Delivery Options' : 'Opciones de Entrega'}</h3>
                <ul className="space-y-4">
                  {program.deliveryOptions.map((option, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="w-6 h-6 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{option}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Target Audience */}
            <Card className="bg-white shadow-lg">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">{language === 'en' ? 'Target Audience' : 'Audiencia Objetivo'}</h3>
                <ul className="space-y-4">
                  {program.targetAudience.map((audience, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="w-6 h-6 text-orange-600 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{audience}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            {language === 'en' ? 'Ready to Get Started?' : '¿Listo para Comenzar?'}
          </h2>
          <p className="text-xl mb-8 opacity-90">
            {language === 'en' ?
              'Join thousands of professionals who have advanced their careers with our training programs.' :
              'Únete a miles de profesionales que han avanzado sus carreras con nuestros programas de entrenamiento.'
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/contact">
              <Button
                size="lg"
                className="bg-white text-purple-600 hover:bg-gray-100 px-8 py-4"
              >
                {language === 'en' ? 'Enroll Now' : 'Inscribirse Ahora'}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/training">
              <Button
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white hover:text-purple-600 px-8 py-4"
              >
                {language === 'en' ? 'View All Programs' : 'Ver Todos los Programas'}
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

const TrainingDetail = () => {
  const { language } = useLanguage();

  return (
    <ContentProvider key={language} defaultLanguage={language}>
      <Layout>
        <TrainingDetailContent />
      </Layout>
    </ContentProvider>
  );
};

export default TrainingDetail;