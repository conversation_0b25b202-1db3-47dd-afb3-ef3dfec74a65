import Layout from '@/components/Layout';
import { Wifi, Home, Users, Shield, Clock, CheckCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/contexts/I18nContext';

const DomesticServices = () => {
  const { t } = useTranslation();

  const plans = [
    {
      name: 'Basic Home',
      speed: '10 Mbps',
      price: '$25',
      features: [
        'Reliable internet connection',
        'Email support',
        'Basic security features',
        'Standard installation'
      ],
      icon: Home,
      popular: false
    },
    {
      name: 'Family Plus',
      speed: '25 Mbps',
      price: '$45',
      features: [
        'High-speed internet',
        'Multiple device support',
        'Enhanced security',
        'Priority support',
        'Free router included'
      ],
      icon: Users,
      popular: true
    },
    {
      name: 'Premium Home',
      speed: '50 Mbps',
      price: '$75',
      features: [
        'Ultra-fast internet',
        'Unlimited devices',
        'Advanced security suite',
        '24/7 premium support',
        'Professional installation',
        'Wi-Fi optimization'
      ],
      icon: Shield,
      popular: false
    }
  ];

  const features = [
    {
      icon: Wifi,
      title: 'High-Speed Internet',
      description: 'Reliable broadband connections for homes and small businesses across Guinea.'
    },
    {
      icon: Shield,
      title: 'Secure Connections',
      description: 'Built-in security features to protect your family and data online.'
    },
    {
      icon: Clock,
      title: '24/7 Support',
      description: 'Round-the-clock technical support for all domestic service customers.'
    },
    {
      icon: CheckCircle,
      title: 'Easy Setup',
      description: 'Professional installation and setup included with all plans.'
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white">
        {/* Hero Section */}
        <section className="relative py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              <span className="text-gradient">Domestic Services</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Reliable internet and communication services for homes and small businesses 
              throughout Guinea. Stay connected with our affordable domestic plans.
            </p>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Why Choose Our Domestic Services?
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                We provide comprehensive internet and communication solutions designed 
                specifically for residential and small business needs.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <Icon className="w-6 h-6 text-blue-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 text-sm">
                        {feature.description}
                      </p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Plans Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Choose Your Plan
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Flexible internet plans designed to meet your specific needs and budget.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {plans.map((plan, index) => {
                const Icon = plan.icon;
                return (
                  <Card key={index} className={`relative ${plan.popular ? 'ring-2 ring-blue-500 shadow-lg' : ''}`}>
                    {plan.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                          Most Popular
                        </span>
                      </div>
                    )}
                    <CardContent className="p-6">
                      <div className="text-center mb-6">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                          <Icon className="w-6 h-6 text-blue-600" />
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-2">
                          {plan.name}
                        </h3>
                        <div className="text-3xl font-bold text-blue-600 mb-1">
                          {plan.price}
                        </div>
                        <p className="text-gray-600 text-sm">per month</p>
                        <p className="text-lg font-semibold text-gray-700 mt-2">
                          Up to {plan.speed}
                        </p>
                      </div>

                      <ul className="space-y-3 mb-6">
                        {plan.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                            <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>

                      <Button 
                        className={`w-full ${plan.popular ? 'bg-blue-600 hover:bg-blue-700' : ''}`}
                        variant={plan.popular ? 'default' : 'outline'}
                      >
                        Choose Plan
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Coverage Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 mb-6">
                  Nationwide Coverage
                </h2>
                <p className="text-gray-600 mb-6">
                  Our domestic services are available across Guinea, bringing reliable 
                  internet connectivity to homes and small businesses in urban and rural areas.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center text-gray-700">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    Conakry and surrounding areas
                  </li>
                  <li className="flex items-center text-gray-700">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    Major cities and towns
                  </li>
                  <li className="flex items-center text-gray-700">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    Rural connectivity programs
                  </li>
                  <li className="flex items-center text-gray-700">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                    Expanding coverage areas
                  </li>
                </ul>
              </div>
              <div className="bg-gradient-to-br from-blue-100 to-blue-50 rounded-2xl p-8">
                <div className="text-center">
                  <Wifi className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    Ready to Get Connected?
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Contact us today to check availability in your area and get started.
                  </p>
                  <Button size="lg">
                    Check Availability
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
};

export default DomesticServices;
