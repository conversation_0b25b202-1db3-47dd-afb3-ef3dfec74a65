
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Inline Editor Styles */
.inline-editor-view {
  position: relative;
  display: inline-block;
  min-height: 1.5rem;
  min-width: 2rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.inline-editor-view:hover {
  background-color: rgba(59, 130, 246, 0.05);
  outline: 1px dashed rgba(59, 130, 246, 0.3);
}

.inline-editor-content {
  display: block;
  width: 100%;
  min-height: inherit;
}

.inline-editor-edit-btn {
  position: absolute;
  top: -4px;
  right: -4px;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.inline-editor-container {
  position: relative;
  z-index: 20;
  background-color: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 2px solid #3b82f6;
}

.inline-editor-container input,
.inline-editor-container textarea {
  color: #111827 !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  background-color: white !important;
  border: 2px solid #e5e7eb !important;
  border-radius: 6px !important;
  padding: 12px 16px !important;
  line-height: 1.6 !important;
  width: 100% !important;
  min-width: 200px !important;
}

.inline-editor-container input:focus,
.inline-editor-container textarea:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

.inline-editor-container textarea {
  min-height: 80px !important;
  resize: vertical !important;
}

/* Edit mode indicator */
.editing {
  background-color: rgba(59, 130, 246, 0.1);
  outline: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  padding: 2px 4px;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 220 91% 42%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 0 84% 35%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 220 91% 42%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 220 91% 42%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 0 84% 35%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }
}

@layer components {
  .hero-gradient {
    background: linear-gradient(135deg, hsl(220 91% 42%) 0%, hsl(220 91% 35%) 100%);
  }
  
  .text-gradient {
    background: linear-gradient(135deg, hsl(220 91% 42%) 0%, hsl(220 91% 35%) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .accent-gradient {
    background: linear-gradient(135deg, hsl(0 84% 35%) 0%, hsl(0 84% 45%) 100%);
  }

  /* Animation delay utilities */
  .animation-delay-300 {
    animation-delay: 300ms;
  }
  
  .animation-delay-600 {
    animation-delay: 600ms;
  }
  
  .animation-delay-1000 {
    animation-delay: 1000ms;
  }
  
  .animation-delay-1500 {
    animation-delay: 1500ms;
  }
}

@layer utilities {
  /* New keyframe animations */
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(50px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes shimmer-slide {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes bounce-slow {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  @keyframes pulse-slow {
    0%, 100% {
      opacity: 0.3;
    }
    50% {
      opacity: 0.6;
    }
  }

  @keyframes pulse-gentle {
    0%, 100% {
      opacity: 0.8;
    }
    50% {
      opacity: 1;
    }
  }

  @keyframes scroll-left {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  @keyframes scale-in {
    0% {
      opacity: 0;
      transform: scale(0.8);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Animation classes */
  .animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out forwards;
  }

  .animate-slide-up {
    animation: slide-up 0.8s ease-out forwards;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, hsl(220 91% 42%), hsl(220 91% 35%), hsl(220 91% 42%));
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
  }

  .animate-shimmer-slide {
    animation: shimmer-slide 2s ease-in-out infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-bounce-slow {
    animation: bounce-slow 2s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse-slow 4s ease-in-out infinite;
  }

  .animate-pulse-gentle {
    animation: pulse-gentle 2s ease-in-out infinite;
  }

  .animate-scroll-left {
    animation: scroll-left 30s linear infinite;
  }

  .animate-scale-in {
    animation: scale-in 0.6s ease-out forwards;
  }
}

/* Admin Layout Fixes */
@layer components {
  /* Ensure admin content is not hidden behind fixed elements */
  .admin-content-wrapper {
    @apply min-h-screen;
  }

  /* Mobile admin adjustments */
  @media (max-width: 768px) {
    .admin-mobile-spacing {
      @apply pt-16;
    }
  }
}
