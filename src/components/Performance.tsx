import { useEffect } from "react";
import { usePerformanceSettings } from "@/contexts/SettingsContext";

interface PerformanceProps {
  preloadImages?: string[];
  criticalCSS?: string;
  deferNonCritical?: boolean;
}

export const Performance = ({
  preloadImages = [],
  criticalCSS,
  deferNonCritical = true,
}: PerformanceProps) => {
  const performanceSettings = usePerformanceSettings();

  useEffect(() => {
    // Preload critical resources if enabled
    if (performanceSettings.preloadCriticalResources) {
      // Preload critical images
      preloadImages.forEach((src) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
      });

      // Preload critical fonts
      const fontPreloads = [
        '/fonts/inter-var.woff2',
        '/fonts/inter-regular.woff2',
        '/fonts/inter-medium.woff2',
        '/fonts/inter-semibold.woff2',
      ];

      fontPreloads.forEach((href) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'font';
        link.type = 'font/woff2';
        link.crossOrigin = 'anonymous';
        link.href = href;
        document.head.appendChild(link);
      });
    }

    // Add critical CSS inline if provided
    if (criticalCSS) {
      const style = document.createElement('style');
      style.textContent = criticalCSS;
      document.head.appendChild(style);
    }

    // Defer non-critical resources
    if (deferNonCritical) {
      // Defer non-critical CSS
      const deferCSS = () => {
        const links = document.querySelectorAll('link[rel="stylesheet"][data-defer]');
        links.forEach((link) => {
          const href = link.getAttribute('href');
          if (href) {
            const newLink = document.createElement('link');
            newLink.rel = 'stylesheet';
            newLink.href = href;
            newLink.media = 'print';
            newLink.onload = () => {
              newLink.media = 'all';
            };
            document.head.appendChild(newLink);
            link.remove();
          }
        });
      };

      // Defer execution until after page load
      if (document.readyState === 'loading') {
        window.addEventListener('load', deferCSS);
      } else {
        deferCSS();
      }
    }

    // Add performance monitoring
    if (typeof window !== 'undefined' && 'performance' in window) {
      // Monitor Core Web Vitals
      const observeWebVitals = () => {
        // Largest Contentful Paint (LCP)
        if ('PerformanceObserver' in window) {
          try {
            const lcpObserver = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              const lastEntry = entries[entries.length - 1];
              console.log('LCP:', lastEntry.startTime);
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

            // First Input Delay (FID)
            const fidObserver = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              entries.forEach((entry) => {
                console.log('FID:', entry.processingStart - entry.startTime);
              });
            });
            fidObserver.observe({ entryTypes: ['first-input'] });

            // Cumulative Layout Shift (CLS)
            const clsObserver = new PerformanceObserver((list) => {
              let clsValue = 0;
              const entries = list.getEntries();
              entries.forEach((entry) => {
                if (!entry.hadRecentInput) {
                  clsValue += entry.value;
                }
              });
              console.log('CLS:', clsValue);
            });
            clsObserver.observe({ entryTypes: ['layout-shift'] });
          } catch (error) {
            console.warn('Performance monitoring not supported:', error);
          }
        }
      };

      // Start monitoring after page load
      if (document.readyState === 'complete') {
        observeWebVitals();
      } else {
        window.addEventListener('load', observeWebVitals);
      }
    }

    // Optimize images with lazy loading
    if (performanceSettings.lazyLoading) {
      const images = document.querySelectorAll('img[data-src]');
      
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              const src = img.getAttribute('data-src');
              if (src) {
                img.src = src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
              }
            }
          });
        });

        images.forEach((img) => imageObserver.observe(img));
      } else {
        // Fallback for browsers without IntersectionObserver
        images.forEach((img) => {
          const src = img.getAttribute('data-src');
          if (src) {
            (img as HTMLImageElement).src = src;
            img.removeAttribute('data-src');
          }
        });
      }
    }

    // Add resource hints
    const addResourceHints = () => {
      // DNS prefetch for external domains
      const externalDomains = [
        'fonts.googleapis.com',
        'fonts.gstatic.com',
        'www.google-analytics.com',
        'www.googletagmanager.com',
      ];

      externalDomains.forEach((domain) => {
        const link = document.createElement('link');
        link.rel = 'dns-prefetch';
        link.href = `//${domain}`;
        document.head.appendChild(link);
      });

      // Preconnect to critical third-party origins
      const preconnectDomains = [
        'fonts.googleapis.com',
        'fonts.gstatic.com',
      ];

      preconnectDomains.forEach((domain) => {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = `https://${domain}`;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      });
    };

    addResourceHints();

    // Service Worker registration for caching
    if (performanceSettings.enableCaching && 'serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('SW registered: ', registration);
          })
          .catch((registrationError) => {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }

    // Cleanup function
    return () => {
      // Remove event listeners if needed
    };
  }, [performanceSettings, preloadImages, criticalCSS, deferNonCritical]);

  return null; // This component doesn't render anything visible
};

// Hook for measuring performance metrics
export const usePerformanceMetrics = () => {
  useEffect(() => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const measurePageLoad = () => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        const metrics = {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          firstPaint: 0,
          firstContentfulPaint: 0,
        };

        // Get paint timings
        const paintEntries = performance.getEntriesByType('paint');
        paintEntries.forEach((entry) => {
          if (entry.name === 'first-paint') {
            metrics.firstPaint = entry.startTime;
          } else if (entry.name === 'first-contentful-paint') {
            metrics.firstContentfulPaint = entry.startTime;
          }
        });

        console.log('Performance Metrics:', metrics);
        return metrics;
      };

      if (document.readyState === 'complete') {
        measurePageLoad();
      } else {
        window.addEventListener('load', measurePageLoad);
      }
    }
  }, []);
};

// Component for critical CSS injection
export const CriticalCSS = ({ css }: { css: string }) => {
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = css;
    style.setAttribute('data-critical', 'true');
    document.head.appendChild(style);

    return () => {
      const criticalStyles = document.querySelectorAll('style[data-critical="true"]');
      criticalStyles.forEach((style) => style.remove());
    };
  }, [css]);

  return null;
};
