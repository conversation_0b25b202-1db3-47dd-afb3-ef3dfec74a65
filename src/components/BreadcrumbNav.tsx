import { Link } from 'react-router-dom';
import { useTranslation, useLanguage } from '@/contexts/I18nContext';
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

interface BreadcrumbItem {
  label: string;
  href?: string;
  isCurrentPage?: boolean;
}

interface BreadcrumbNavProps {
  items: BreadcrumbItem[];
  className?: string;
}

export const BreadcrumbNav = ({ items, className = "" }: BreadcrumbNavProps) => {
  const { t } = useTranslation();
  const { language } = useLanguage();

  return (
    <section className={`py-6 px-4 sm:px-6 lg:px-8 border-b bg-white ${className}`}>
      <div className="max-w-7xl mx-auto">
        <Breadcrumb>
          <BreadcrumbList>
            {items.map((item, index) => (
              <BreadcrumbItem key={index}>
                {item.isCurrentPage ? (
                  <BreadcrumbPage>{item.label}</BreadcrumbPage>
                ) : (
                  <>
                    <BreadcrumbLink asChild>
                      <Link to={item.href || '#'} className="hover:text-blue-600">
                        {item.label}
                      </Link>
                    </BreadcrumbLink>
                    {index < items.length - 1 && <BreadcrumbSeparator />}
                  </>
                )}
              </BreadcrumbItem>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
    </section>
  );
};

// Helper function to generate common breadcrumb patterns
export const generateBreadcrumbs = (
  type: 'service' | 'network-solution' | 'training',
  itemTitle: string,
  language: 'en' | 'es',
  t: (key: string) => string
): BreadcrumbItem[] => {
  const baseBreadcrumbs: BreadcrumbItem[] = [
    { label: t('nav.home'), href: '/' }
  ];

  switch (type) {
    case 'service':
      return [
        ...baseBreadcrumbs,
        { label: t('nav.services'), href: '/services' },
        { label: itemTitle, isCurrentPage: true }
      ];
    
    case 'network-solution':
      return [
        ...baseBreadcrumbs,
        { label: t('nav.networkSolutions'), href: '/network-solutions' },
        { label: itemTitle, isCurrentPage: true }
      ];
    
    case 'training':
      return [
        ...baseBreadcrumbs,
        { label: t('nav.training'), href: '/training' },
        { label: itemTitle, isCurrentPage: true }
      ];
    
    default:
      return baseBreadcrumbs;
  }
};
