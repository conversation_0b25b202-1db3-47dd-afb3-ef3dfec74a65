import { ReactNode } from "react";
import Layout from "./Layout";
import { SEO } from "./SEO";
import { Performance } from "./Performance";
import { Analytics, useScrollTracking } from "./Analytics";

interface SEOLayoutProps {
  children: ReactNode;
  seo?: {
    title?: string;
    description?: string;
    keywords?: string;
    image?: string;
    url?: string;
    type?: string;
    noIndex?: boolean;
    canonical?: string;
  };
  performance?: {
    preloadImages?: string[];
    criticalCSS?: string;
    deferNonCritical?: boolean;
  };
  analytics?: {
    pageTitle?: string;
    pagePath?: string;
    userId?: string;
  };
}

export const SEOLayout = ({ 
  children, 
  seo = {}, 
  performance = {}, 
  analytics = {} 
}: SEOLayoutProps) => {
  // Enable scroll tracking
  useScrollTracking();

  return (
    <>
      {/* SEO Meta Tags */}
      <SEO {...seo} />
      
      {/* Performance Optimizations */}
      <Performance {...performance} />
      
      {/* Analytics Tracking */}
      <Analytics {...analytics} />
      
      {/* Main Layout */}
      <Layout>
        {children}
      </Layout>
    </>
  );
};

// Convenience wrapper for pages that need specific SEO
export const withSEO = (
  Component: React.ComponentType<any>,
  seoConfig?: SEOLayoutProps['seo'],
  performanceConfig?: SEOLayoutProps['performance'],
  analyticsConfig?: SEOLayoutProps['analytics']
) => {
  return (props: any) => (
    <SEOLayout 
      seo={seoConfig} 
      performance={performanceConfig}
      analytics={analyticsConfig}
    >
      <Component {...props} />
    </SEOLayout>
  );
};

// Pre-configured layouts for common page types
export const HomePageLayout = ({ children }: { children: ReactNode }) => (
  <SEOLayout
    seo={{
      title: "Home",
      description: "Leading technology and security solutions in Guinea. Network infrastructure, cybersecurity, surveillance, training, and IT support services.",
      keywords: "technology, IT services, cybersecurity, network solutions, Guinea, surveillance, training, data protection",
      type: "website"
    }}
    performance={{
      preloadImages: [
        "/images/hero-bg.jpg",
        "/images/services-preview.jpg",
        "/images/team-photo.jpg"
      ],
      deferNonCritical: true
    }}
    analytics={{
      pageTitle: "Home - OfficeTech Guinea",
      pagePath: "/"
    }}
  >
    {children}
  </SEOLayout>
);

export const ServicesPageLayout = ({ children }: { children: ReactNode }) => (
  <SEOLayout
    seo={{
      title: "Services",
      description: "Comprehensive technology services including network infrastructure, cybersecurity, surveillance systems, and IT training in Guinea.",
      keywords: "IT services, network infrastructure, cybersecurity, surveillance, training, Guinea, enterprise solutions",
      type: "website"
    }}
    performance={{
      preloadImages: [
        "/images/services-hero.jpg",
        "/images/network-solutions.jpg",
        "/images/security-systems.jpg"
      ]
    }}
    analytics={{
      pageTitle: "Services - OfficeTech Guinea",
      pagePath: "/services"
    }}
  >
    {children}
  </SEOLayout>
);

export const AboutPageLayout = ({ children }: { children: ReactNode }) => (
  <SEOLayout
    seo={{
      title: "About Us",
      description: "Learn about OfficeTech Guinea's mission to provide cutting-edge technology and security solutions. Meet our expert team and discover our story.",
      keywords: "about, company, team, mission, technology experts, Guinea, IT professionals",
      type: "website"
    }}
    performance={{
      preloadImages: [
        "/images/about-hero.jpg",
        "/images/team-group.jpg",
        "/images/office-location.jpg"
      ]
    }}
    analytics={{
      pageTitle: "About Us - OfficeTech Guinea",
      pagePath: "/about"
    }}
  >
    {children}
  </SEOLayout>
);

export const ContactPageLayout = ({ children }: { children: ReactNode }) => (
  <SEOLayout
    seo={{
      title: "Contact Us",
      description: "Get in touch with OfficeTech Guinea for your technology and security needs. Professional consultation and support available.",
      keywords: "contact, consultation, support, Guinea, technology services, get quote",
      type: "website"
    }}
    performance={{
      preloadImages: [
        "/images/contact-hero.jpg",
        "/images/office-exterior.jpg"
      ]
    }}
    analytics={{
      pageTitle: "Contact Us - OfficeTech Guinea",
      pagePath: "/contact"
    }}
  >
    {children}
  </SEOLayout>
);

export const BlogPageLayout = ({ 
  children, 
  title, 
  description, 
  image, 
  publishDate,
  author 
}: { 
  children: ReactNode;
  title?: string;
  description?: string;
  image?: string;
  publishDate?: string;
  author?: string;
}) => (
  <SEOLayout
    seo={{
      title: title || "Blog",
      description: description || "Latest insights and updates from OfficeTech Guinea on technology trends, security best practices, and industry news.",
      keywords: "blog, technology news, cybersecurity, IT insights, Guinea, tech trends",
      image: image,
      type: "article"
    }}
    performance={{
      preloadImages: image ? [image] : ["/images/blog-default.jpg"]
    }}
    analytics={{
      pageTitle: title ? `${title} - Blog - OfficeTech Guinea` : "Blog - OfficeTech Guinea",
      pagePath: "/blog"
    }}
  >
    {children}
  </SEOLayout>
);

export const ProductPageLayout = ({ 
  children, 
  productName, 
  description, 
  image, 
  price 
}: { 
  children: ReactNode;
  productName?: string;
  description?: string;
  image?: string;
  price?: string;
}) => (
  <SEOLayout
    seo={{
      title: productName || "Products",
      description: description || "Professional technology products and solutions from OfficeTech Guinea.",
      keywords: "products, technology solutions, enterprise, Guinea, IT equipment",
      image: image,
      type: "product"
    }}
    performance={{
      preloadImages: image ? [image] : ["/images/product-default.jpg"]
    }}
    analytics={{
      pageTitle: productName ? `${productName} - Products - OfficeTech Guinea` : "Products - OfficeTech Guinea",
      pagePath: "/products"
    }}
  >
    {children}
  </SEOLayout>
);
