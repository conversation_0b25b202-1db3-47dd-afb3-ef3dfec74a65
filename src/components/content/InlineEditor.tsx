import { useState, useRef, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Save, X, Edit3, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { useContentContext } from "./ContentProvider";

interface InlineEditorProps {
  identifier: string;
  fieldName: string;
  value: string;
  contentId?: string;
  type?: "text" | "textarea" | "html";
  className?: string;
  placeholder?: string;
  onSave?: (newValue: string) => void;
  children?: (value: string, isEditing: boolean) => React.ReactNode;
}

export const InlineEditor = ({
  identifier,
  fieldName,
  value,
  contentId,
  type = "text",
  className = "",
  placeholder,
  onSave,
  children
}: InlineEditorProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isSaving, setIsSaving] = useState(false);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  
  const { toast } = useToast();
  const { canEditContent } = useAuth();
  const { isEditMode, language } = useContentContext();
  const updateContent = useMutation(api.content.updateContent);

  // Update edit value when prop value changes
  useEffect(() => {
    setEditValue(value);
  }, [value]);

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      if (type === "text") {
        inputRef.current.select();
      }
    }
  }, [isEditing, type]);

  // Don't show edit functionality if user can't edit content or edit mode is off
  if (!canEditContent || !isEditMode) {
    return children ? children(value, false) : <span className={className}>{value}</span>;
  }

  // Debug logging
  console.log("InlineEditor render:", {
    identifier,
    fieldName,
    value,
    contentId,
    canEditContent,
    isEditMode,
    isEditing
  });

  const handleSave = async () => {
    if (!contentId || editValue === value) {
      setIsEditing(false);
      return;
    }

    setIsSaving(true);
    try {
      console.log("Saving content:", { contentId, fieldName, editValue });

      // Update the content in the database with just the field change
      // The Convex function will merge this with existing data
      await updateContent({
        id: contentId as any,
        data: {
          [fieldName]: editValue
        }
      });

      console.log("Content saved successfully");

      toast({
        title: "Content updated",
        description: `${fieldName} has been updated successfully.`,
      });

      onSave?.(editValue);
      setIsEditing(false);
    } catch (error) {
      console.error("Failed to save content:", error);
      toast({
        title: "Save failed",
        description: `Failed to update content: ${error.message || 'Unknown error'}`,
        variant: "destructive",
      });
      // Reset to original value on error
      setEditValue(value);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditValue(value);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey && type === "text") {
      e.preventDefault();
      handleSave();
    } else if (e.key === "Escape") {
      handleCancel();
    }
  };

  if (isEditing) {
    return (
      <div className={`inline-editor-container ${className}`}>
        <div className="relative">
          {type === "textarea" ? (
            <Textarea
              ref={inputRef as React.RefObject<HTMLTextAreaElement>}
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className="min-h-[100px] resize-y"
              disabled={isSaving}
            />
          ) : (
            <Input
              ref={inputRef as React.RefObject<HTMLInputElement>}
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className="w-full"
              disabled={isSaving}
            />
          )}
        </div>
        
        <div className="flex items-center space-x-2 mt-2">
          <Button
            size="sm"
            onClick={handleSave}
            disabled={isSaving || editValue === value}
            className="h-8 px-3"
          >
            {isSaving ? (
              <Loader2 className="w-3 h-3 animate-spin" />
            ) : (
              <Save className="w-3 h-3" />
            )}
            <span className="ml-1 text-xs">Save</span>
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={handleCancel}
            disabled={isSaving}
            className="h-8 px-3"
          >
            <X className="w-3 h-3" />
            <span className="ml-1 text-xs">Cancel</span>
          </Button>
        </div>
      </div>
    );
  }

  // Render view mode with edit button
  return (
    <div className={`inline-editor-view group relative ${className}`}>
      {children ? children(value, false) : (
        <span className="inline-editor-content">{value}</span>
      )}
      
      <Button
        size="sm"
        variant="ghost"
        onClick={() => setIsEditing(true)}
        className="inline-editor-edit-btn opacity-0 group-hover:opacity-100 transition-opacity absolute -top-1 -right-1 h-6 w-6 p-0 bg-blue-600 hover:bg-blue-700 text-white rounded-full"
        title={`Edit ${fieldName}`}
      >
        <Edit3 className="w-3 h-3" />
      </Button>
    </div>
  );
};

// Wrapper component for text content
export const EditableText = ({
  identifier,
  fieldName,
  value,
  contentId,
  className = "",
  placeholder,
  onSave,
  as: Component = "span"
}: InlineEditorProps & { as?: keyof JSX.IntrinsicElements }) => {
  return (
    <InlineEditor
      identifier={identifier}
      fieldName={fieldName}
      value={value}
      contentId={contentId}
      type="text"
      className={className}
      placeholder={placeholder}
      onSave={onSave}
    >
      {(value, isEditing) => (
        <Component className={`${isEditing ? "editing" : ""} ${className}`}>
          {value}
        </Component>
      )}
    </InlineEditor>
  );
};

// Wrapper component for textarea content
export const EditableTextarea = ({
  identifier,
  fieldName,
  value,
  contentId,
  className = "",
  placeholder,
  onSave,
  as: Component = "p"
}: InlineEditorProps & { as?: keyof JSX.IntrinsicElements }) => {
  return (
    <InlineEditor
      identifier={identifier}
      fieldName={fieldName}
      value={value}
      contentId={contentId}
      type="textarea"
      className={className}
      placeholder={placeholder}
      onSave={onSave}
    >
      {(value, isEditing) => (
        <Component className={`${isEditing ? "editing" : ""} ${className}`}>
          {value}
        </Component>
      )}
    </InlineEditor>
  );
};
