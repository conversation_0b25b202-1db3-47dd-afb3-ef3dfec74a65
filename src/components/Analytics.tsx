import { useEffect } from "react";
import { useSEOSettings, useFeatureFlags } from "@/contexts/SettingsContext";

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
    fbq: (...args: any[]) => void;
    _fbq: any;
  }
}

interface AnalyticsProps {
  pageTitle?: string;
  pagePath?: string;
  userId?: string;
}

export const Analytics = ({ pageTitle, pagePath, userId }: AnalyticsProps) => {
  const seoSettings = useSEOSettings();
  const featureFlags = useFeatureFlags();

  useEffect(() => {
    if (!featureFlags.analyticsEnabled) {
      return;
    }

    // Initialize Google Analytics
    if (seoSettings.googleAnalyticsId) {
      initializeGoogleAnalytics(seoSettings.googleAnalyticsId, userId);
    }

    // Initialize Google Tag Manager
    if (seoSettings.googleTagManagerId) {
      initializeGoogleTagManager(seoSettings.googleTagManagerId);
    }

    // Initialize Facebook Pixel
    if (seoSettings.facebookPixelId) {
      initializeFacebookPixel(seoSettings.facebookPixelId);
    }
  }, [seoSettings, featureFlags.analyticsEnabled, userId]);

  useEffect(() => {
    // Track page view when route changes
    if (featureFlags.analyticsEnabled) {
      trackPageView(pageTitle, pagePath);
    }
  }, [pageTitle, pagePath, featureFlags.analyticsEnabled]);

  return null; // This component doesn't render anything visible
};

// Initialize Google Analytics
const initializeGoogleAnalytics = (gaId: string, userId?: string) => {
  if (typeof window === 'undefined') return;

  // Load gtag script
  const script = document.createElement('script');
  script.async = true;
  script.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
  document.head.appendChild(script);

  // Initialize dataLayer
  window.dataLayer = window.dataLayer || [];
  window.gtag = function() {
    window.dataLayer.push(arguments);
  };

  window.gtag('js', new Date());
  window.gtag('config', gaId, {
    page_title: document.title,
    page_location: window.location.href,
    user_id: userId,
    anonymize_ip: true,
    allow_google_signals: false,
    allow_ad_personalization_signals: false,
  });

  console.log('Google Analytics initialized:', gaId);
};

// Initialize Google Tag Manager
const initializeGoogleTagManager = (gtmId: string) => {
  if (typeof window === 'undefined') return;

  // GTM script
  const script = document.createElement('script');
  script.innerHTML = `
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','${gtmId}');
  `;
  document.head.appendChild(script);

  // GTM noscript fallback
  const noscript = document.createElement('noscript');
  noscript.innerHTML = `
    <iframe src="https://www.googletagmanager.com/ns.html?id=${gtmId}"
    height="0" width="0" style="display:none;visibility:hidden"></iframe>
  `;
  document.body.appendChild(noscript);

  console.log('Google Tag Manager initialized:', gtmId);
};

// Initialize Facebook Pixel
const initializeFacebookPixel = (pixelId: string) => {
  if (typeof window === 'undefined') return;

  // Facebook Pixel script
  const script = document.createElement('script');
  script.innerHTML = `
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '${pixelId}');
    fbq('track', 'PageView');
  `;
  document.head.appendChild(script);

  // Noscript fallback
  const noscript = document.createElement('noscript');
  noscript.innerHTML = `
    <img height="1" width="1" style="display:none"
    src="https://www.facebook.com/tr?id=${pixelId}&ev=PageView&noscript=1" />
  `;
  document.body.appendChild(noscript);

  console.log('Facebook Pixel initialized:', pixelId);
};

// Track page view
export const trackPageView = (title?: string, path?: string) => {
  if (typeof window === 'undefined') return;

  const pageTitle = title || document.title;
  const pagePath = path || window.location.pathname;

  // Google Analytics
  if (window.gtag) {
    window.gtag('config', 'GA_MEASUREMENT_ID', {
      page_title: pageTitle,
      page_location: window.location.href,
      page_path: pagePath,
    });
  }

  // Facebook Pixel
  if (window.fbq) {
    window.fbq('track', 'PageView');
  }

  console.log('Page view tracked:', { title: pageTitle, path: pagePath });
};

// Track custom events
export const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
  if (typeof window === 'undefined') return;

  // Google Analytics
  if (window.gtag) {
    window.gtag('event', eventName, parameters);
  }

  // Facebook Pixel
  if (window.fbq) {
    window.fbq('track', eventName, parameters);
  }

  console.log('Event tracked:', eventName, parameters);
};

// Track conversions
export const trackConversion = (conversionType: string, value?: number, currency?: string) => {
  if (typeof window === 'undefined') return;

  const parameters: Record<string, any> = {
    event_category: 'conversion',
    event_label: conversionType,
  };

  if (value !== undefined) {
    parameters.value = value;
  }

  if (currency) {
    parameters.currency = currency;
  }

  trackEvent('conversion', parameters);
};

// Track user interactions
export const trackUserInteraction = (action: string, element: string, value?: string) => {
  trackEvent('user_interaction', {
    event_category: 'engagement',
    event_label: action,
    element_type: element,
    element_value: value,
  });
};

// Track form submissions
export const trackFormSubmission = (formName: string, success: boolean = true) => {
  trackEvent('form_submit', {
    event_category: 'form',
    event_label: formName,
    success: success,
  });
};

// Track downloads
export const trackDownload = (fileName: string, fileType: string) => {
  trackEvent('file_download', {
    event_category: 'download',
    event_label: fileName,
    file_type: fileType,
  });
};

// Track external link clicks
export const trackExternalLink = (url: string, linkText?: string) => {
  trackEvent('click', {
    event_category: 'external_link',
    event_label: url,
    link_text: linkText,
  });
};

// Track search queries
export const trackSearch = (query: string, results?: number) => {
  trackEvent('search', {
    search_term: query,
    event_category: 'search',
    results_count: results,
  });
};

// Track video interactions
export const trackVideo = (action: 'play' | 'pause' | 'complete', videoTitle: string, progress?: number) => {
  trackEvent('video_' + action, {
    event_category: 'video',
    event_label: videoTitle,
    progress: progress,
  });
};

// Track scroll depth
export const trackScrollDepth = (depth: number) => {
  trackEvent('scroll', {
    event_category: 'engagement',
    event_label: 'scroll_depth',
    value: depth,
  });
};

// Hook for automatic scroll tracking
export const useScrollTracking = () => {
  useEffect(() => {
    let maxScroll = 0;
    const milestones = [25, 50, 75, 90, 100];
    const tracked = new Set<number>();

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = Math.round((scrollTop / docHeight) * 100);

      if (scrollPercent > maxScroll) {
        maxScroll = scrollPercent;
        
        milestones.forEach((milestone) => {
          if (scrollPercent >= milestone && !tracked.has(milestone)) {
            tracked.add(milestone);
            trackScrollDepth(milestone);
          }
        });
      }
    };

    const throttledScroll = throttle(handleScroll, 1000);
    window.addEventListener('scroll', throttledScroll);

    return () => {
      window.removeEventListener('scroll', throttledScroll);
    };
  }, []);
};

// Utility function to throttle events
const throttle = (func: Function, limit: number) => {
  let inThrottle: boolean;
  return function(this: any, ...args: any[]) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
