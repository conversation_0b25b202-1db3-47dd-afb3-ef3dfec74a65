import { User<PERSON><PERSON><PERSON>, useUser } from "@clerk/clerk-react";
import { useAuth } from "@/hooks/useAuth";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { User, Shield, Clock, Mail } from "lucide-react";

export const UserProfile = () => {
  const { user: clerkUser } = useUser();
  const { user: dbUser } = useAuth();

  if (!clerkUser || !dbUser) {
    return null;
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "super_admin":
        return "bg-red-100 text-red-800 border-red-200";
      case "admin":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "content_editor":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "viewer":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatRole = (role: string) => {
    return role.split("_").map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(" ");
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <User className="w-5 h-5" />
          User Profile
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* User Avatar and Basic Info */}
        <div className="flex items-center gap-4">
          <UserButton 
            appearance={{
              elements: {
                avatarBox: "w-12 h-12",
              }
            }}
          />
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900">
              {clerkUser.firstName} {clerkUser.lastName}
            </h3>
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Mail className="w-3 h-3" />
              {clerkUser.emailAddresses[0]?.emailAddress}
            </div>
          </div>
        </div>

        {/* Role Badge */}
        <div className="flex items-center gap-2">
          <Shield className="w-4 h-4 text-gray-500" />
          <Badge className={getRoleBadgeColor(dbUser.role)}>
            {formatRole(dbUser.role)}
          </Badge>
        </div>

        {/* Last Login */}
        {dbUser.lastLogin && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Clock className="w-4 h-4" />
            Last login: {new Date(dbUser.lastLogin).toLocaleDateString()}
          </div>
        )}

        {/* Account Status */}
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${dbUser.isActive ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className={`text-sm ${dbUser.isActive ? 'text-green-600' : 'text-red-600'}`}>
            {dbUser.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>
      </CardContent>
    </Card>
  );
};
