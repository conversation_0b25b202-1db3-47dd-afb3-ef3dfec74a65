import { useEffect, useState } from "react";
import { useAuth } from "@clerk/clerk-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Mail, Clock, User } from "lucide-react";
import { useInvitationFlow } from "@/hooks/useInvitationFlow";
import { getRoleDisplayName, getRoleColor } from "@/hooks/useAdminRoles";

interface InvitationWelcomeProps {
  onComplete?: () => void;
}

export const InvitationWelcome = ({ onComplete }: InvitationWelcomeProps) => {
  const { user } = useAuth();
  const { invitationInfo, isCheckingInvitation } = useInvitationFlow();
  const [isAccepting, setIsAccepting] = useState(false);

  // Auto-accept invitation when component mounts if user has invitation
  useEffect(() => {
    if (invitationInfo?.hasInvitation && !isAccepting) {
      handleAcceptInvitation();
    }
  }, [invitationInfo]);

  const handleAcceptInvitation = async () => {
    if (!invitationInfo?.hasInvitation) return;

    setIsAccepting(true);
    
    // The invitation is automatically accepted when the user is created/updated
    // in the getOrCreateUser function, so we just need to show the welcome message
    // and then redirect to the appropriate area
    
    setTimeout(() => {
      setIsAccepting(false);
      onComplete?.();
    }, 2000);
  };

  if (isCheckingInvitation) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!invitationInfo?.hasInvitation) {
    return null;
  }

  const roleColor = getRoleColor(invitationInfo.role || "viewer");
  const roleDisplay = getRoleDisplayName(invitationInfo.role || "viewer");

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
            {isAccepting ? (
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
            ) : (
              <CheckCircle className="h-6 w-6 text-green-600" />
            )}
          </div>
          <CardTitle className="text-xl">
            {isAccepting ? "Setting up your account..." : "Welcome to the team!"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <p className="text-gray-600 mb-4">
              {isAccepting 
                ? "We're setting up your account with the invited permissions..."
                : "You've been invited to join as:"
              }
            </p>
            
            <Badge 
              variant="secondary" 
              className={`${roleColor} text-white px-3 py-1 text-sm font-medium`}
            >
              <User className="h-4 w-4 mr-1" />
              {roleDisplay}
            </Badge>
          </div>

          {invitationInfo.message && (
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="flex items-start">
                <Mail className="h-4 w-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                <p className="text-sm text-blue-800">
                  "{invitationInfo.message}"
                </p>
              </div>
            </div>
          )}

          <div className="flex items-center justify-center text-xs text-gray-500">
            <Clock className="h-3 w-3 mr-1" />
            Invitation expires: {new Date(invitationInfo.expiresAt || 0).toLocaleDateString()}
          </div>

          {!isAccepting && (
            <div className="text-center">
              <Button 
                onClick={handleAcceptInvitation}
                className="w-full"
                disabled={isAccepting}
              >
                Continue to Dashboard
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
