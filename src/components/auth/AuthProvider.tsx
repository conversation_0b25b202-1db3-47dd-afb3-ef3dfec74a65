import { ReactNode, createContext, useContext } from "react";
import { useAuth as useAuthHook, AuthUser, UserRole } from "@/hooks/useAuth";

interface AuthContextType {
  clerkUser: any;
  isLoaded: boolean;
  isSignedIn: boolean;
  user: AuthUser | null;
  hasRole: (role: UserRole) => boolean;
  hasPermission: (permission: string) => boolean;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  isContentEditor: boolean;
  canEditContent: boolean;
  canManageUsers: boolean;
  canManageSettings: boolean;
  canManageRoles: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const auth = useAuthHook();

  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
