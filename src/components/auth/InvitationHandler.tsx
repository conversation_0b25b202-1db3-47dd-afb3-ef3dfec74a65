import { useEffect, useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useNavigate } from "react-router-dom";
import { InvitationWelcome } from "./InvitationWelcome";
import { useInvitationFlow } from "@/hooks/useInvitationFlow";

interface InvitationHandlerProps {
  children: React.ReactNode;
}

export const InvitationHandler = ({ children }: InvitationHandlerProps) => {
  const { user, isLoaded } = useAuth();
  const { invitationInfo, isCheckingInvitation } = useInvitationFlow();
  const navigate = useNavigate();
  const [showWelcome, setShowWelcome] = useState(false);
  const [hasShownWelcome, setHasShownWelcome] = useState(false);

  // Check if we should show the invitation welcome
  useEffect(() => {
    if (
      isLoaded && 
      user && 
      invitationInfo?.hasInvitation && 
      !hasShownWelcome &&
      !isCheckingInvitation
    ) {
      // Check if this is a new user (created recently) or if they just accepted an invitation
      const isNewUser = user.createdAt && (Date.now() - user.createdAt) < 60000; // Within last minute
      
      if (isNewUser) {
        setShowWelcome(true);
      }
    }
  }, [isLoaded, user, invitationInfo, hasShownWelcome, isCheckingInvitation]);

  const handleWelcomeComplete = () => {
    setShowWelcome(false);
    setHasShownWelcome(true);
    
    // Navigate to appropriate area based on role
    if (user?.role === "admin" || user?.role === "super_admin") {
      navigate("/admin");
    } else if (user?.role === "content_editor") {
      navigate("/admin/content");
    } else {
      navigate("/");
    }
  };

  // Show invitation welcome if needed
  if (showWelcome && invitationInfo?.hasInvitation) {
    return <InvitationWelcome onComplete={handleWelcomeComplete} />;
  }

  // Show loading if checking invitation
  if (isCheckingInvitation) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return <>{children}</>;
};
