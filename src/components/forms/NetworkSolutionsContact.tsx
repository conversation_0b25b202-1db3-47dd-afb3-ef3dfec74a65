import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  CheckCircle,
  Network,
  Building2,
  Users
} from "lucide-react";
import { useContactFormSubmission } from "@/hooks/useContactForms";
import { useAnalyticsContext } from "@/components/analytics/AnalyticsProvider";
import { toast } from "sonner";

interface NetworkSolutionsContactProps {
  defaultService?: string;
  className?: string;
}

export const NetworkSolutionsContact = ({
  defaultService,
  className
}: NetworkSolutionsContactProps) => {
  const { submitContactForm } = useContactFormSubmission();
  const { trackForm, trackContent } = useAnalyticsContext();

  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    company: "",
    position: "",
    service: defaultService || "",
    bandwidth: "",
    locations: "",
    timeline: "",
    budget: "",
    message: "",
    newsletter: false,
    terms: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Track form field interactions
    trackContent(`network-solutions-form-${field}`, "form-field", "input", {
      field,
      defaultService,
      value: typeof value === 'string' ? value.substring(0, 50) : value // Limit string length for analytics
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.terms) {
      toast.error("Please accept the terms and conditions");
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data for contact form submission
      const contactData = {
        name: `${formData.firstName} ${formData.lastName}`.trim(),
        email: formData.email,
        phone: formData.phone,
        company: formData.company,
        service: formData.service,
        message: `Position: ${formData.position}
Service Interest: ${formData.service}
Required Bandwidth: ${formData.bandwidth}
Number of Locations: ${formData.locations}
Implementation Timeline: ${formData.timeline}
Estimated Budget: ${formData.budget}

Additional Requirements:
${formData.message}

Newsletter Subscription: ${formData.newsletter ? 'Yes' : 'No'}`,
        source: "network-solutions-page"
      };

      await submitContactForm(contactData);

      // Track successful form submission
      trackForm("network-solutions", "network-solutions-contact", true, {
        service: formData.service,
        bandwidth: formData.bandwidth,
        locations: formData.locations,
        timeline: formData.timeline,
        budget: formData.budget,
        newsletter: formData.newsletter,
        hasCompany: !!formData.company,
        hasPosition: !!formData.position
      });

      setIsSubmitted(true);

      // Reset form
      setFormData({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        company: "",
        position: "",
        service: defaultService || "",
        bandwidth: "",
        locations: "",
        timeline: "",
        budget: "",
        message: "",
        newsletter: false,
        terms: false
      });
    } catch (error) {
      // Track failed form submission
      trackForm("network-solutions", "network-solutions-contact", false, {
        service: formData.service,
        error: error instanceof Error ? error.message : "Unknown error"
      });
      // Error is handled in the hook
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={`grid grid-cols-1 lg:grid-cols-3 gap-8 ${className}`}>
      {/* Contact Form */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Network className="w-5 h-5" />
              Get Your Free Network Consultation
            </CardTitle>
            <p className="text-gray-600">
              Tell us about your network requirements and we'll design a custom solution for your business.
            </p>
          </CardHeader>
          <CardContent>
            {isSubmitted ? (
              <div className="text-center py-8">
                <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Thank You for Your Interest!
                </h3>
                <p className="text-gray-600 mb-6">
                  We've received your network consultation request. Our team will contact you within 24 hours to discuss your requirements.
                </p>
                <Button
                  onClick={() => setIsSubmitted(false)}
                  variant="outline"
                >
                  Submit Another Request
                </Button>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">First Name *</Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange("firstName", e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">Last Name *</Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange("lastName", e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    required
                  />
                </div>
              </div>

              {/* Company Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="company">Company Name *</Label>
                  <Input
                    id="company"
                    value={formData.company}
                    onChange={(e) => handleInputChange("company", e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="position">Your Position</Label>
                  <Input
                    id="position"
                    value={formData.position}
                    onChange={(e) => handleInputChange("position", e.target.value)}
                  />
                </div>
              </div>

              {/* Service Requirements */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="service">Service Interest *</Label>
                  <Select value={formData.service} onValueChange={(value) => handleInputChange("service", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a service" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="national-connectivity">National Connectivity</SelectItem>
                      <SelectItem value="branch-office">Branch Office Connectivity</SelectItem>
                      <SelectItem value="managed-internet">Managed Enterprise Internet</SelectItem>
                      <SelectItem value="custom">Custom Solution</SelectItem>
                      <SelectItem value="consultation">General Consultation</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="bandwidth">Required Bandwidth</Label>
                  <Select value={formData.bandwidth} onValueChange={(value) => handleInputChange("bandwidth", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select bandwidth" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10-50mbps">10-50 Mbps</SelectItem>
                      <SelectItem value="50-100mbps">50-100 Mbps</SelectItem>
                      <SelectItem value="100-500mbps">100-500 Mbps</SelectItem>
                      <SelectItem value="500mbps-1gbps">500 Mbps - 1 Gbps</SelectItem>
                      <SelectItem value="1gbps+">1 Gbps+</SelectItem>
                      <SelectItem value="not-sure">Not Sure</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="locations">Number of Locations</Label>
                  <Select value={formData.locations} onValueChange={(value) => handleInputChange("locations", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select locations" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">Single Location</SelectItem>
                      <SelectItem value="2-5">2-5 Locations</SelectItem>
                      <SelectItem value="6-10">6-10 Locations</SelectItem>
                      <SelectItem value="11-25">11-25 Locations</SelectItem>
                      <SelectItem value="25+">25+ Locations</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="timeline">Implementation Timeline</Label>
                  <Select value={formData.timeline} onValueChange={(value) => handleInputChange("timeline", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select timeline" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="immediate">Immediate (ASAP)</SelectItem>
                      <SelectItem value="1-3months">1-3 Months</SelectItem>
                      <SelectItem value="3-6months">3-6 Months</SelectItem>
                      <SelectItem value="6-12months">6-12 Months</SelectItem>
                      <SelectItem value="planning">Planning Phase</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="budget">Estimated Budget (Optional)</Label>
                <Select value={formData.budget} onValueChange={(value) => handleInputChange("budget", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select budget range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="under-1k">Under $1,000/month</SelectItem>
                    <SelectItem value="1k-5k">$1,000 - $5,000/month</SelectItem>
                    <SelectItem value="5k-10k">$5,000 - $10,000/month</SelectItem>
                    <SelectItem value="10k-25k">$10,000 - $25,000/month</SelectItem>
                    <SelectItem value="25k+">$25,000+/month</SelectItem>
                    <SelectItem value="discuss">Prefer to Discuss</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="message">Additional Requirements</Label>
                <Textarea
                  id="message"
                  value={formData.message}
                  onChange={(e) => handleInputChange("message", e.target.value)}
                  placeholder="Tell us about your specific network requirements, challenges, or questions..."
                  rows={4}
                />
              </div>

              {/* Checkboxes */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="newsletter"
                    checked={formData.newsletter}
                    onCheckedChange={(checked) => handleInputChange("newsletter", checked as boolean)}
                  />
                  <Label htmlFor="newsletter" className="text-sm">
                    Subscribe to our newsletter for network technology updates and industry insights
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="terms"
                    checked={formData.terms}
                    onCheckedChange={(checked) => handleInputChange("terms", checked as boolean)}
                  />
                  <Label htmlFor="terms" className="text-sm">
                    I agree to the terms and conditions and privacy policy *
                  </Label>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Get Free Consultation"}
              </Button>
            </form>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Contact Information */}
      <div className="space-y-6">
        {/* Contact Details */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Contact Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <Phone className="w-5 h-5 text-blue-600" />
              <div>
                <p className="font-medium">Phone</p>
                <p className="text-gray-600">+224 123 456 789</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Mail className="w-5 h-5 text-blue-600" />
              <div>
                <p className="font-medium">Email</p>
                <p className="text-gray-600"><EMAIL></p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <MapPin className="w-5 h-5 text-blue-600" />
              <div>
                <p className="font-medium">Address</p>
                <p className="text-gray-600">Conakry, Guinea</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Clock className="w-5 h-5 text-blue-600" />
              <div>
                <p className="font-medium">Business Hours</p>
                <p className="text-gray-600">Mon-Fri: 8AM-6PM</p>
                <p className="text-gray-600">24/7 Emergency Support</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Why Choose Us */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Why Choose Our Network Solutions?</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-1" />
              <div>
                <p className="font-medium text-sm">15+ Years Experience</p>
                <p className="text-xs text-gray-600">Proven track record in Guinea</p>
              </div>
            </div>

            <div className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-1" />
              <div>
                <p className="font-medium text-sm">99.9% Uptime SLA</p>
                <p className="text-xs text-gray-600">Guaranteed network reliability</p>
              </div>
            </div>

            <div className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-1" />
              <div>
                <p className="font-medium text-sm">24/7 Support</p>
                <p className="text-xs text-gray-600">Round-the-clock technical assistance</p>
              </div>
            </div>

            <div className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-1" />
              <div>
                <p className="font-medium text-sm">Local Expertise</p>
                <p className="text-xs text-gray-600">Deep understanding of Guinea market</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">500+</div>
                <div className="text-xs text-gray-600">Connected Businesses</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">99.9%</div>
                <div className="text-xs text-gray-600">Network Uptime</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
