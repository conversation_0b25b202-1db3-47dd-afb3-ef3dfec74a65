// SEO Components
export { SEO } from '../SEO';
export { Performance, CriticalCSS, usePerformanceMetrics } from '../Performance';
export { 
  Analytics, 
  trackPageView, 
  trackEvent, 
  trackConversion, 
  trackUserInteraction, 
  trackFormSubmission, 
  trackDownload, 
  trackExternalLink, 
  trackSearch, 
  trackVideo, 
  trackScrollDepth,
  useScrollTracking 
} from '../Analytics';

// Layout Components
export { 
  SEOLayout, 
  withSEO,
  HomePageLayout,
  ServicesPageLayout,
  AboutPageLayout,
  ContactPageLayout,
  BlogPageLayout,
  ProductPageLayout
} from '../SEOLayout';

// Types
export interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  noIndex?: boolean;
  canonical?: string;
}

export interface PerformanceProps {
  preloadImages?: string[];
  criticalCSS?: string;
  deferNonCritical?: boolean;
}

export interface AnalyticsProps {
  pageTitle?: string;
  pagePath?: string;
  userId?: string;
}

export interface SEOLayoutProps {
  children: React.ReactNode;
  seo?: SEOProps;
  performance?: PerformanceProps;
  analytics?: AnalyticsProps;
}

// Utility functions for SEO optimization
export const generateStructuredData = (type: string, data: Record<string, any>) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": type,
    ...data
  };

  return JSON.stringify(structuredData);
};

export const addStructuredData = (type: string, data: Record<string, any>) => {
  if (typeof window === 'undefined') return;

  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = generateStructuredData(type, data);
  document.head.appendChild(script);
};

// Common structured data generators
export const generateOrganizationSchema = () => ({
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "OfficeTech Guinea",
  "description": "Leading technology and security solutions provider in Guinea",
  "url": "https://officetech.gn",
  "logo": "https://officetech.gn/images/logo.png",
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "+***********-789",
    "contactType": "customer service",
    "availableLanguage": ["English", "French"]
  },
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "GN",
    "addressLocality": "Conakry",
    "addressRegion": "Conakry"
  },
  "sameAs": [
    "https://facebook.com/officetechgn",
    "https://twitter.com/officetechgn",
    "https://linkedin.com/company/officetechgn"
  ]
});

export const generateWebsiteSchema = () => ({
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "OfficeTech Guinea",
  "url": "https://officetech.gn",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://officetech.gn/search?q={search_term_string}",
    "query-input": "required name=search_term_string"
  }
});

export const generateServiceSchema = (service: {
  name: string;
  description: string;
  provider?: string;
  areaServed?: string;
}) => ({
  "@context": "https://schema.org",
  "@type": "Service",
  "name": service.name,
  "description": service.description,
  "provider": {
    "@type": "Organization",
    "name": service.provider || "OfficeTech Guinea"
  },
  "areaServed": {
    "@type": "Country",
    "name": service.areaServed || "Guinea"
  }
});

export const generateBreadcrumbSchema = (breadcrumbs: Array<{ name: string; url: string }>) => ({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": breadcrumbs.map((crumb, index) => ({
    "@type": "ListItem",
    "position": index + 1,
    "name": crumb.name,
    "item": crumb.url
  }))
});

export const generateArticleSchema = (article: {
  headline: string;
  description: string;
  author: string;
  datePublished: string;
  dateModified?: string;
  image?: string;
  url: string;
}) => ({
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": article.headline,
  "description": article.description,
  "author": {
    "@type": "Person",
    "name": article.author
  },
  "publisher": {
    "@type": "Organization",
    "name": "OfficeTech Guinea",
    "logo": {
      "@type": "ImageObject",
      "url": "https://officetech.gn/images/logo.png"
    }
  },
  "datePublished": article.datePublished,
  "dateModified": article.dateModified || article.datePublished,
  "image": article.image,
  "url": article.url
});

// SEO utility hooks
export const useSEOOptimization = () => {
  const addOrganizationSchema = () => {
    addStructuredData("Organization", generateOrganizationSchema());
  };

  const addWebsiteSchema = () => {
    addStructuredData("WebSite", generateWebsiteSchema());
  };

  const addServiceSchema = (service: Parameters<typeof generateServiceSchema>[0]) => {
    addStructuredData("Service", generateServiceSchema(service));
  };

  const addBreadcrumbSchema = (breadcrumbs: Parameters<typeof generateBreadcrumbSchema>[0]) => {
    addStructuredData("BreadcrumbList", generateBreadcrumbSchema(breadcrumbs));
  };

  const addArticleSchema = (article: Parameters<typeof generateArticleSchema>[0]) => {
    addStructuredData("Article", generateArticleSchema(article));
  };

  return {
    addOrganizationSchema,
    addWebsiteSchema,
    addServiceSchema,
    addBreadcrumbSchema,
    addArticleSchema
  };
};

// Performance optimization utilities
export const preloadResource = (href: string, as: string, type?: string) => {
  if (typeof window === 'undefined') return;

  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  if (type) link.type = type;
  if (as === 'font') link.crossOrigin = 'anonymous';
  document.head.appendChild(link);
};

export const prefetchResource = (href: string) => {
  if (typeof window === 'undefined') return;

  const link = document.createElement('link');
  link.rel = 'prefetch';
  link.href = href;
  document.head.appendChild(link);
};

export const preconnectToOrigin = (origin: string) => {
  if (typeof window === 'undefined') return;

  const link = document.createElement('link');
  link.rel = 'preconnect';
  link.href = origin;
  link.crossOrigin = 'anonymous';
  document.head.appendChild(link);
};

// Image optimization utilities
export const generateSrcSet = (baseUrl: string, sizes: number[]) => {
  return sizes.map(size => `${baseUrl}?w=${size} ${size}w`).join(', ');
};

export const generateSizes = (breakpoints: Array<{ minWidth?: number; size: string }>) => {
  return breakpoints
    .map(bp => bp.minWidth ? `(min-width: ${bp.minWidth}px) ${bp.size}` : bp.size)
    .join(', ');
};

// Critical CSS utilities
export const extractCriticalCSS = (selectors: string[]) => {
  if (typeof window === 'undefined') return '';

  const styles: string[] = [];
  const sheets = Array.from(document.styleSheets);

  sheets.forEach(sheet => {
    try {
      const rules = Array.from(sheet.cssRules || sheet.rules || []);
      rules.forEach(rule => {
        if (rule.type === CSSRule.STYLE_RULE) {
          const styleRule = rule as CSSStyleRule;
          if (selectors.some(selector => styleRule.selectorText?.includes(selector))) {
            styles.push(styleRule.cssText);
          }
        }
      });
    } catch (e) {
      // Cross-origin stylesheets may throw errors
      console.warn('Could not access stylesheet:', e);
    }
  });

  return styles.join('\n');
};
