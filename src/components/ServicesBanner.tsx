
import { useTranslation } from '@/contexts/I18nContext';

const ServicesBanner = () => {
  const { t } = useTranslation();

  const services = [
    t('services.cybersecurity') || 'Cybersecurity Solutions',
    t('services.webDesign') || 'Web Design & Development',
    t('services.surveillance') || 'Electronic Surveillance',
    t('services.training') || 'IT Training & Support',
    t('services.network') || 'Network Infrastructure',
    t('services.integration') || 'System Integration'
  ];

  return (
    <section className="bg-primary py-4 overflow-hidden">
      <div className="flex whitespace-nowrap animate-scroll">
        {[...services, ...services].map((service, index) => (
          <div key={index} className="flex items-center text-white mx-8">
            <span className="text-lg font-medium">{service}</span>
            <div className="w-2 h-2 bg-accent rounded-full mx-8"></div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default ServicesBanner;
