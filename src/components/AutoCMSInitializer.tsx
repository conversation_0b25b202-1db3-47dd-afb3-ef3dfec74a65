import { useEffect, useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";

export const AutoCMSInitializer = ({ children }: { children: React.ReactNode }) => {
  const [isInitializing, setIsInitializing] = useState(false);
  const [initializationComplete, setInitializationComplete] = useState(false);
  
  const status = useQuery(api.autoSetup.checkInitializationStatus);
  const autoInitialize = useMutation(api.autoSetup.autoInitializeCMS);

  useEffect(() => {
    const initializeCMS = async () => {
      if (status && status.needsInitialization && !isInitializing && !initializationComplete) {
        setIsInitializing(true);
        try {
          console.log("🚀 Auto-initializing CMS...");
          const result = await autoInitialize({});
          console.log("✅ CMS initialization result:", result);
          setInitializationComplete(true);
        } catch (error) {
          console.error("❌ CMS initialization failed:", error);
        } finally {
          setIsInitializing(false);
        }
      }
    };

    initializeCMS();
  }, [status, autoInitialize, isInitializing, initializationComplete]);

  // Show loading state during initialization
  if (status === undefined || (status?.needsInitialization && isInitializing)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {status === undefined ? "Loading..." : "Initializing CMS..."}
          </h2>
          <p className="text-gray-600">
            {status === undefined 
              ? "Please wait while we load the application" 
              : "Setting up content management system for the first time"
            }
          </p>
        </div>
      </div>
    );
  }

  // Render children once initialization is complete or not needed
  return <>{children}</>;
};
