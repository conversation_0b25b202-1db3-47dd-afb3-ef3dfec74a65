/**
 * @jest-environment jsdom
 */
import { render } from '@testing-library/react'
import { Performance, usePerformanceMetrics } from '../Performance'
import { SettingsProvider } from '@/contexts/SettingsContext'

// Mock the SettingsContext
const MockSettingsProvider = ({ children }: { children: React.ReactNode }) => (
  <SettingsProvider>
    {children}
  </SettingsProvider>
)

// Mock performance APIs
Object.defineProperty(window, 'performance', {
  value: {
    getEntriesByType: jest.fn(() => []),
    getEntriesByName: jest.fn(() => []),
  },
  writable: true,
})

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock PerformanceObserver
global.PerformanceObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
}))

describe('Performance Component', () => {
  beforeEach(() => {
    // Clear document head before each test
    document.head.innerHTML = ''
    document.body.innerHTML = ''
  })

  it('should render without errors', () => {
    render(
      <MockSettingsProvider>
        <Performance />
      </MockSettingsProvider>
    )
    
    // Component should render without throwing
    expect(true).toBe(true)
  })

  it('should preload images when specified', () => {
    render(
      <MockSettingsProvider>
        <Performance preloadImages={['/image1.jpg', '/image2.jpg']} />
      </MockSettingsProvider>
    )
    
    // Check if preload links are added to head
    const preloadLinks = document.querySelectorAll('link[rel="preload"][as="image"]')
    expect(preloadLinks.length).toBeGreaterThan(0)
  })

  it('should add critical CSS when provided', () => {
    const criticalCSS = 'body { font-family: Arial; }'
    
    render(
      <MockSettingsProvider>
        <Performance criticalCSS={criticalCSS} />
      </MockSettingsProvider>
    )
    
    // Check if style tag is added
    const styleTag = document.querySelector('style')
    expect(styleTag?.textContent).toBe(criticalCSS)
  })

  it('should add resource hints', () => {
    render(
      <MockSettingsProvider>
        <Performance />
      </MockSettingsProvider>
    )
    
    // Check for DNS prefetch links
    const dnsPrefetchLinks = document.querySelectorAll('link[rel="dns-prefetch"]')
    expect(dnsPrefetchLinks.length).toBeGreaterThan(0)
    
    // Check for preconnect links
    const preconnectLinks = document.querySelectorAll('link[rel="preconnect"]')
    expect(preconnectLinks.length).toBeGreaterThan(0)
  })
})

describe('Performance Utilities', () => {
  it('should generate srcset correctly', () => {
    const { generateSrcSet } = require('../seo')
    
    const srcSet = generateSrcSet('/image.jpg', [400, 800, 1200])
    expect(srcSet).toBe('/image.jpg?w=400 400w, /image.jpg?w=800 800w, /image.jpg?w=1200 1200w')
  })

  it('should generate sizes correctly', () => {
    const { generateSizes } = require('../seo')
    
    const sizes = generateSizes([
      { minWidth: 768, size: '50vw' },
      { size: '100vw' }
    ])
    expect(sizes).toBe('(min-width: 768px) 50vw, 100vw')
  })

  it('should preload resources correctly', () => {
    const { preloadResource } = require('../seo')
    
    preloadResource('/font.woff2', 'font', 'font/woff2')
    
    const preloadLink = document.querySelector('link[rel="preload"][as="font"]')
    expect(preloadLink?.getAttribute('href')).toBe('/font.woff2')
    expect(preloadLink?.getAttribute('type')).toBe('font/woff2')
    expect(preloadLink?.getAttribute('crossorigin')).toBe('anonymous')
  })

  it('should prefetch resources correctly', () => {
    const { prefetchResource } = require('../seo')
    
    prefetchResource('/next-page.html')
    
    const prefetchLink = document.querySelector('link[rel="prefetch"]')
    expect(prefetchLink?.getAttribute('href')).toBe('/next-page.html')
  })

  it('should preconnect to origins correctly', () => {
    const { preconnectToOrigin } = require('../seo')
    
    preconnectToOrigin('https://fonts.googleapis.com')
    
    const preconnectLink = document.querySelector('link[rel="preconnect"]')
    expect(preconnectLink?.getAttribute('href')).toBe('https://fonts.googleapis.com')
    expect(preconnectLink?.getAttribute('crossorigin')).toBe('anonymous')
  })
})

// Test component that uses the performance hook
const TestPerformanceComponent = () => {
  usePerformanceMetrics()
  return <div>Test Component</div>
}

describe('usePerformanceMetrics Hook', () => {
  it('should not throw when used', () => {
    expect(() => {
      render(<TestPerformanceComponent />)
    }).not.toThrow()
  })
})
