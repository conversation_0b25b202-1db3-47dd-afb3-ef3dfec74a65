/**
 * @jest-environment jsdom
 */
import { render } from '@testing-library/react'
import { SEO } from '../SEO'
import { SettingsProvider } from '@/contexts/SettingsContext'

// Mock the SettingsContext
const MockSettingsProvider = ({ children }: { children: React.ReactNode }) => (
  <SettingsProvider>
    {children}
  </SettingsProvider>
)

describe('SEO Component', () => {
  beforeEach(() => {
    // Clear document head before each test
    document.head.innerHTML = ''
  })

  it('should set document title', () => {
    render(
      <MockSettingsProvider>
        <SEO title="Test Page" />
      </MockSettingsProvider>
    )

    expect(document.title).toBe('Test Page | OfficeTech Guinea')
  })

  it('should set meta description', () => {
    render(
      <MockSettingsProvider>
        <SEO description="Test description" />
      </MockSettingsProvider>
    )

    const metaDescription = document.querySelector('meta[name="description"]')
    expect(metaDescription?.getAttribute('content')).toBe('Test description')
  })

  it('should set meta keywords', () => {
    render(
      <MockSettingsProvider>
        <SEO keywords="test, keywords, seo" />
      </MockSettingsProvider>
    )

    const metaKeywords = document.querySelector('meta[name="keywords"]')
    expect(metaKeywords?.getAttribute('content')).toBe('test, keywords, seo')
  })

  it('should set Open Graph tags', () => {
    render(
      <MockSettingsProvider>
        <SEO 
          title="Test Page"
          description="Test description"
          image="/test-image.jpg"
          url="https://test.com"
        />
      </MockSettingsProvider>
    )

    const ogTitle = document.querySelector('meta[property="og:title"]')
    const ogDescription = document.querySelector('meta[property="og:description"]')
    const ogImage = document.querySelector('meta[property="og:image"]')
    const ogUrl = document.querySelector('meta[property="og:url"]')

    expect(ogTitle?.getAttribute('content')).toBe('Test Page | OfficeTech Guinea')
    expect(ogDescription?.getAttribute('content')).toBe('Test description')
    expect(ogImage?.getAttribute('content')).toBe('/test-image.jpg')
    expect(ogUrl?.getAttribute('content')).toBe('https://test.com')
  })

  it('should set Twitter Card tags', () => {
    render(
      <MockSettingsProvider>
        <SEO 
          title="Test Page"
          description="Test description"
          image="/test-image.jpg"
        />
      </MockSettingsProvider>
    )

    const twitterCard = document.querySelector('meta[name="twitter:card"]')
    const twitterTitle = document.querySelector('meta[name="twitter:title"]')
    const twitterDescription = document.querySelector('meta[name="twitter:description"]')
    const twitterImage = document.querySelector('meta[name="twitter:image"]')

    expect(twitterCard?.getAttribute('content')).toBe('summary_large_image')
    expect(twitterTitle?.getAttribute('content')).toBe('Test Page | OfficeTech Guinea')
    expect(twitterDescription?.getAttribute('content')).toBe('Test description')
    expect(twitterImage?.getAttribute('content')).toBe('/test-image.jpg')
  })

  it('should set canonical URL', () => {
    render(
      <MockSettingsProvider>
        <SEO canonical="https://test.com/canonical" />
      </MockSettingsProvider>
    )

    const canonical = document.querySelector('link[rel="canonical"]')
    expect(canonical?.getAttribute('href')).toBe('https://test.com/canonical')
  })

  it('should set noindex when specified', () => {
    render(
      <MockSettingsProvider>
        <SEO noIndex={true} />
      </MockSettingsProvider>
    )

    const robots = document.querySelector('meta[name="robots"]')
    expect(robots?.getAttribute('content')).toBe('noindex,nofollow')
  })
})
