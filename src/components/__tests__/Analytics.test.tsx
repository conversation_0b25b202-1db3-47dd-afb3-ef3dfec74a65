/**
 * @jest-environment jsdom
 */
import { render } from '@testing-library/react'
import { Analytics, trackEvent, trackPageView } from '../Analytics'
import { SettingsProvider } from '@/contexts/SettingsContext'

// Mock console.log to capture analytics events
const mockConsoleLog = jest.fn()
console.log = mockConsoleLog

// Mock the SettingsContext
const MockSettingsProvider = ({ children }: { children: React.ReactNode }) => (
  <SettingsProvider>
    {children}
  </SettingsProvider>
)

describe('Analytics Component', () => {
  beforeEach(() => {
    // Clear document head and reset mocks
    document.head.innerHTML = ''
    mockConsoleLog.mockClear()
    
    // Reset window objects
    delete (window as any).gtag
    delete (window as any).fbq
    delete (window as any).dataLayer
  })

  it('should render without errors', () => {
    render(
      <MockSettingsProvider>
        <Analytics pageTitle="Test Page" pagePath="/test" />
      </MockSettingsProvider>
    )
    
    // Component should render without throwing
    expect(true).toBe(true)
  })

  it('should track page view', () => {
    trackPageView('Test Page', '/test')
    
    expect(mockConsoleLog).toHaveBeenCalledWith(
      'Page view tracked:',
      { title: 'Test Page', path: '/test' }
    )
  })

  it('should track custom events', () => {
    trackEvent('button_click', { button_name: 'test_button' })
    
    expect(mockConsoleLog).toHaveBeenCalledWith(
      'Event tracked:',
      'button_click',
      { button_name: 'test_button' }
    )
  })
})

describe('Analytics Utility Functions', () => {
  beforeEach(() => {
    mockConsoleLog.mockClear()
  })

  it('should track form submission', () => {
    const { trackFormSubmission } = require('../Analytics')
    
    trackFormSubmission('contact_form', true)
    
    expect(mockConsoleLog).toHaveBeenCalledWith(
      'Event tracked:',
      'form_submit',
      {
        event_category: 'form',
        event_label: 'contact_form',
        success: true
      }
    )
  })

  it('should track downloads', () => {
    const { trackDownload } = require('../Analytics')
    
    trackDownload('brochure.pdf', 'pdf')
    
    expect(mockConsoleLog).toHaveBeenCalledWith(
      'Event tracked:',
      'file_download',
      {
        event_category: 'download',
        event_label: 'brochure.pdf',
        file_type: 'pdf'
      }
    )
  })

  it('should track external links', () => {
    const { trackExternalLink } = require('../Analytics')
    
    trackExternalLink('https://external.com', 'External Link')
    
    expect(mockConsoleLog).toHaveBeenCalledWith(
      'Event tracked:',
      'click',
      {
        event_category: 'external_link',
        event_label: 'https://external.com',
        link_text: 'External Link'
      }
    )
  })

  it('should track search queries', () => {
    const { trackSearch } = require('../Analytics')
    
    trackSearch('test query', 5)
    
    expect(mockConsoleLog).toHaveBeenCalledWith(
      'Event tracked:',
      'search',
      {
        search_term: 'test query',
        event_category: 'search',
        results_count: 5
      }
    )
  })

  it('should track video interactions', () => {
    const { trackVideo } = require('../Analytics')
    
    trackVideo('play', 'Demo Video', 25)
    
    expect(mockConsoleLog).toHaveBeenCalledWith(
      'Event tracked:',
      'video_play',
      {
        event_category: 'video',
        event_label: 'Demo Video',
        progress: 25
      }
    )
  })

  it('should track scroll depth', () => {
    const { trackScrollDepth } = require('../Analytics')
    
    trackScrollDepth(50)
    
    expect(mockConsoleLog).toHaveBeenCalledWith(
      'Event tracked:',
      'scroll',
      {
        event_category: 'engagement',
        event_label: 'scroll_depth',
        value: 50
      }
    )
  })
})
