
import { Clock, Users, Award } from 'lucide-react';
import { useTranslation } from '@/contexts/I18nContext';

const KeyFeatures = () => {
  const { t } = useTranslation();

  const features = [
    {
      icon: Clock,
      title: t('features.support24') || '24/7 Support',
      description: t('features.supportDescription') || 'Round-the-clock monitoring and support for all your systems'
    },
    {
      icon: Users,
      title: t('features.expertTeam') || 'Expert Team',
      description: t('features.teamDescription') || 'Certified professionals with years of industry experience'
    },
    {
      icon: Award,
      title: t('features.qualityAssured') || 'Quality Assured',
      description: t('features.qualityDescription') || 'ISO certified processes and industry-leading standards'
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <div key={index} className="text-center group">
                <div className="w-20 h-20 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-600 transition-colors duration-300">
                  <IconComponent className="w-10 h-10 text-blue-600 group-hover:text-white transition-colors duration-300" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default KeyFeatures;
