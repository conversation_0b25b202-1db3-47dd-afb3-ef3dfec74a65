import { useEffect, useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { CheckCircle, AlertCircle, X } from "lucide-react";

export const CMSStatusNotification = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasShownInitialized, setHasShownInitialized] = useState(false);
  
  const status = useQuery(api.autoSetup.checkInitializationStatus);

  useEffect(() => {
    if (status) {
      // Show notification when CMS becomes initialized for the first time
      if (status.isInitialized && !hasShownInitialized) {
        setIsVisible(true);
        setHasShownInitialized(true);
        
        // Auto-hide after 5 seconds
        const timer = setTimeout(() => {
          setIsVisible(false);
        }, 5000);
        
        return () => clearTimeout(timer);
      }
    }
  }, [status, hasShownInitialized]);

  if (!isVisible || !status?.isInitialized) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <div className="bg-white border border-green-200 rounded-lg shadow-lg p-4">
        <div className="flex items-start space-x-3">
          <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
          <div className="flex-1">
            <h4 className="text-sm font-semibold text-gray-900">
              CMS Initialized Successfully!
            </h4>
            <p className="text-xs text-gray-600 mt-1">
              Content management system is ready. All pages now support dynamic content editing through the admin interface.
            </p>
          </div>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};
