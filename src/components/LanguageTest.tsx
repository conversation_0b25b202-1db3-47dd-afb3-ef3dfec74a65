import { useTranslation, useLanguage } from '@/contexts/I18nContext';

export const LanguageTest = () => {
  const { t } = useTranslation();
  const { language, setLanguage } = useLanguage();

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg z-50">
      <h3 className="font-bold mb-2">Language Test</h3>
      <p className="text-sm mb-2">Current: {language}</p>
      <p className="text-sm mb-2">Hero Title: {t('home.heroTitle')}</p>
      <p className="text-sm mb-2">Hero Button: {t('home.heroButton')}</p>
      <div className="flex space-x-2">
        <button 
          onClick={() => setLanguage('en')}
          className={`px-2 py-1 text-xs rounded ${language === 'en' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
        >
          EN
        </button>
        <button 
          onClick={() => setLanguage('es')}
          className={`px-2 py-1 text-xs rounded ${language === 'es' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
        >
          ES
        </button>
      </div>
    </div>
  );
};
