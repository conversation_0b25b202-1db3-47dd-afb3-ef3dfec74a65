import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Network,
  Globe,
  Building2,
  Wifi,
  ArrowRight,
  Shield,
  Zap,
  Users
} from 'lucide-react';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useLanguage } from '@/contexts/I18nContext';
import { DynamicContent } from '@/components/content/DynamicContent';

const NetworkSolutionsSection = () => {
  const { language } = useLanguage();

  // Get all network solution cards in current language
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for network solution cards and sort by order
  const networkSolutions = allContent?.filter(item =>
    item.contentType?.name === "network_solution" && item.data.slug
  ).sort((a, b) => (a.data.order || 0) - (b.data.order || 0)) || [];

  // Fallback data if no dynamic content is available
  const fallbackNetworkSolutions = [
    {
      id: 'national-connectivity',
      title: language === 'en' ? 'National Connectivity' : 'Conectividad Nacional',
      description: language === 'en' ? 'Comprehensive network solutions across the entire country with coverage in Bata, Malabo & Mongomo.' : 'Soluciones de red integrales en todo el país con cobertura en Bata, Malabo y Mongomo.',
      icon: Globe,
      features: language === 'en' ? [
        'Private Networks',
        'High-Speed Internet',
        'Security Solutions',
        '24/7 Technical Support'
      ] : [
        'Redes Privadas',
        'Internet de Alta Velocidad',
        'Soluciones de Seguridad',
        'Soporte Técnico 24/7'
      ],
      link: '/network-solutions/national-connectivity'
    },
    {
      id: 'international-connectivity',
      title: language === 'en' ? 'International Connectivity' : 'Conectividad Internacional',
      description: language === 'en' ? 'Advanced solutions like MPLS, SD-WAN, and VPL to ensure your business stays connected globally.' : 'Soluciones avanzadas como MPLS, SD-WAN y VPL para asegurar que tu negocio se mantenga conectado globalmente.',
      icon: Network,
      features: language === 'en' ? [
        'MPLS (Multi-Protocol Label Switching)',
        'SD-WAN (Software-Defined WAN)',
        'VPL (Layer 2 VPN)',
        'Uninterrupted Connectivity'
      ] : [
        'MPLS (Conmutación de Etiquetas Multi-Protocolo)',
        'SD-WAN (WAN Definida por Software)',
        'VPL (VPN de Capa 2)',
        'Conectividad Ininterrumpida'
      ],
      link: '/network-solutions/international-connectivity'
    },
    {
      id: 'branch-office-connectivity',
      title: language === 'en' ? 'Branch Office Connectivity' : 'Conectividad de Sucursales',
      description: language === 'en' ? 'Keep your enterprise connected, collaborative, and competitive across all branch locations.' : 'Mantén tu empresa conectada, colaborativa y competitiva en todas las ubicaciones de sucursales.',
      icon: Building2,
      features: language === 'en' ? [
        'High-Speed Connectivity',
        'Centralized Management',
        'Enhanced Security',
        'Cost Efficiency & Scalability'
      ] : [
        'Conectividad de Alta Velocidad',
        'Gestión Centralizada',
        'Seguridad Mejorada',
        'Eficiencia de Costos y Escalabilidad'
      ],
      link: '/network-solutions/branch-office-connectivity'
    },
    {
      id: 'managed-enterprise-internet',
      title: language === 'en' ? 'Managed Enterprise Internet' : 'Internet Empresarial Gestionado',
      description: language === 'en' ? 'Comprehensive end-to-end internet infrastructure solutions with proactive monitoring and expert support.' : 'Soluciones integrales de infraestructura de internet de extremo a extremo con monitoreo proactivo y soporte experto.',
      icon: Wifi,
      features: language === 'en' ? [
        'Reliable High-Speed Connectivity',
        'Proactive Monitoring & Maintenance',
        'Enterprise-Grade Security',
        'Expert Support 24/7'
      ] : [
        'Conectividad Confiable de Alta Velocidad',
        'Monitoreo y Mantenimiento Proactivo',
        'Seguridad de Grado Empresarial',
        'Soporte Experto 24/7'
      ],
      link: '/network-solutions/managed-enterprise-internet'
    }
  ];

  // Use dynamic content if available, otherwise fallback
  const displaySolutions = networkSolutions.length > 0 ? networkSolutions : fallbackNetworkSolutions;

  return (
    <section className="py-20 bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-4">
              <Network className="w-8 h-8 text-blue-600" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold">
              {language === 'en' ? 'Network' : 'Soluciones de'} <span className="text-blue-300">{language === 'en' ? 'Solutions' : 'Red'}</span>
            </h2>
          </div>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
            {language === 'en'
              ? 'Connect your business to success with our cutting-edge network infrastructure solutions. From local connectivity to global reach, we deliver reliable, secure, and scalable networking.'
              : 'Conecta tu negocio al éxito con nuestras soluciones de infraestructura de red de vanguardia. Desde conectividad local hasta alcance global, ofrecemos redes confiables, seguras y escalables.'
            }
          </p>
        </div>

        {/* Network Solutions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {displaySolutions.map((solution) => {
            // Handle both dynamic content and fallback data
            const IconComponent = solution.icon || Network;
            const title = solution.data?.title || solution.title;
            const description = solution.data?.description || solution.description;
            const features = solution.data?.features || solution.features || [];
            const link = solution.data?.slug ? `/network-solutions/${solution.data.slug}` : solution.link;

            return (
              <Card
                key={solution.id || solution._id}
                className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 bg-white/10 backdrop-blur-sm border-white/20 text-white"
              >
                <CardContent className="p-6">
                  <div className="flex items-start mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-white transition-colors duration-300">
                      <IconComponent className="w-6 h-6 text-blue-600 group-hover:text-blue-700 transition-colors duration-300" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold mb-2">
                        {title}
                      </h3>
                    </div>
                  </div>

                  <p className="text-blue-100 mb-4 leading-relaxed">
                    {description}
                  </p>

                  {/* Features List */}
                  <ul className="space-y-2 mb-6">
                    {features.map((feature, index) => (
                      <li key={index} className="text-sm text-blue-200 flex items-center">
                        <div className="w-1.5 h-1.5 bg-blue-300 rounded-full mr-3"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>

                  <Link to={link}>
                    <Button 
                      variant="outline" 
                      className="w-full bg-transparent border-white/30 text-white hover:bg-white hover:text-blue-900 transition-all duration-300"
                    >
                      {language === 'en' ? 'Learn More' : 'Saber Más'}
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Key Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">Enterprise Security</h3>
            <p className="text-blue-200">
              Advanced security protocols and monitoring to protect your network infrastructure.
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Zap className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">High Performance</h3>
            <p className="text-blue-200">
              Optimized network performance with minimal latency and maximum uptime.
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-bold mb-2">Expert Support</h3>
            <p className="text-blue-200">
              Dedicated team of network specialists available 24/7 for support and maintenance.
            </p>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
          <h3 className="text-2xl font-bold mb-4">
            Ready to Transform Your Network Infrastructure?
          </h3>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            Whether you need to connect multiple locations, ensure reliable internet access, or implement 
            advanced networking solutions, our experts are here to help design the perfect solution for your business.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/network-solutions">
              <Button size="lg" className="bg-white text-blue-900 hover:bg-blue-50 px-8">
                Explore All Solutions
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/contact">
              <Button size="lg" variant="outline" className="border-white/30 text-white hover:bg-white hover:text-blue-900 px-8">
                <Network className="mr-2 h-5 w-5" />
                Get Network Assessment
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NetworkSolutionsSection;
