import { useEffect, useRef, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useAnalyticsContext } from './AnalyticsProvider';

interface UserBehaviorTrackerProps {
  children: React.ReactNode;
}

export const UserBehaviorTracker = ({ children }: UserBehaviorTrackerProps) => {
  const location = useLocation();
  const { trackContent } = useAnalyticsContext();
  const startTimeRef = useRef<number>(Date.now());
  const scrollDepthRef = useRef<number>(0);
  const maxScrollDepthRef = useRef<number>(0);
  const clickCountRef = useRef<number>(0);
  const lastScrollTimeRef = useRef<number>(Date.now());
  const engagementIntervalsRef = useRef<Set<number>>(new Set());

  // Track page engagement time intervals
  const trackEngagementInterval = useCallback((seconds: number) => {
    if (!engagementIntervalsRef.current.has(seconds)) {
      engagementIntervalsRef.current.add(seconds);
      trackContent(`page-engagement-${location.pathname}`, 'engagement', 'time_milestone', {
        path: location.pathname,
        seconds,
        scrollDepth: maxScrollDepthRef.current,
        clickCount: clickCountRef.current
      });
    }
  }, [location.pathname, trackContent]);

  // Track scroll depth
  const handleScroll = useCallback(() => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;
    
    const scrollPercentage = Math.round((scrollTop + windowHeight) / documentHeight * 100);
    scrollDepthRef.current = scrollPercentage;
    
    if (scrollPercentage > maxScrollDepthRef.current) {
      maxScrollDepthRef.current = scrollPercentage;
      
      // Track scroll milestones
      const milestones = [25, 50, 75, 90, 100];
      milestones.forEach(milestone => {
        if (scrollPercentage >= milestone && !engagementIntervalsRef.current.has(`scroll-${milestone}`)) {
          engagementIntervalsRef.current.add(`scroll-${milestone}`);
          trackContent(`scroll-depth-${location.pathname}`, 'scroll', 'milestone', {
            path: location.pathname,
            percentage: milestone,
            timeToReach: Date.now() - startTimeRef.current
          });
        }
      });
    }
    
    lastScrollTimeRef.current = Date.now();
  }, [location.pathname, trackContent]);

  // Track clicks
  const handleClick = useCallback((event: MouseEvent) => {
    clickCountRef.current += 1;
    
    const target = event.target as HTMLElement;
    const tagName = target.tagName.toLowerCase();
    const className = target.className;
    const id = target.id;
    const href = target.getAttribute('href');
    const text = target.textContent?.substring(0, 50) || '';
    
    trackContent(`click-${location.pathname}`, 'interaction', 'click', {
      path: location.pathname,
      element: tagName,
      className,
      id,
      href,
      text,
      clickCount: clickCountRef.current,
      timeOnPage: Date.now() - startTimeRef.current,
      scrollDepth: scrollDepthRef.current
    });
  }, [location.pathname, trackContent]);

  // Track mouse movement patterns
  const handleMouseMove = useCallback((event: MouseEvent) => {
    // Sample mouse movements (only track occasionally to avoid spam)
    if (Math.random() < 0.01) { // 1% sampling rate
      trackContent(`mouse-movement-${location.pathname}`, 'interaction', 'mouse_move', {
        path: location.pathname,
        x: event.clientX,
        y: event.clientY,
        timeOnPage: Date.now() - startTimeRef.current
      });
    }
  }, [location.pathname, trackContent]);

  // Track keyboard interactions
  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    trackContent(`keyboard-${location.pathname}`, 'interaction', 'keypress', {
      path: location.pathname,
      key: event.key,
      ctrlKey: event.ctrlKey,
      altKey: event.altKey,
      shiftKey: event.shiftKey,
      timeOnPage: Date.now() - startTimeRef.current
    });
  }, [location.pathname, trackContent]);

  // Track page visibility changes
  const handleVisibilityChange = useCallback(() => {
    const isVisible = !document.hidden;
    trackContent(`visibility-${location.pathname}`, 'engagement', isVisible ? 'visible' : 'hidden', {
      path: location.pathname,
      timeOnPage: Date.now() - startTimeRef.current,
      scrollDepth: maxScrollDepthRef.current
    });
  }, [location.pathname, trackContent]);

  // Track page focus/blur
  const handleFocus = useCallback(() => {
    trackContent(`focus-${location.pathname}`, 'engagement', 'focus', {
      path: location.pathname,
      timeOnPage: Date.now() - startTimeRef.current
    });
  }, [location.pathname, trackContent]);

  const handleBlur = useCallback(() => {
    trackContent(`blur-${location.pathname}`, 'engagement', 'blur', {
      path: location.pathname,
      timeOnPage: Date.now() - startTimeRef.current,
      maxScrollDepth: maxScrollDepthRef.current
    });
  }, [location.pathname, trackContent]);

  // Reset tracking data when location changes
  useEffect(() => {
    startTimeRef.current = Date.now();
    scrollDepthRef.current = 0;
    maxScrollDepthRef.current = 0;
    clickCountRef.current = 0;
    lastScrollTimeRef.current = Date.now();
    engagementIntervalsRef.current.clear();
  }, [location.pathname]);

  // Set up event listeners
  useEffect(() => {
    const throttledScroll = throttle(handleScroll, 100);
    const throttledMouseMove = throttle(handleMouseMove, 500);
    
    window.addEventListener('scroll', throttledScroll, { passive: true });
    document.addEventListener('click', handleClick, true);
    document.addEventListener('mousemove', throttledMouseMove, { passive: true });
    document.addEventListener('keypress', handleKeyPress);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);

    return () => {
      window.removeEventListener('scroll', throttledScroll);
      document.removeEventListener('click', handleClick, true);
      document.removeEventListener('mousemove', throttledMouseMove);
      document.removeEventListener('keypress', handleKeyPress);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
    };
  }, [handleScroll, handleClick, handleMouseMove, handleKeyPress, handleVisibilityChange, handleFocus, handleBlur]);

  // Track engagement time intervals
  useEffect(() => {
    const intervals = [10, 30, 60, 120, 300, 600]; // 10s, 30s, 1m, 2m, 5m, 10m
    
    const timers = intervals.map(seconds => 
      setTimeout(() => trackEngagementInterval(seconds), seconds * 1000)
    );

    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [trackEngagementInterval]);

  // Track session end when component unmounts
  useEffect(() => {
    return () => {
      const sessionDuration = Date.now() - startTimeRef.current;
      trackContent(`session-end-${location.pathname}`, 'engagement', 'session_end', {
        path: location.pathname,
        duration: sessionDuration,
        maxScrollDepth: maxScrollDepthRef.current,
        totalClicks: clickCountRef.current,
        engagementIntervals: Array.from(engagementIntervalsRef.current)
      });
    };
  }, [location.pathname, trackContent]);

  return <>{children}</>;
};

// Utility function to throttle events
function throttle<T extends (...args: any[]) => any>(func: T, limit: number): T {
  let inThrottle: boolean;
  return ((...args: any[]) => {
    if (!inThrottle) {
      func.apply(null, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }) as T;
}

// Hook for tracking specific user interactions
export const useUserBehaviorTracking = () => {
  const { trackContent } = useAnalyticsContext();
  const location = useLocation();

  const trackButtonClick = useCallback((buttonId: string, buttonText?: string) => {
    trackContent(`button-${buttonId}`, 'interaction', 'button_click', {
      path: location.pathname,
      buttonId,
      buttonText: buttonText?.substring(0, 50)
    });
  }, [trackContent, location.pathname]);

  const trackLinkClick = useCallback((href: string, linkText?: string) => {
    trackContent(`link-click`, 'interaction', 'link_click', {
      path: location.pathname,
      href,
      linkText: linkText?.substring(0, 50),
      isExternal: href.startsWith('http') && !href.includes(window.location.hostname)
    });
  }, [trackContent, location.pathname]);

  const trackFormInteraction = useCallback((formId: string, action: string, fieldName?: string) => {
    trackContent(`form-${formId}`, 'form', action, {
      path: location.pathname,
      formId,
      fieldName
    });
  }, [trackContent, location.pathname]);

  const trackSearchQuery = useCallback((query: string, resultCount?: number) => {
    trackContent('search-query', 'search', 'query', {
      path: location.pathname,
      query: query.substring(0, 100),
      resultCount
    });
  }, [trackContent, location.pathname]);

  return {
    trackButtonClick,
    trackLinkClick,
    trackFormInteraction,
    trackSearchQuery
  };
};
