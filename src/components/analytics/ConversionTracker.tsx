import { useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useAnalyticsContext } from './AnalyticsProvider';

interface ConversionEvent {
  name: string;
  value?: number;
  currency?: string;
  metadata?: Record<string, any>;
}

interface FunnelStep {
  name: string;
  path: string;
  required: boolean;
}

interface ConversionTrackerProps {
  children: React.ReactNode;
}

export const ConversionTracker = ({ children }: ConversionTrackerProps) => {
  const location = useLocation();
  const { trackContent } = useAnalyticsContext();

  // Define conversion funnels
  const conversionFunnels = {
    contact: [
      { name: 'Landing', path: '/', required: false },
      { name: 'Services View', path: '/services', required: false },
      { name: 'Contact Page', path: '/contact', required: true },
      { name: 'Form Submission', path: '/contact', required: true }
    ],
    serviceInquiry: [
      { name: 'Homepage', path: '/', required: false },
      { name: 'Services Page', path: '/services', required: true },
      { name: 'Service Detail', path: '/services/', required: true },
      { name: 'Contact Form', path: '/contact', required: true }
    ],
    networkSolutions: [
      { name: 'Homepage', path: '/', required: false },
      { name: 'Network Solutions', path: '/network-solutions', required: true },
      { name: 'Solution Detail', path: '/network-solutions/', required: true },
      { name: 'Contact Form', path: '/contact', required: true }
    ]
  };

  // Track funnel progression
  const trackFunnelStep = useCallback((funnelName: string, stepName: string) => {
    trackContent(`funnel-${funnelName}-${stepName}`, 'funnel', 'step', {
      funnelName,
      stepName,
      path: location.pathname,
      timestamp: Date.now()
    });
  }, [location.pathname, trackContent]);

  // Check and track funnel steps based on current path
  useEffect(() => {
    Object.entries(conversionFunnels).forEach(([funnelName, steps]) => {
      steps.forEach(step => {
        if (location.pathname === step.path || 
            (step.path.endsWith('/') && location.pathname.startsWith(step.path))) {
          trackFunnelStep(funnelName, step.name);
        }
      });
    });
  }, [location.pathname, trackFunnelStep]);

  return <>{children}</>;
};

// Hook for tracking conversions
export const useConversionTracking = () => {
  const { trackContent } = useAnalyticsContext();
  const location = useLocation();

  const trackConversion = useCallback((event: ConversionEvent) => {
    trackContent(`conversion-${event.name}`, 'conversion', 'complete', {
      conversionName: event.name,
      value: event.value,
      currency: event.currency,
      path: location.pathname,
      timestamp: Date.now(),
      ...event.metadata
    });
  }, [trackContent, location.pathname]);

  const trackConversionStep = useCallback((conversionName: string, step: string, metadata?: Record<string, any>) => {
    trackContent(`conversion-step-${conversionName}-${step}`, 'conversion', 'step', {
      conversionName,
      step,
      path: location.pathname,
      timestamp: Date.now(),
      ...metadata
    });
  }, [trackContent, location.pathname]);

  // Predefined conversion tracking methods
  const trackContactFormSubmission = useCallback((formData: any) => {
    trackConversion({
      name: 'contact_form_submission',
      value: 1,
      metadata: {
        source: formData.source,
        service: formData.service,
        hasCompany: !!formData.company,
        hasPhone: !!formData.phone
      }
    });
  }, [trackConversion]);

  const trackServiceInquiry = useCallback((serviceSlug: string, serviceName: string) => {
    trackConversion({
      name: 'service_inquiry',
      value: 1,
      metadata: {
        serviceSlug,
        serviceName
      }
    });
  }, [trackConversion]);

  const trackNetworkSolutionInquiry = useCallback((solutionSlug: string, solutionName: string) => {
    trackConversion({
      name: 'network_solution_inquiry',
      value: 1,
      metadata: {
        solutionSlug,
        solutionName
      }
    });
  }, [trackConversion]);

  const trackDownload = useCallback((fileName: string, fileType: string) => {
    trackConversion({
      name: 'file_download',
      value: 1,
      metadata: {
        fileName,
        fileType
      }
    });
  }, [trackConversion]);

  const trackNewsletterSignup = useCallback((email: string) => {
    trackConversion({
      name: 'newsletter_signup',
      value: 1,
      metadata: {
        hasEmail: !!email
      }
    });
  }, [trackConversion]);

  return {
    trackConversion,
    trackConversionStep,
    trackContactFormSubmission,
    trackServiceInquiry,
    trackNetworkSolutionInquiry,
    trackDownload,
    trackNewsletterSignup
  };
};

// A/B Testing Hook
export const useABTesting = () => {
  const { trackContent } = useAnalyticsContext();
  const location = useLocation();

  const trackABTest = useCallback((testName: string, variant: string, metadata?: Record<string, any>) => {
    trackContent(`ab-test-${testName}`, 'ab_test', 'view', {
      testName,
      variant,
      path: location.pathname,
      timestamp: Date.now(),
      ...metadata
    });
  }, [trackContent, location.pathname]);

  const trackABTestConversion = useCallback((testName: string, variant: string, conversionType: string, metadata?: Record<string, any>) => {
    trackContent(`ab-test-conversion-${testName}`, 'ab_test', 'conversion', {
      testName,
      variant,
      conversionType,
      path: location.pathname,
      timestamp: Date.now(),
      ...metadata
    });
  }, [trackContent, location.pathname]);

  // Simple A/B test variant selector
  const getABTestVariant = useCallback((testName: string, variants: string[]): string => {
    // Use a simple hash of the test name and session to determine variant
    const sessionId = sessionStorage.getItem('analytics_session_id') || 'default';
    const hash = hashString(testName + sessionId);
    const variantIndex = hash % variants.length;
    const selectedVariant = variants[variantIndex];
    
    // Track the variant view
    trackABTest(testName, selectedVariant);
    
    return selectedVariant;
  }, [trackABTest]);

  return {
    trackABTest,
    trackABTestConversion,
    getABTestVariant
  };
};

// Custom Event Tracking Hook
export const useCustomEventTracking = () => {
  const { trackContent } = useAnalyticsContext();
  const location = useLocation();

  const trackCustomEvent = useCallback((eventName: string, eventData?: Record<string, any>) => {
    trackContent(`custom-${eventName}`, 'custom_event', 'trigger', {
      eventName,
      path: location.pathname,
      timestamp: Date.now(),
      ...eventData
    });
  }, [trackContent, location.pathname]);

  // Business-specific event tracking
  const trackBusinessEvent = useCallback((eventType: string, eventData: Record<string, any>) => {
    trackContent(`business-${eventType}`, 'business_event', eventType, {
      path: location.pathname,
      timestamp: Date.now(),
      ...eventData
    });
  }, [trackContent, location.pathname]);

  // Predefined business events
  const trackQuoteRequest = useCallback((serviceType: string, estimatedValue?: number) => {
    trackBusinessEvent('quote_request', {
      serviceType,
      estimatedValue
    });
  }, [trackBusinessEvent]);

  const trackConsultationRequest = useCallback((consultationType: string, urgency?: string) => {
    trackBusinessEvent('consultation_request', {
      consultationType,
      urgency
    });
  }, [trackBusinessEvent]);

  const trackPartnershipInquiry = useCallback((partnerType: string, companySize?: string) => {
    trackBusinessEvent('partnership_inquiry', {
      partnerType,
      companySize
    });
  }, [trackBusinessEvent]);

  return {
    trackCustomEvent,
    trackBusinessEvent,
    trackQuoteRequest,
    trackConsultationRequest,
    trackPartnershipInquiry
  };
};

// Utility function to hash strings for A/B testing
function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

// Goal Tracking Hook
export const useGoalTracking = () => {
  const { trackContent } = useAnalyticsContext();
  const location = useLocation();

  const trackGoal = useCallback((goalName: string, value?: number, metadata?: Record<string, any>) => {
    trackContent(`goal-${goalName}`, 'goal', 'complete', {
      goalName,
      value,
      path: location.pathname,
      timestamp: Date.now(),
      ...metadata
    });
  }, [trackContent, location.pathname]);

  // Predefined goals
  const trackLeadGeneration = useCallback((leadSource: string, leadQuality?: string) => {
    trackGoal('lead_generation', 1, {
      leadSource,
      leadQuality
    });
  }, [trackGoal]);

  const trackEngagement = useCallback((engagementType: string, duration?: number) => {
    trackGoal('engagement', duration, {
      engagementType
    });
  }, [trackGoal]);

  const trackRetention = useCallback((userType: string, sessionCount?: number) => {
    trackGoal('retention', sessionCount, {
      userType
    });
  }, [trackGoal]);

  return {
    trackGoal,
    trackLeadGeneration,
    trackEngagement,
    trackRetention
  };
};
