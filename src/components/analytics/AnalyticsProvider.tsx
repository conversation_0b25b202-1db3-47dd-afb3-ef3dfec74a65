import { createContext, useContext, useEffect, ReactNode } from "react";
import { useLocation } from "react-router-dom";
import { useAnalyticsTracking } from "@/hooks/useAnalytics";

interface AnalyticsContextType {
  trackPage: (path: string, title?: string) => void;
  trackContent: (contentId: string, contentType: string, action: string, metadata?: any) => void;
  trackForm: (formType: string, formId: string, success: boolean, metadata?: any) => void;
  sessionId: string;
}

const AnalyticsContext = createContext<AnalyticsContextType | null>(null);

interface AnalyticsProviderProps {
  children: ReactNode;
  enabled?: boolean;
}

export const AnalyticsProvider = ({ children, enabled = true }: AnalyticsProviderProps) => {
  const location = useLocation();
  const { trackPage, trackContent, trackForm, sessionId } = useAnalyticsTracking();

  // Track page views automatically
  useEffect(() => {
    if (enabled) {
      trackPage(location.pathname, document.title);
    }
  }, [location.pathname, trackPage, enabled]);

  const contextValue: AnalyticsContextType = {
    trackPage,
    trackContent,
    trackForm,
    sessionId,
  };

  return (
    <AnalyticsContext.Provider value={contextValue}>
      {children}
    </AnalyticsContext.Provider>
  );
};

export const useAnalyticsContext = () => {
  const context = useContext(AnalyticsContext);
  if (!context) {
    throw new Error("useAnalyticsContext must be used within an AnalyticsProvider");
  }
  return context;
};

// HOC for tracking content interactions
export const withContentTracking = <P extends object>(
  Component: React.ComponentType<P>,
  contentId: string,
  contentType: string
) => {
  return (props: P) => {
    const { trackContent } = useAnalyticsContext();

    useEffect(() => {
      trackContent(contentId, contentType, "view");
    }, [trackContent]);

    return <Component {...props} />;
  };
};

// Hook for tracking form submissions
export const useFormTracking = (formType: string, formId: string) => {
  const { trackForm } = useAnalyticsContext();

  const trackSubmission = (success: boolean, metadata?: any) => {
    trackForm(formType, formId, success, metadata);
  };

  return { trackSubmission };
};

// Component for tracking specific events
export const AnalyticsEvent = ({ 
  event, 
  children 
}: { 
  event: () => void; 
  children: ReactNode;
}) => {
  useEffect(() => {
    event();
  }, [event]);

  return <>{children}</>;
};

// Hook for tracking button clicks and interactions
export const useInteractionTracking = () => {
  const { trackContent } = useAnalyticsContext();

  const trackClick = (elementId: string, elementType: string = "button", metadata?: any) => {
    trackContent(elementId, elementType, "click", metadata);
  };

  const trackHover = (elementId: string, elementType: string = "element", metadata?: any) => {
    trackContent(elementId, elementType, "hover", metadata);
  };

  const trackScroll = (elementId: string, scrollPercentage: number) => {
    trackContent(elementId, "scroll", "scroll", { scrollPercentage });
  };

  return {
    trackClick,
    trackHover,
    trackScroll,
  };
};

// Component for tracking scroll depth
export const ScrollTracker = ({ 
  elementId, 
  thresholds = [25, 50, 75, 100] 
}: {
  elementId: string;
  thresholds?: number[];
}) => {
  const { trackContent } = useAnalyticsContext();

  useEffect(() => {
    const trackedThresholds = new Set<number>();

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercentage = (scrollTop / scrollHeight) * 100;

      thresholds.forEach(threshold => {
        if (scrollPercentage >= threshold && !trackedThresholds.has(threshold)) {
          trackedThresholds.add(threshold);
          trackContent(elementId, "page", "scroll", { 
            threshold, 
            scrollPercentage: Math.round(scrollPercentage) 
          });
        }
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [elementId, thresholds, trackContent]);

  return null;
};

// Component for tracking time spent on page
export const TimeTracker = ({ 
  elementId,
  intervals = [30, 60, 120, 300] // seconds
}: {
  elementId: string;
  intervals?: number[];
}) => {
  const { trackContent } = useAnalyticsContext();

  useEffect(() => {
    const startTime = Date.now();
    const trackedIntervals = new Set<number>();

    const checkTimeSpent = () => {
      const timeSpent = Math.floor((Date.now() - startTime) / 1000);
      
      intervals.forEach(interval => {
        if (timeSpent >= interval && !trackedIntervals.has(interval)) {
          trackedIntervals.add(interval);
          trackContent(elementId, "page", "time_spent", { 
            interval, 
            timeSpent 
          });
        }
      });
    };

    const intervalId = setInterval(checkTimeSpent, 5000); // Check every 5 seconds

    return () => {
      clearInterval(intervalId);
      // Track final time spent
      const finalTimeSpent = Math.floor((Date.now() - startTime) / 1000);
      trackContent(elementId, "page", "session_end", { 
        timeSpent: finalTimeSpent 
      });
    };
  }, [elementId, intervals, trackContent]);

  return null;
};

// Component for tracking visibility
export const VisibilityTracker = ({ 
  elementId,
  threshold = 0.5 
}: {
  elementId: string;
  threshold?: number;
}) => {
  const { trackContent } = useAnalyticsContext();

  useEffect(() => {
    let hasBeenVisible = false;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && entry.intersectionRatio >= threshold && !hasBeenVisible) {
            hasBeenVisible = true;
            trackContent(elementId, "element", "visible", { 
              threshold,
              intersectionRatio: entry.intersectionRatio 
            });
          }
        });
      },
      { threshold }
    );

    // Observe the document body as a proxy for the page
    observer.observe(document.body);

    return () => observer.disconnect();
  }, [elementId, threshold, trackContent]);

  return null;
};

// Higher-order component for automatic analytics tracking
export const withAnalytics = <P extends object>(
  Component: React.ComponentType<P>,
  config: {
    contentId: string;
    contentType: string;
    trackView?: boolean;
    trackTime?: boolean;
    trackScroll?: boolean;
    trackVisibility?: boolean;
  }
) => {
  return (props: P) => {
    const { trackContent } = useAnalyticsContext();

    useEffect(() => {
      if (config.trackView) {
        trackContent(config.contentId, config.contentType, "view");
      }
    }, [trackContent]);

    return (
      <>
        <Component {...props} />
        {config.trackTime && <TimeTracker elementId={config.contentId} />}
        {config.trackScroll && <ScrollTracker elementId={config.contentId} />}
        {config.trackVisibility && <VisibilityTracker elementId={config.contentId} />}
      </>
    );
  };
};

// Utility function to create analytics-enabled components
export const createTrackedComponent = <P extends object>(
  Component: React.ComponentType<P>,
  defaultConfig: {
    contentType: string;
    trackView?: boolean;
    trackTime?: boolean;
    trackScroll?: boolean;
    trackVisibility?: boolean;
  }
) => {
  return (props: P & { analyticsId?: string; analyticsConfig?: Partial<typeof defaultConfig> }) => {
    const { analyticsId, analyticsConfig, ...componentProps } = props;
    const config = { ...defaultConfig, ...analyticsConfig };
    
    if (!analyticsId) {
      return <Component {...(componentProps as P)} />;
    }

    const TrackedComponent = withAnalytics(Component, {
      contentId: analyticsId,
      ...config,
    });

    return <TrackedComponent {...(componentProps as P)} />;
  };
};
