import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Plus, Edit, Trash2 } from "lucide-react";
import { useState } from "react";

interface DynamicTimelineProps {
  language?: string;
  className?: string;
  title?: string;
  subtitle?: string;
  showTitle?: boolean;
  editable?: boolean;
}

export const DynamicTimeline = ({
  language = "en",
  className = "",
  title = "Our Journey",
  subtitle = "Over 15 years of innovation and growth in technology solutions.",
  showTitle = true,
  editable = false
}: DynamicTimelineProps) => {
  const { canEditContent } = useAuth();
  const [isAdding, setIsAdding] = useState(false);

  // Get all timeline items
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for timeline items and sort by order
  const timelineItems = allContent?.filter(item => 
    item.contentType?.name === "timeline_item"
  ).sort((a, b) => (a.data.order || 0) - (b.data.order || 0)) || [];

  const isLoading = allContent === undefined;

  if (isLoading) {
    return (
      <section className={`py-20 bg-gray-50 ${className}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {showTitle && (
            <div className="text-center mb-16">
              <Skeleton className="h-10 w-64 mx-auto mb-4" />
              <Skeleton className="h-6 w-96 mx-auto" />
            </div>
          )}
          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-blue-200 hidden md:block"></div>
            <div className="space-y-12">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="flex items-center md:flex-row flex-col">
                  <div className="w-full md:w-1/2 md:pr-8">
                    <Card className="bg-white shadow-lg">
                      <CardContent className="p-6">
                        <Skeleton className="h-8 w-16 mb-2" />
                        <Skeleton className="h-6 w-32 mb-2" />
                        <Skeleton className="h-4 w-full mb-2" />
                        <Skeleton className="h-4 w-3/4" />
                      </CardContent>
                    </Card>
                  </div>
                  <div className="hidden md:block w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg z-10"></div>
                  <div className="w-full md:w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={`py-20 bg-gray-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {showTitle && (
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-4 mb-6">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                {title}
              </h2>
              {editable && canEditContent && (
                <Button
                  onClick={() => setIsAdding(true)}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Timeline Item
                </Button>
              )}
            </div>
            <p className="text-xl text-gray-600">
              {subtitle}
            </p>
          </div>
        )}

        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-blue-200 hidden md:block"></div>

          <div className="space-y-12">
            {timelineItems.map((item, index) => (
              <div key={item._id} className={`flex items-center ${
                index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
              } flex-col md:flex-row group`}>
                <div className={`w-full md:w-1/2 ${
                  index % 2 === 0 ? 'md:pr-8 md:text-right' : 'md:pl-8'
                }`}>
                  <Card className="bg-white shadow-lg hover:shadow-xl transition-shadow relative">
                    {editable && canEditContent && (
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-8 w-8 p-0"
                            onClick={() => {/* TODO: Open edit modal */}}
                          >
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            onClick={() => {/* TODO: Delete item */}}
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    )}
                    <CardContent className="p-6">
                      <div className="text-2xl font-bold text-blue-600 mb-2">
                        {item.data.year}
                      </div>
                      <h3 className="text-lg font-bold text-gray-900 mb-2">
                        {item.data.title}
                      </h3>
                      <div 
                        className="text-gray-600"
                        dangerouslySetInnerHTML={{ __html: item.data.description }}
                      />
                    </CardContent>
                  </Card>
                </div>
                
                {/* Timeline dot */}
                <div className="hidden md:block w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg z-10"></div>
                
                <div className="w-full md:w-1/2"></div>
              </div>
            ))}
          </div>
        </div>

        {timelineItems.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">No timeline items found.</p>
            {editable && canEditContent && (
              <Button
                onClick={() => setIsAdding(true)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add First Timeline Item
              </Button>
            )}
          </div>
        )}
      </div>
    </section>
  );
};
