
import { ArrowRight, Play } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { useTranslation } from '@/contexts/I18nContext';
import { useAnalyticsContext } from '@/components/analytics/AnalyticsProvider';

const Hero = () => {
  const { t } = useTranslation();
  const { trackContent } = useAnalyticsContext();

  return (
    <section className="relative bg-gradient-to-br from-blue-50 via-white to-blue-50 py-20 lg:py-32 overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent rounded-full blur-3xl animate-pulse-slow animation-delay-1000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="animate-slide-up">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6 animate-fade-in-up">
              <span className="bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">Advanced</span>
              <span className="text-gray-900"> &</span>
              <br />
              <span className="text-gray-900">Security Solutions</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed animate-fade-in-up animation-delay-300">
              {t('home.heroSubtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 animate-fade-in-up animation-delay-600">
              <Button
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg group transform hover:scale-105 transition-all duration-300"
                onClick={() => trackContent('hero-cta-primary', 'button', 'click', { action: 'get-started' })}
              >
                {t('home.heroButton')}
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-4 text-lg group transform hover:scale-105 transition-all duration-300"
                onClick={() => trackContent('hero-cta-secondary', 'button', 'click', { action: 'view-work' })}
              >
                <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                View Our Work
              </Button>
            </div>
          </div>

          {/* Hero Image */}
          <div className="relative animate-slide-up animation-delay-600">
            <div className="relative rounded-2xl overflow-hidden shadow-2xl transform hover:rotate-1 transition-transform duration-500">
              <img
                src="/img/img_presentation.jpg"
                alt="Professional IT team working on technology solutions"
                className="w-full h-96 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-blue-600/80 via-blue-600/20 to-transparent"></div>

              {/* Floating badges */}
              <div className="absolute top-4 right-4 bg-white/95 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg animate-float">
                <div className="text-blue-600 font-semibold text-sm">24/7 Support</div>
              </div>

              <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg animate-float animation-delay-600">
                <div className="text-blue-600 font-semibold text-sm">Secure Solutions</div>
              </div>

              {/* Center experience badge */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="bg-white/95 backdrop-blur-sm rounded-full w-24 h-24 flex flex-col items-center justify-center shadow-lg animate-pulse-gentle">
                  <div className="text-2xl font-bold text-blue-600 mb-1">15+</div>
                  <div className="text-xs text-gray-600 text-center leading-tight">Years<br />Experience</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
