import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import * as Icons from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Plus, Edit, Trash2 } from "lucide-react";
import { useState } from "react";

interface DynamicValueCardsProps {
  language?: string;
  className?: string;
  title?: string;
  subtitle?: string;
  showTitle?: boolean;
  editable?: boolean;
}

export const DynamicValueCards = ({
  language = "en",
  className = "",
  title = "Our Values",
  subtitle = "The principles that guide everything we do and every solution we deliver.",
  showTitle = true,
  editable = false
}: DynamicValueCardsProps) => {
  const { canEditContent } = useAuth();
  const [isAdding, setIsAdding] = useState(false);

  // Get all value cards
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for value cards and sort by order
  const valueCards = allContent?.filter(item => 
    item.contentType?.name === "value_card"
  ).sort((a, b) => (a.data.order || 0) - (b.data.order || 0)) || [];

  const isLoading = allContent === undefined;

  const getIconComponent = (iconName: string) => {
    if (!iconName) return Icons.Award;
    const IconComponent = (Icons as any)[iconName];
    return IconComponent || Icons.Award;
  };

  if (isLoading) {
    return (
      <section className={`py-20 bg-white ${className}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {showTitle && (
            <div className="text-center mb-16">
              <Skeleton className="h-10 w-64 mx-auto mb-4" />
              <Skeleton className="h-6 w-96 mx-auto" />
            </div>
          )}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[...Array(4)].map((_, index) => (
              <Card key={index} className="text-center border-0 shadow-lg">
                <CardContent className="p-8">
                  <Skeleton className="w-16 h-16 rounded-full mx-auto mb-6" />
                  <Skeleton className="h-6 w-32 mx-auto mb-4" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4 mx-auto" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className={`py-20 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {showTitle && (
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-4 mb-6">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                {title}
              </h2>
              {editable && canEditContent && (
                <Button
                  onClick={() => setIsAdding(true)}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Value
                </Button>
              )}
            </div>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {subtitle}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {valueCards.map((card, index) => {
            const IconComponent = getIconComponent(card.data.icon);
            return (
              <Card 
                key={card._id} 
                className="text-center border-0 shadow-lg hover:shadow-xl transition-shadow group relative"
              >
                {editable && canEditContent && (
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 w-8 p-0"
                        onClick={() => {/* TODO: Open edit modal */}}
                      >
                        <Edit className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                        onClick={() => {/* TODO: Delete card */}}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                )}
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <IconComponent className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    {card.data.title}
                  </h3>
                  <div 
                    className="text-gray-600"
                    dangerouslySetInnerHTML={{ __html: card.data.description }}
                  />
                </CardContent>
              </Card>
            );
          })}
        </div>

        {valueCards.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">No value cards found.</p>
            {editable && canEditContent && (
              <Button
                onClick={() => setIsAdding(true)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add First Value Card
              </Button>
            )}
          </div>
        )}
      </div>
    </section>
  );
};
