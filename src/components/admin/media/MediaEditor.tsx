import { useState } from "react";
import { useMediaMutations } from "@/hooks/useMedia";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { 
  Save, 
  X, 
  Plus,
  Tag
} from "lucide-react";
import { toast } from "sonner";

interface MediaEditorProps {
  media: any;
  onClose: () => void;
  onSave: () => void;
}

export const MediaEditor = ({ media, onClose, onSave }: MediaEditorProps) => {
  const { updateMedia } = useMediaMutations();
  const [formData, setFormData] = useState({
    alt: media.alt || "",
    tags: media.tags || [],
  });
  const [newTag, setNewTag] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await updateMedia({
        id: media._id,
        alt: formData.alt || undefined,
        tags: formData.tags,
      });
      
      toast.success("Media updated successfully");
      onSave();
    } catch (error) {
      toast.error("Failed to update media");
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Tag className="w-5 h-5" />
            Edit Media: {media.originalName}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Media Preview */}
          <div className="flex justify-center bg-gray-50 rounded-lg p-4">
            {media.mimeType.startsWith("image/") ? (
              <img
                src={media.url}
                alt={media.alt || media.originalName}
                className="max-h-48 object-contain rounded"
              />
            ) : (
              <div className="flex flex-col items-center justify-center p-8">
                <div className="text-4xl mb-2">
                  {media.mimeType.startsWith("video/") ? "🎥" : "📁"}
                </div>
                <p className="text-sm text-gray-600">{media.originalName}</p>
              </div>
            )}
          </div>

          {/* Alt Text Field */}
          <div className="space-y-2">
            <Label htmlFor="alt-text">
              Alt Text
              {media.mimeType.startsWith("image/") && (
                <span className="text-red-500 ml-1">*</span>
              )}
            </Label>
            <Textarea
              id="alt-text"
              value={formData.alt}
              onChange={(e) => setFormData(prev => ({ ...prev, alt: e.target.value }))}
              placeholder="Describe this media file for accessibility..."
              rows={3}
              disabled={isSaving}
            />
            <p className="text-xs text-gray-500">
              Provide a clear description of the media content for screen readers and SEO.
            </p>
          </div>

          {/* Tags Section */}
          <div className="space-y-3">
            <Label>Tags</Label>
            
            {/* Existing Tags */}
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <Badge 
                    key={index} 
                    variant="secondary" 
                    className="flex items-center gap-1 pr-1"
                  >
                    {tag}
                    <button
                      onClick={() => handleRemoveTag(tag)}
                      disabled={isSaving}
                      className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}

            {/* Add New Tag */}
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Add a tag..."
                disabled={isSaving}
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddTag}
                disabled={!newTag.trim() || isSaving}
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            
            <p className="text-xs text-gray-500">
              Tags help organize and find your media files. Press Enter or click + to add a tag.
            </p>
          </div>

          {/* File Information (Read-only) */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
            <h4 className="font-medium text-gray-900">File Information</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-600">File Name:</span>
                <p className="text-gray-900">{media.filename}</p>
              </div>
              <div>
                <span className="font-medium text-gray-600">File Size:</span>
                <p className="text-gray-900">
                  {(media.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
              <div>
                <span className="font-medium text-gray-600">Type:</span>
                <p className="text-gray-900">{media.mimeType}</p>
              </div>
              <div>
                <span className="font-medium text-gray-600">Uploaded:</span>
                <p className="text-gray-900">
                  {new Date(media.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSaving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
