import { useState, useCallback } from "react";
import { useMediaLibrary, useMediaSearch, formatFileSize, getFileTypeIcon, isImageFile } from "@/hooks/useMedia";
import { useAnalyticsContext } from "@/components/analytics/AnalyticsProvider";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Upload, 
  Search, 
  Filter, 
  Grid, 
  List, 
  Trash2,
  Edit,
  Download,
  Eye,
  Tag,
  MoreHorizontal,
  X
} from "lucide-react";
import { toast } from "sonner";
import { MediaUploader } from "./MediaUploader";
import { MediaViewer } from "./MediaViewer";
import { MediaEditor } from "./MediaEditor";

export const MediaLibrary = () => {
  const { media, tags, isLoading, deleteFile, bulkTag } = useMediaLibrary();
  const { trackContent } = useAnalyticsContext();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedType, setSelectedType] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedMedia, setSelectedMedia] = useState<string[]>([]);
  const [isUploadOpen, setIsUploadOpen] = useState(false);
  const [viewingMedia, setViewingMedia] = useState<any>(null);
  const [editingMedia, setEditingMedia] = useState<any>(null);

  const { results: searchResults } = useMediaSearch(searchQuery);

  // Filter media based on search and filters
  const filteredMedia = searchQuery ? searchResults : media?.filter(item => {
    if (selectedType !== "all" && !item.mimeType.startsWith(selectedType)) {
      return false;
    }
    if (selectedTags.length > 0 && !selectedTags.some(tag => item.tags.includes(tag))) {
      return false;
    }
    return true;
  });

  const handleSelectMedia = (mediaId: string) => {
    setSelectedMedia(prev => 
      prev.includes(mediaId) 
        ? prev.filter(id => id !== mediaId)
        : [...prev, mediaId]
    );
  };

  const handleSelectAll = () => {
    if (selectedMedia.length === filteredMedia?.length) {
      setSelectedMedia([]);
    } else {
      setSelectedMedia(filteredMedia?.map(item => item._id) || []);
    }
  };

  const handleBulkDelete = async () => {
    if (!confirm(`Delete ${selectedMedia.length} selected files?`)) return;

    try {
      // Track bulk delete attempt
      trackContent('media-library-bulk-delete', 'media', 'bulk_delete_start', {
        fileCount: selectedMedia.length
      });

      for (const mediaId of selectedMedia) {
        await deleteFile(mediaId as any);
      }
      setSelectedMedia([]);

      // Track successful bulk delete
      trackContent('media-library-bulk-delete', 'media', 'bulk_delete_success', {
        fileCount: selectedMedia.length
      });

      toast.success(`Deleted ${selectedMedia.length} files`);
    } catch (error) {
      // Track failed bulk delete
      trackContent('media-library-bulk-delete', 'media', 'bulk_delete_failed', {
        fileCount: selectedMedia.length,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      toast.error("Failed to delete files");
    }
  };

  const handleBulkTag = async (tags: string[], operation: "add" | "remove") => {
    try {
      await bulkTag(selectedMedia as any, tags, operation);

      // Track bulk tagging
      trackContent('media-library-bulk-tag', 'media', 'bulk_tag', {
        operation,
        tagCount: tags.length,
        fileCount: selectedMedia.length
      });

      setSelectedMedia([]);
      toast.success(`${operation === "add" ? "Added" : "Removed"} tags from ${selectedMedia.length} files`);
    } catch (error) {
      toast.error("Failed to update tags");
    }
  };

  // Analytics-tracked media viewing
  const handleViewMedia = (media: any) => {
    trackContent(`media-view-${media._id}`, 'media', 'view', {
      fileName: media.originalName,
      fileType: media.mimeType,
      fileSize: media.size
    });
    setViewingMedia(media);
  };

  // Analytics-tracked media editing
  const handleEditMedia = (media: any) => {
    trackContent(`media-edit-${media._id}`, 'media', 'edit_start', {
      fileName: media.originalName,
      fileType: media.mimeType,
      fileSize: media.size
    });
    setEditingMedia(media);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Media Library</h2>
          <p className="text-gray-600">
            {filteredMedia?.length || 0} files
            {selectedMedia.length > 0 && ` • ${selectedMedia.length} selected`}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          {selectedMedia.length > 0 && (
            <>
              <Button variant="outline" size="sm" onClick={handleBulkDelete}>
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Selected
              </Button>
              <BulkTagDialog
                onApply={handleBulkTag}
                availableTags={tags || []}
              />
            </>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
          >
            {viewMode === "grid" ? <List className="w-4 h-4" /> : <Grid className="w-4 h-4" />}
          </Button>
          
          <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
            <DialogTrigger asChild>
              <Button>
                <Upload className="w-4 h-4 mr-2" />
                Upload Files
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Upload Media Files</DialogTitle>
              </DialogHeader>
              <MediaUploader onUploadComplete={() => setIsUploadOpen(false)} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search files..."
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  if (e.target.value) {
                    trackContent('media-library-search', 'search', 'query', {
                      query: e.target.value.substring(0, 50),
                      resultCount: searchResults?.length || 0
                    });
                  }
                }}
                className="pl-10"
              />
            </div>

            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="File Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="image">Images</SelectItem>
                <SelectItem value="video">Videos</SelectItem>
                <SelectItem value="application">Documents</SelectItem>
              </SelectContent>
            </Select>

            <TagFilter
              availableTags={tags || []}
              selectedTags={selectedTags}
              onTagsChange={setSelectedTags}
            />
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedMedia.length > 0 && (
        <div className="flex items-center justify-between bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={selectedMedia.length === filteredMedia?.length}
              onCheckedChange={handleSelectAll}
            />
            <span className="text-sm font-medium">
              {selectedMedia.length} of {filteredMedia?.length} selected
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => setSelectedMedia([])}>
              Clear Selection
            </Button>
          </div>
        </div>
      )}

      {/* Media Grid/List */}
      {viewMode === "grid" ? (
        <MediaGrid
          media={filteredMedia || []}
          selectedMedia={selectedMedia}
          onSelectMedia={handleSelectMedia}
          onViewMedia={handleViewMedia}
          onEditMedia={handleEditMedia}
        />
      ) : (
        <MediaList
          media={filteredMedia || []}
          selectedMedia={selectedMedia}
          onSelectMedia={handleSelectMedia}
          onViewMedia={handleViewMedia}
          onEditMedia={handleEditMedia}
        />
      )}

      {/* Media Viewer Dialog */}
      {viewingMedia && (
        <MediaViewer
          media={viewingMedia}
          onClose={() => setViewingMedia(null)}
          onEdit={() => {
            setEditingMedia(viewingMedia);
            setViewingMedia(null);
          }}
        />
      )}

      {/* Media Editor Dialog */}
      {editingMedia && (
        <MediaEditor
          media={editingMedia}
          onClose={() => setEditingMedia(null)}
          onSave={() => setEditingMedia(null)}
        />
      )}
    </div>
  );
};

// Media Grid Component
const MediaGrid = ({ media, selectedMedia, onSelectMedia, onViewMedia, onEditMedia }: {
  media: any[];
  selectedMedia: string[];
  onSelectMedia: (id: string) => void;
  onViewMedia: (media: any) => void;
  onEditMedia: (media: any) => void;
}) => {
  if (media.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">No media files found</div>
        <Button variant="outline">
          <Upload className="w-4 h-4 mr-2" />
          Upload Your First File
        </Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
      {media.map((item) => (
        <MediaCard
          key={item._id}
          media={item}
          isSelected={selectedMedia.includes(item._id)}
          onSelect={() => onSelectMedia(item._id)}
          onView={() => onViewMedia(item)}
          onEdit={() => onEditMedia(item)}
        />
      ))}
    </div>
  );
};

// Media Card Component
const MediaCard = ({ media, isSelected, onSelect, onView, onEdit }: {
  media: any;
  isSelected: boolean;
  onSelect: () => void;
  onView: () => void;
  onEdit: () => void;
}) => {
  return (
    <Card className={`group cursor-pointer transition-all ${isSelected ? 'ring-2 ring-blue-500' : ''}`}>
      <CardContent className="p-2">
        <div className="relative aspect-square mb-2">
          <Checkbox
            checked={isSelected}
            onCheckedChange={onSelect}
            className="absolute top-2 left-2 z-10 bg-white"
          />
          
          {isImageFile(media.mimeType) ? (
            <img
              src={media.url}
              alt={media.alt || media.originalName}
              className="w-full h-full object-cover rounded"
              onClick={onView}
            />
          ) : (
            <div 
              className="w-full h-full bg-gray-100 rounded flex items-center justify-center text-4xl"
              onClick={onView}
            >
              {getFileTypeIcon(media.mimeType)}
            </div>
          )}
          
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex space-x-2">
              <Button size="sm" variant="secondary" onClick={onView}>
                <Eye className="w-4 h-4" />
              </Button>
              <Button size="sm" variant="secondary" onClick={onEdit}>
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
        
        <div className="space-y-1">
          <p className="text-xs font-medium truncate" title={media.originalName}>
            {media.originalName}
          </p>
          <p className="text-xs text-gray-500">
            {formatFileSize(media.size)}
          </p>
          {media.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {media.tags.slice(0, 2).map((tag: string) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {media.tags.length > 2 && (
                <Badge variant="outline" className="text-xs">
                  +{media.tags.length - 2}
                </Badge>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Media List Component (simplified for now)
const MediaList = ({ media, selectedMedia, onSelectMedia, onViewMedia, onEditMedia }: {
  media: any[];
  selectedMedia: string[];
  onSelectMedia: (id: string) => void;
  onViewMedia: (media: any) => void;
  onEditMedia: (media: any) => void;
}) => {
  return (
    <div className="space-y-2">
      {media.map((item) => (
        <Card key={item._id} className="p-4">
          <div className="flex items-center space-x-4">
            <Checkbox
              checked={selectedMedia.includes(item._id)}
              onCheckedChange={() => onSelectMedia(item._id)}
            />
            <div className="flex-1">
              <p className="font-medium">{item.originalName}</p>
              <p className="text-sm text-gray-500">
                {formatFileSize(item.size)} • {item.mimeType}
              </p>
            </div>
            <div className="flex space-x-2">
              <Button size="sm" variant="outline" onClick={() => onViewMedia(item)}>
                <Eye className="w-4 h-4" />
              </Button>
              <Button size="sm" variant="outline" onClick={() => onEditMedia(item)}>
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

// Tag Filter Component
const TagFilter = ({ availableTags, selectedTags, onTagsChange }: {
  availableTags: string[];
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
}) => {
  return (
    <div className="space-y-2">
      <div className="flex flex-wrap gap-1">
        {selectedTags.map(tag => (
          <Badge key={tag} variant="default" className="flex items-center gap-1">
            {tag}
            <X 
              className="w-3 h-3 cursor-pointer" 
              onClick={() => onTagsChange(selectedTags.filter(t => t !== tag))}
            />
          </Badge>
        ))}
      </div>
      <Select onValueChange={(tag) => {
        if (!selectedTags.includes(tag)) {
          onTagsChange([...selectedTags, tag]);
        }
      }}>
        <SelectTrigger>
          <SelectValue placeholder="Filter by tags" />
        </SelectTrigger>
        <SelectContent>
          {availableTags.filter(tag => !selectedTags.includes(tag)).map(tag => (
            <SelectItem key={tag} value={tag}>
              {tag}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

// Bulk Tag Dialog Component
const BulkTagDialog = ({ onApply, availableTags }: {
  onApply: (tags: string[], operation: "add" | "remove") => void;
  availableTags: string[];
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [operation, setOperation] = useState<"add" | "remove">("add");

  const handleApply = () => {
    onApply(selectedTags, operation);
    setIsOpen(false);
    setSelectedTags([]);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Tag className="w-4 h-4 mr-2" />
          Bulk Tag
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Bulk Tag Operation</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <Select value={operation} onValueChange={(value: "add" | "remove") => setOperation(value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="add">Add Tags</SelectItem>
              <SelectItem value="remove">Remove Tags</SelectItem>
            </SelectContent>
          </Select>

          <div className="space-y-2">
            <label className="text-sm font-medium">Select Tags:</label>
            <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
              {availableTags.map(tag => (
                <label key={tag} className="flex items-center space-x-2">
                  <Checkbox
                    checked={selectedTags.includes(tag)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedTags([...selectedTags, tag]);
                      } else {
                        setSelectedTags(selectedTags.filter(t => t !== tag));
                      }
                    }}
                  />
                  <span className="text-sm">{tag}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleApply} disabled={selectedTags.length === 0}>
              Apply
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
