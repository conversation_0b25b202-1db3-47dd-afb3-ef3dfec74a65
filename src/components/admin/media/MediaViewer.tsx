import { useState } from "react";
import { formatFileSize, getFileTypeIcon, isImageFile, isVideoFile } from "@/hooks/useMedia";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  Download, 
  Edit, 
  X, 
  Calendar,
  User,
  Tag,
  FileText
} from "lucide-react";

interface MediaViewerProps {
  media: any;
  onClose: () => void;
  onEdit: () => void;
}

export const MediaViewer = ({ media, onClose, onEdit }: MediaViewerProps) => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = media.url;
    link.download = media.originalName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderMediaPreview = () => {
    if (isImageFile(media.mimeType)) {
      return (
        <img
          src={media.url}
          alt={media.alt || media.originalName}
          className={`max-w-full max-h-full object-contain cursor-pointer ${
            isFullscreen ? "w-screen h-screen" : "max-h-96"
          }`}
          onClick={() => setIsFullscreen(!isFullscreen)}
        />
      );
    }

    if (isVideoFile(media.mimeType)) {
      return (
        <video
          src={media.url}
          controls
          className="max-w-full max-h-96"
        >
          Your browser does not support the video tag.
        </video>
      );
    }

    // For other file types, show icon and info
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-gray-50 rounded-lg">
        <div className="text-6xl mb-4">
          {getFileTypeIcon(media.mimeType)}
        </div>
        <p className="text-lg font-medium text-gray-900 mb-2">
          {media.originalName}
        </p>
        <p className="text-sm text-gray-500">
          {media.mimeType}
        </p>
      </div>
    );
  };

  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center">
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-4 right-4 text-white hover:bg-white hover:bg-opacity-20"
          onClick={() => setIsFullscreen(false)}
        >
          <X className="w-6 h-6" />
        </Button>
        {renderMediaPreview()}
      </div>
    );
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>{media.originalName}</span>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
              <Button variant="outline" size="sm" onClick={onEdit}>
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Media Preview */}
          <div className="flex justify-center bg-gray-50 rounded-lg p-4">
            {renderMediaPreview()}
          </div>

          {/* Media Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <FileText className="w-5 h-5" />
                File Information
              </h3>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">File Name:</span>
                  <span className="text-sm text-gray-900">{media.filename}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Original Name:</span>
                  <span className="text-sm text-gray-900">{media.originalName}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">File Size:</span>
                  <span className="text-sm text-gray-900">{formatFileSize(media.size)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Type:</span>
                  <span className="text-sm text-gray-900">{media.mimeType}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Uploaded:</span>
                  <span className="text-sm text-gray-900 flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {new Date(media.createdAt).toLocaleDateString()}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Uploaded By:</span>
                  <span className="text-sm text-gray-900 flex items-center gap-1">
                    <User className="w-3 h-3" />
                    {media.uploadedBy}
                  </span>
                </div>
              </div>
            </div>

            {/* Metadata */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Tag className="w-5 h-5" />
                Metadata
              </h3>
              
              <div className="space-y-3">
                {media.alt && (
                  <div>
                    <span className="text-sm font-medium text-gray-600 block mb-1">
                      Alt Text:
                    </span>
                    <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded">
                      {media.alt}
                    </p>
                  </div>
                )}
                
                <div>
                  <span className="text-sm font-medium text-gray-600 block mb-2">
                    Tags:
                  </span>
                  {media.tags && media.tags.length > 0 ? (
                    <div className="flex flex-wrap gap-1">
                      {media.tags.map((tag: string, index: number) => (
                        <Badge key={index} variant="outline">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 italic">No tags assigned</p>
                  )}
                </div>

                {/* Image-specific metadata */}
                {isImageFile(media.mimeType) && media.variants && (
                  <div>
                    <span className="text-sm font-medium text-gray-600 block mb-2">
                      Available Variants:
                    </span>
                    <div className="space-y-1">
                      {media.variants.map((variant: any, index: number) => (
                        <div key={index} className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                          {variant.name}: {variant.width}x{variant.height || 'auto'} 
                          (Quality: {variant.quality}%)
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* URL Information */}
          <div className="space-y-2">
            <span className="text-sm font-medium text-gray-600">File URL:</span>
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={media.url}
                readOnly
                className="flex-1 text-sm bg-gray-50 border border-gray-200 rounded px-3 py-2"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigator.clipboard.writeText(media.url)}
              >
                Copy
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
