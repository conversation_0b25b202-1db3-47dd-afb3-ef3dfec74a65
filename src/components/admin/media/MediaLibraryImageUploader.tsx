import { useState, useRef } from "react";
import { useMediaLibrary, useMedia, formatFileSize, isImageFile } from "@/hooks/useMedia";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Upload, 
  Image as ImageIcon, 
  X, 
  Link, 
  ExternalLink,
  Search,
  Grid,
  Check
} from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface MediaLibraryImageUploaderProps {
  value?: any;
  onChange: (value: any) => void;
  accept?: string;
  className?: string;
  disabled?: boolean;
  error?: boolean;
}

export const MediaLibraryImageUploader = ({
  value,
  onChange,
  accept = "image/*",
  className,
  disabled = false,
  error = false
}: MediaLibraryImageUploaderProps) => {
  const { uploadFile: uploadFileToLibrary } = useMediaLibrary();
  const { media, isLoading } = useMedia();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [imageUrl, setImageUrl] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Filter media to only show images
  const imageMedia = media?.filter(item => isImageFile(item.mimeType)) || [];
  
  // Filter by search query
  const filteredMedia = searchQuery 
    ? imageMedia.filter(item => 
        item.originalName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.alt?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : imageMedia;

  // Handle file upload
  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    if (!file.type.startsWith("image/")) {
      toast.error("Please select an image file");
      return;
    }

    setIsUploading(true);
    try {
      const mediaId = await uploadFileToLibrary(file, {
        alt: file.name,
        tags: ["content-image"]
      });
      
      // Get the uploaded media item
      const uploadedMedia = media?.find(m => m._id === mediaId);
      if (uploadedMedia) {
        onChange({
          url: uploadedMedia.url,
          name: uploadedMedia.originalName,
          alt: uploadedMedia.alt,
          mediaId: uploadedMedia._id
        });
      }
      
      setIsModalOpen(false);
      toast.success("Image uploaded successfully!");
    } catch (error) {
      toast.error("Failed to upload image");
      console.error("Upload error:", error);
    } finally {
      setIsUploading(false);
    }
  };

  // Handle media selection from library
  const handleMediaSelect = (mediaItem: any) => {
    onChange({
      url: mediaItem.url,
      name: mediaItem.originalName,
      alt: mediaItem.alt,
      mediaId: mediaItem._id
    });
    setIsModalOpen(false);
  };

  // Handle URL input
  const handleUrlSubmit = () => {
    if (!imageUrl.trim()) return;

    onChange({
      url: imageUrl.trim(),
      name: "External Image",
      type: "url",
    });
    
    setImageUrl("");
    setIsModalOpen(false);
  };

  // Handle remove
  const handleRemove = () => {
    onChange(null);
  };

  return (
    <div className={cn("space-y-3", className)}>
      {value?.url ? (
        // Image Preview
        <div className="relative group">
          <div className={cn(
            "relative overflow-hidden rounded-lg border",
            error && "border-red-500"
          )}>
            <img
              src={value.url}
              alt={value.alt || value.name || "Selected image"}
              className="w-full h-48 object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setIsModalOpen(true)}
                  disabled={disabled}
                >
                  <Upload className="w-4 h-4 mr-1" />
                  Replace
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleRemove}
                  disabled={disabled}
                >
                  <X className="w-4 h-4 mr-1" />
                  Remove
                </Button>
              </div>
            </div>
          </div>
          <div className="mt-2 text-sm text-gray-600">
            {value.name} {value.mediaId && <Badge variant="secondary">From Library</Badge>}
          </div>
        </div>
      ) : (
        // Upload Area
        <div className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center",
          error && "border-red-500",
          disabled && "opacity-50 cursor-not-allowed"
        )}>
          <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-sm text-gray-600 mb-4">
            Select an image from your media library or upload a new one
          </p>
          <Button
            variant="outline"
            onClick={() => setIsModalOpen(true)}
            disabled={disabled}
          >
            <ImageIcon className="w-4 h-4 mr-2" />
            Choose Image
          </Button>
        </div>
      )}

      {/* Media Library Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Select or Upload Image</DialogTitle>
          </DialogHeader>
          
          <Tabs defaultValue="library" className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="library">Media Library</TabsTrigger>
              <TabsTrigger value="upload">Upload New</TabsTrigger>
              <TabsTrigger value="url">From URL</TabsTrigger>
            </TabsList>
            
            <TabsContent value="library" className="flex-1 overflow-hidden">
              <div className="space-y-4 h-full">
                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search images..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                {/* Media Grid */}
                <div className="grid grid-cols-4 gap-4 max-h-96 overflow-y-auto">
                  {isLoading ? (
                    <div className="col-span-4 text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="text-gray-500 mt-2">Loading images...</p>
                    </div>
                  ) : filteredMedia.length === 0 ? (
                    <div className="col-span-4 text-center py-8">
                      <ImageIcon className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                      <p className="text-gray-500">No images found</p>
                    </div>
                  ) : (
                    filteredMedia.map((item) => (
                      <Card 
                        key={item._id} 
                        className="cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() => handleMediaSelect(item)}
                      >
                        <CardContent className="p-2">
                          <div className="aspect-square relative overflow-hidden rounded">
                            <img
                              src={item.url}
                              alt={item.alt || item.originalName}
                              className="w-full h-full object-cover"
                            />
                            {value?.mediaId === item._id && (
                              <div className="absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center">
                                <Check className="w-6 h-6 text-blue-600" />
                              </div>
                            )}
                          </div>
                          <p className="text-xs text-gray-600 mt-1 truncate">
                            {item.originalName}
                          </p>
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="upload" className="flex-1">
              <div className="space-y-4">
                <div
                  onClick={() => fileInputRef.current?.click()}
                  className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors"
                >
                  <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-lg text-gray-600 mb-2">
                    Click to upload an image
                  </p>
                  <p className="text-sm text-gray-500">
                    Supports JPG, PNG, GIF, WebP
                  </p>
                </div>
                
                <input
                  ref={fileInputRef}
                  type="file"
                  accept={accept}
                  onChange={(e) => handleFileUpload(e.target.files)}
                  className="hidden"
                  disabled={isUploading}
                />
                
                {isUploading && (
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className="text-gray-600">Uploading image...</p>
                  </div>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="url" className="flex-1">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="image-url">Image URL</Label>
                  <Input
                    id="image-url"
                    type="url"
                    placeholder="https://example.com/image.jpg"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleUrlSubmit} disabled={!imageUrl.trim()}>
                    <ExternalLink className="w-4 h-4 mr-1" />
                    Add Image
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </div>
  );
};
