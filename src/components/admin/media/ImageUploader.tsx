import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Upload, Image as ImageIcon, X, Link, ExternalLink } from "lucide-react";
import { cn } from "@/lib/utils";

interface ImageUploaderProps {
  value?: any;
  onChange: (value: any) => void;
  accept?: string;
  className?: string;
}

export const ImageUploader = ({
  value,
  onChange,
  accept = "image/*",
  className,
}: ImageUploaderProps) => {
  const [isUrlModalOpen, setIsUrlModalOpen] = useState(false);
  const [imageUrl, setImageUrl] = useState("");
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    if (!file.type.startsWith("image/")) {
      alert("Please select an image file");
      return;
    }

    // Create preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      onChange({
        url: result,
        name: file.name,
        size: file.size,
        type: file.type,
        file: file,
      });
    };
    reader.readAsDataURL(file);
  };

  // Handle URL input
  const handleUrlSubmit = () => {
    if (!imageUrl.trim()) return;

    onChange({
      url: imageUrl.trim(),
      name: "External Image",
      type: "url",
    });
    
    setImageUrl("");
    setIsUrlModalOpen(false);
  };

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  // Remove image
  const handleRemove = () => {
    onChange(null);
  };

  return (
    <div className={cn("space-y-3", className)}>
      {value?.url ? (
        // Image Preview
        <div className="relative group">
          <div className="relative overflow-hidden rounded-lg border">
            <img
              src={value.url}
              alt={value.name || "Uploaded image"}
              className="w-full h-48 object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className="w-4 h-4 mr-1" />
                  Replace
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleRemove}
                >
                  <X className="w-4 h-4 mr-1" />
                  Remove
                </Button>
              </div>
            </div>
          </div>
          
          {/* Image Info */}
          <div className="text-xs text-gray-500 mt-1">
            {value.name}
            {value.size && (
              <span className="ml-2">
                ({(value.size / 1024).toFixed(1)} KB)
              </span>
            )}
          </div>
        </div>
      ) : (
        // Upload Area
        <div
          className={cn(
            "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
            isDragging
              ? "border-blue-500 bg-blue-50"
              : "border-gray-300 hover:border-gray-400"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-sm text-gray-600 mb-3">
            Drag and drop an image here, or click to select
          </p>
          
          <div className="flex justify-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="w-4 h-4 mr-1" />
              Upload File
            </Button>
            
            <Dialog open={isUrlModalOpen} onOpenChange={setIsUrlModalOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Link className="w-4 h-4 mr-1" />
                  Add URL
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Add Image URL</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="imageUrl">Image URL</Label>
                    <Input
                      id="imageUrl"
                      value={imageUrl}
                      onChange={(e) => setImageUrl(e.target.value)}
                      placeholder="https://example.com/image.jpg"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          handleUrlSubmit();
                        }
                      }}
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsUrlModalOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleUrlSubmit} disabled={!imageUrl.trim()}>
                      <ExternalLink className="w-4 h-4 mr-1" />
                      Add Image
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
      />
    </div>
  );
};
