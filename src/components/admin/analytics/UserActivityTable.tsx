import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { formatAnalyticsData } from "@/hooks/useAnalytics";
import { Users, Activity, Clock, Crown, Edit, Eye } from "lucide-react";

interface UserActivity {
  userId: string;
  name: string;
  role: string;
  interactions: number;
  lastActivity: number;
}

interface UserActivityTableProps {
  data: UserActivity[];
}

export const UserActivityTable = ({ data }: UserActivityTableProps) => {
  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            User Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            No user activity data available
          </div>
        </CardContent>
      </Card>
    );
  }

  const getRoleColor = (role: string): string => {
    const colors: Record<string, string> = {
      'super_admin': 'bg-red-100 text-red-800',
      'admin': 'bg-purple-100 text-purple-800',
      'content_editor': 'bg-blue-100 text-blue-800',
      'viewer': 'bg-gray-100 text-gray-800',
    };
    return colors[role] || colors['viewer'];
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin':
      case 'admin':
        return <Crown className="w-3 h-3" />;
      case 'content_editor':
        return <Edit className="w-3 h-3" />;
      default:
        return <Eye className="w-3 h-3" />;
    }
  };

  const getActivityLevel = (interactions: number): { level: string; color: string } => {
    if (interactions >= 50) return { level: 'Very Active', color: 'text-green-600' };
    if (interactions >= 20) return { level: 'Active', color: 'text-blue-600' };
    if (interactions >= 5) return { level: 'Moderate', color: 'text-yellow-600' };
    return { level: 'Low', color: 'text-gray-600' };
  };

  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const totalInteractions = data.reduce((sum, user) => sum + user.interactions, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="w-5 h-5" />
          User Activity
        </CardTitle>
        <p className="text-sm text-gray-600">
          Most active users by content interactions
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((user, index) => {
            const percentage = totalInteractions > 0 ? (user.interactions / totalInteractions) * 100 : 0;
            const activityLevel = getActivityLevel(user.interactions);

            return (
              <div key={user.userId} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-4 flex-1">
                  {/* Rank */}
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-sm font-semibold text-gray-600">
                    {index + 1}
                  </div>

                  {/* Avatar */}
                  <Avatar className="w-10 h-10">
                    <AvatarFallback className="bg-blue-100 text-blue-600 font-semibold">
                      {getInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>

                  {/* User Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-gray-900 truncate">
                        {user.name}
                      </h4>
                      <Badge className={getRoleColor(user.role)}>
                        <span className="flex items-center gap-1">
                          {getRoleIcon(user.role)}
                          {user.role.replace('_', ' ')}
                        </span>
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className={activityLevel.color}>
                        {activityLevel.level}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {formatAnalyticsData.formatTimeAgo(user.lastActivity)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Metrics */}
                <div className="flex items-center space-x-6">
                  {/* Interactions */}
                  <div className="text-right">
                    <div className="font-semibold text-gray-900">
                      {formatAnalyticsData.formatNumber(user.interactions)}
                    </div>
                    <div className="text-xs text-gray-500">interactions</div>
                  </div>

                  {/* Percentage */}
                  <div className="text-right min-w-[60px]">
                    <div className="font-semibold text-gray-900">
                      {percentage.toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-500">of total</div>
                  </div>

                  {/* Visual Bar */}
                  <div className="w-20">
                    <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-blue-500 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Summary */}
        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              Showing top {data.length} active users
            </span>
            <span className="text-gray-600">
              Total interactions: {formatAnalyticsData.formatNumber(totalInteractions)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Component for user activity insights
export const UserActivityInsights = ({ data }: UserActivityTableProps) => {
  if (!data || data.length === 0) return null;

  const totalUsers = data.length;
  const activeUsers = data.filter(user => user.interactions > 0).length;
  const superActiveUsers = data.filter(user => user.interactions >= 50).length;
  const recentlyActiveUsers = data.filter(user => {
    const dayAgo = Date.now() - (24 * 60 * 60 * 1000);
    return user.lastActivity > dayAgo;
  }).length;

  const roleDistribution = data.reduce((acc, user) => {
    acc[user.role] = (acc[user.role] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const avgInteractions = data.reduce((sum, user) => sum + user.interactions, 0) / totalUsers;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="w-5 h-5" />
          User Insights
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {((activeUsers / totalUsers) * 100).toFixed(0)}%
            </div>
            <div className="text-sm text-gray-600">Active Users</div>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {superActiveUsers}
            </div>
            <div className="text-sm text-gray-600">Super Active</div>
          </div>

          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {recentlyActiveUsers}
            </div>
            <div className="text-sm text-gray-600">Active Today</div>
          </div>

          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">
              {avgInteractions.toFixed(1)}
            </div>
            <div className="text-sm text-gray-600">Avg Interactions</div>
          </div>
        </div>

        {/* Role Distribution */}
        <div className="pt-4 border-t">
          <h4 className="font-medium text-gray-900 mb-3">Role Distribution</h4>
          <div className="space-y-2">
            {Object.entries(roleDistribution).map(([role, count]) => {
              const percentage = (count / totalUsers) * 100;
              return (
                <div key={role} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge className={getRoleColor(role)}>
                      <span className="flex items-center gap-1">
                        {getRoleIcon(role)}
                        {role.replace('_', ' ')}
                      </span>
                    </Badge>
                    <span className="text-sm text-gray-600">{count} users</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-blue-500 rounded-full"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 w-12 text-right">
                      {percentage.toFixed(0)}%
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Helper functions (duplicated for standalone use)
const getRoleColor = (role: string): string => {
  const colors: Record<string, string> = {
    'super_admin': 'bg-red-100 text-red-800',
    'admin': 'bg-purple-100 text-purple-800',
    'content_editor': 'bg-blue-100 text-blue-800',
    'viewer': 'bg-gray-100 text-gray-800',
  };
  return colors[role] || colors['viewer'];
};

const getRoleIcon = (role: string) => {
  switch (role) {
    case 'super_admin':
    case 'admin':
      return <Crown className="w-3 h-3" />;
    case 'content_editor':
      return <Edit className="w-3 h-3" />;
    default:
      return <Eye className="w-3 h-3" />;
  }
};
