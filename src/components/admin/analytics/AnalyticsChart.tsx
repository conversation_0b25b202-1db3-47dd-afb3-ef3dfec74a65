import { useMemo } from "react";

interface ChartData {
  date: string;
  value: number;
  formattedDate: string;
}

interface AnalyticsChartProps {
  data: ChartData[];
  type: "line" | "bar" | "area";
  color: string;
  height?: number;
  showGrid?: boolean;
  showTooltip?: boolean;
}

export const AnalyticsChart = ({ 
  data, 
  type = "line", 
  color = "#3B82F6", 
  height = 200,
  showGrid = true,
  showTooltip = true 
}: AnalyticsChartProps) => {
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return null;

    const maxValue = Math.max(...data.map(d => d.value));
    const minValue = Math.min(...data.map(d => d.value));
    const range = maxValue - minValue || 1;

    return data.map((item, index) => ({
      ...item,
      x: (index / (data.length - 1)) * 100,
      y: ((maxValue - item.value) / range) * 80 + 10, // 10% padding top/bottom
    }));
  }, [data]);

  if (!chartData || chartData.length === 0) {
    return (
      <div 
        className="flex items-center justify-center bg-gray-50 rounded-lg"
        style={{ height }}
      >
        <p className="text-gray-500">No data available</p>
      </div>
    );
  }

  const renderLineChart = () => {
    const pathData = chartData
      .map((point, index) => `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`)
      .join(' ');

    const areaPathData = type === "area" 
      ? `${pathData} L ${chartData[chartData.length - 1].x} 90 L ${chartData[0].x} 90 Z`
      : "";

    return (
      <g>
        {/* Area fill */}
        {type === "area" && (
          <path
            d={areaPathData}
            fill={`${color}20`}
            stroke="none"
          />
        )}
        
        {/* Line */}
        <path
          d={pathData}
          fill="none"
          stroke={color}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        
        {/* Data points */}
        {chartData.map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r="3"
            fill={color}
            className="hover:r-4 transition-all cursor-pointer"
          />
        ))}
      </g>
    );
  };

  const renderBarChart = () => {
    const barWidth = 80 / chartData.length;
    
    return (
      <g>
        {chartData.map((point, index) => {
          const barHeight = 90 - point.y;
          return (
            <rect
              key={index}
              x={point.x - barWidth / 2}
              y={point.y}
              width={barWidth}
              height={barHeight}
              fill={color}
              className="hover:opacity-80 transition-opacity cursor-pointer"
              rx="2"
            />
          );
        })}
      </g>
    );
  };

  const renderGrid = () => {
    if (!showGrid) return null;

    const horizontalLines = [];
    const verticalLines = [];

    // Horizontal grid lines
    for (let i = 0; i <= 4; i++) {
      const y = 10 + (i * 20);
      horizontalLines.push(
        <line
          key={`h-${i}`}
          x1="0"
          y1={y}
          x2="100"
          y2={y}
          stroke="#E5E7EB"
          strokeWidth="1"
        />
      );
    }

    // Vertical grid lines
    for (let i = 0; i <= 4; i++) {
      const x = i * 25;
      verticalLines.push(
        <line
          key={`v-${i}`}
          x1={x}
          y1="10"
          x2={x}
          y2="90"
          stroke="#E5E7EB"
          strokeWidth="1"
        />
      );
    }

    return (
      <g>
        {horizontalLines}
        {verticalLines}
      </g>
    );
  };

  return (
    <div className="relative">
      <svg
        width="100%"
        height={height}
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        className="overflow-visible"
      >
        {renderGrid()}
        {type === "bar" ? renderBarChart() : renderLineChart()}
      </svg>
      
      {/* X-axis labels */}
      <div className="flex justify-between mt-2 text-xs text-gray-500">
        {chartData.map((point, index) => {
          // Show only first, middle, and last labels to avoid crowding
          if (index === 0 || index === Math.floor(chartData.length / 2) || index === chartData.length - 1) {
            return (
              <span key={index}>
                {point.formattedDate}
              </span>
            );
          }
          return <span key={index}></span>;
        })}
      </div>

      {/* Tooltip (simplified version) */}
      {showTooltip && (
        <div className="absolute top-2 right-2 bg-white border rounded-lg p-2 text-xs shadow-lg opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
          <div className="text-gray-600">Latest: {chartData[chartData.length - 1]?.value}</div>
          <div className="text-gray-600">Date: {chartData[chartData.length - 1]?.formattedDate}</div>
        </div>
      )}
    </div>
  );
};

// Simple bar chart for smaller metrics
export const MiniBarChart = ({ 
  data, 
  color = "#3B82F6", 
  height = 40 
}: {
  data: number[];
  color?: string;
  height?: number;
}) => {
  const maxValue = Math.max(...data);
  
  return (
    <div className="flex items-end space-x-1" style={{ height }}>
      {data.map((value, index) => (
        <div
          key={index}
          className="bg-current rounded-sm flex-1 min-w-[2px]"
          style={{
            height: `${(value / maxValue) * 100}%`,
            color,
          }}
        />
      ))}
    </div>
  );
};

// Donut chart for simple percentage displays
export const DonutChart = ({ 
  percentage, 
  color = "#3B82F6", 
  size = 60,
  strokeWidth = 6 
}: {
  percentage: number;
  color?: string;
  size?: number;
  strokeWidth?: number;
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = `${(percentage / 100) * circumference} ${circumference}`;

  return (
    <div className="relative inline-flex items-center justify-center">
      <svg width={size} height={size} className="transform -rotate-90">
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#E5E7EB"
          strokeWidth={strokeWidth}
          fill="none"
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeLinecap="round"
          className="transition-all duration-300"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <span className="text-sm font-semibold text-gray-900">
          {percentage.toFixed(0)}%
        </span>
      </div>
    </div>
  );
};

// Sparkline for inline metrics
export const Sparkline = ({ 
  data, 
  color = "#3B82F6", 
  width = 100, 
  height = 20 
}: {
  data: number[];
  color?: string;
  width?: number;
  height?: number;
}) => {
  if (!data || data.length === 0) return null;

  const maxValue = Math.max(...data);
  const minValue = Math.min(...data);
  const range = maxValue - minValue || 1;

  const points = data.map((value, index) => {
    const x = (index / (data.length - 1)) * width;
    const y = height - ((value - minValue) / range) * height;
    return `${x},${y}`;
  }).join(' ');

  return (
    <svg width={width} height={height} className="inline-block">
      <polyline
        points={points}
        fill="none"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
