import { useState, useEffect } from "react";
import { useRealTimeAnalytics, formatAnalyticsData } from "@/hooks/useAnalytics";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Activity, 
  Users, 
  Eye, 
  TrendingUp, 
  TrendingDown,
  Clock,
  Globe,
  Zap
} from "lucide-react";
import { Sparkline } from "./AnalyticsChart";

export const RealTimeWidget = () => {
  const { analytics, metrics, isLoading, lastUpdated } = useRealTimeAnalytics();
  const [realtimeData, setRealtimeData] = useState({
    activeUsers: 0,
    pageViews: 0,
    interactions: 0,
    trend: [] as number[],
  });

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setRealtimeData(prev => ({
        activeUsers: Math.max(0, prev.activeUsers + Math.floor(Math.random() * 3) - 1),
        pageViews: prev.pageViews + Math.floor(Math.random() * 2),
        interactions: prev.interactions + Math.floor(Math.random() * 3),
        trend: [...prev.trend.slice(-19), Math.floor(Math.random() * 100)],
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Real-Time Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Real-Time Activity
          </div>
          <Badge variant="outline" className="text-green-600 border-green-600">
            <div className="w-2 h-2 bg-green-600 rounded-full mr-1 animate-pulse"></div>
            Live
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Activity */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {realtimeData.activeUsers}
            </div>
            <div className="text-xs text-gray-600">Active Users</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {realtimeData.pageViews}
            </div>
            <div className="text-xs text-gray-600">Page Views</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {realtimeData.interactions}
            </div>
            <div className="text-xs text-gray-600">Interactions</div>
          </div>
        </div>

        {/* Activity Trend */}
        {realtimeData.trend.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Activity Trend</span>
              <span className="text-xs text-gray-500">Last 5 minutes</span>
            </div>
            <Sparkline 
              data={realtimeData.trend} 
              color="#3B82F6" 
              width={200} 
              height={30} 
            />
          </div>
        )}

        {/* System Status */}
        {metrics && (
          <div className="space-y-3 pt-3 border-t">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Zap className="w-4 h-4 text-yellow-600" />
                <span className="text-sm text-gray-600">Response Time</span>
              </div>
              <span className="text-sm font-medium">
                {metrics.performance.avgResponseTime}ms
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-gray-600">Uptime</span>
              </div>
              <span className="text-sm font-medium text-green-600">
                {formatAnalyticsData.formatPercentage(metrics.performance.uptime)}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="w-4 h-4 text-purple-600" />
                <span className="text-sm text-gray-600">Requests/sec</span>
              </div>
              <span className="text-sm font-medium">
                {metrics.performance.requestsPerSecond.toFixed(2)}
              </span>
            </div>
          </div>
        )}

        {/* Last Updated */}
        <div className="text-center text-xs text-gray-500 pt-2 border-t">
          <Clock className="w-3 h-3 inline mr-1" />
          Updated {formatAnalyticsData.formatTimeAgo(lastUpdated)}
        </div>
      </CardContent>
    </Card>
  );
};

// Quick stats widget for dashboard
export const QuickStatsWidget = () => {
  const { analytics, isLoading } = useRealTimeAnalytics();

  if (isLoading || !analytics) {
    return (
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const stats = [
    {
      label: "Total Views",
      value: formatAnalyticsData.formatNumber(analytics.overview.totalPageViews),
      icon: Eye,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      change: 12.5,
    },
    {
      label: "Unique Visitors",
      value: formatAnalyticsData.formatNumber(analytics.overview.uniqueVisitors),
      icon: Users,
      color: "text-green-600",
      bgColor: "bg-green-100",
      change: 8.2,
    },
    {
      label: "Conversion Rate",
      value: formatAnalyticsData.formatPercentage(analytics.overview.conversionRate),
      icon: TrendingUp,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      change: 5.7,
    },
    {
      label: "Active Content",
      value: analytics.overview.publishedContent.toString(),
      icon: Activity,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      change: 2.1,
    },
  ];

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => {
        const Icon = stat.icon;
        const TrendIcon = stat.change > 0 ? TrendingUp : TrendingDown;
        const trendColor = stat.change > 0 ? "text-green-600" : "text-red-600";

        return (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <div className={`flex items-center mt-1 text-sm ${trendColor}`}>
                    <TrendIcon className="w-3 h-3 mr-1" />
                    {Math.abs(stat.change).toFixed(1)}%
                  </div>
                </div>
                <div className={`p-2 rounded-full ${stat.bgColor}`}>
                  <Icon className={`w-5 h-5 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

// Activity feed widget
export const ActivityFeedWidget = () => {
  const [activities] = useState([
    {
      id: 1,
      type: "page_view",
      description: "New page view on Network Solutions",
      timestamp: Date.now() - 1000 * 60 * 2,
      icon: Eye,
      color: "text-blue-600",
    },
    {
      id: 2,
      type: "content_edit",
      description: "Hero section updated by Admin",
      timestamp: Date.now() - 1000 * 60 * 5,
      icon: Activity,
      color: "text-green-600",
    },
    {
      id: 3,
      type: "form_submission",
      description: "New contact form submission",
      timestamp: Date.now() - 1000 * 60 * 8,
      icon: Users,
      color: "text-purple-600",
    },
    {
      id: 4,
      type: "user_login",
      description: "Content Editor logged in",
      timestamp: Date.now() - 1000 * 60 * 12,
      icon: Activity,
      color: "text-orange-600",
    },
  ]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="w-5 h-5" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {activities.map((activity) => {
            const Icon = activity.icon;
            return (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className={`p-1 rounded-full bg-gray-100`}>
                  <Icon className={`w-3 h-3 ${activity.color}`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">{activity.description}</p>
                  <p className="text-xs text-gray-500">
                    {formatAnalyticsData.formatTimeAgo(activity.timestamp)}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};
