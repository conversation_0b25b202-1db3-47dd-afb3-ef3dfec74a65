import { useState, useEffect } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Activity, 
  Users, 
  Eye, 
  TrendingUp, 
  TrendingDown,
  Clock,
  Globe,
  Zap,
  RefreshCw,
  Monitor,
  MousePointer,
  FileText,
  MessageSquare
} from "lucide-react";

interface RealTimeMetrics {
  activeUsers: number;
  pageViews: number;
  interactions: number;
  formSubmissions: number;
  averageSessionTime: number;
  bounceRate: number;
  topPages: Array<{ path: string; views: number }>;
  recentActivity: Array<{ type: string; description: string; timestamp: number }>;
}

export const EnhancedRealTimeDashboard = () => {
  const [metrics, setMetrics] = useState<RealTimeMetrics>({
    activeUsers: 0,
    pageViews: 0,
    interactions: 0,
    formSubmissions: 0,
    averageSessionTime: 0,
    bounceRate: 0,
    topPages: [],
    recentActivity: []
  });
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Get real-time analytics data
  const systemMetrics = useQuery(api.analytics.getSystemMetrics);
  const dashboardAnalytics = useQuery(api.analytics.getDashboardAnalytics, { dateRange: "1d" });

  // Fetch recent page views (last 5 minutes)
  const recentPageViews = useQuery(api.analytics.getRecentActivity, {
    minutes: 5,
    limit: 50
  });

  // Update metrics when data changes
  useEffect(() => {
    if (systemMetrics && dashboardAnalytics) {
      const now = Date.now();
      const fiveMinutesAgo = now - (5 * 60 * 1000);

      // Calculate active users (users with activity in last 5 minutes)
      const activeUsers = recentPageViews?.filter(
        (view: any) => view.timestamp > fiveMinutesAgo
      ).length || 0;

      // Get top pages from recent activity
      const pageViewCounts = recentPageViews?.reduce((acc: any, view: any) => {
        acc[view.path] = (acc[view.path] || 0) + 1;
        return acc;
      }, {}) || {};

      const topPages = Object.entries(pageViewCounts)
        .map(([path, views]) => ({ path, views: views as number }))
        .sort((a, b) => b.views - a.views)
        .slice(0, 5);

      // Create recent activity feed
      const recentActivity = recentPageViews?.slice(0, 10).map((view: any) => ({
        type: 'page_view',
        description: `Page view: ${view.path}`,
        timestamp: view.timestamp
      })) || [];

      setMetrics({
        activeUsers,
        pageViews: dashboardAnalytics.overview?.totalPageViews || 0,
        interactions: systemMetrics.activity?.totalInteractions || 0,
        formSubmissions: dashboardAnalytics.overview?.totalFormSubmissions || 0,
        averageSessionTime: systemMetrics.performance?.averageResponseTime || 0,
        bounceRate: systemMetrics.performance?.errorRate || 0,
        topPages,
        recentActivity
      });
      
      setLastUpdated(new Date());
    }
  }, [systemMetrics, dashboardAnalytics, recentPageViews]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setIsRefreshing(true);
      // The useQuery hooks will automatically refetch
      setTimeout(() => setIsRefreshing(false), 1000);
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const handleManualRefresh = () => {
    setIsRefreshing(true);
    // Force refresh by updating a state that triggers re-render
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'page_view':
        return <Eye className="w-4 h-4" />;
      case 'form_submission':
        return <MessageSquare className="w-4 h-4" />;
      case 'content_interaction':
        return <FileText className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Real-Time Analytics</h2>
          <p className="text-sm text-gray-500">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleManualRefresh}
          disabled={isRefreshing}
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-green-600">{metrics.activeUsers}</p>
                <p className="text-xs text-gray-500">Last 5 minutes</p>
              </div>
              <Users className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Page Views</p>
                <p className="text-2xl font-bold text-blue-600">{metrics.pageViews}</p>
                <p className="text-xs text-gray-500">Today</p>
              </div>
              <Eye className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Interactions</p>
                <p className="text-2xl font-bold text-purple-600">{metrics.interactions}</p>
                <p className="text-xs text-gray-500">All time</p>
              </div>
              <MousePointer className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Form Submissions</p>
                <p className="text-2xl font-bold text-orange-600">{metrics.formSubmissions}</p>
                <p className="text-xs text-gray-500">Today</p>
              </div>
              <MessageSquare className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Real-time Activity and Top Pages */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity Feed */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {metrics.recentActivity.length > 0 ? (
                metrics.recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded-lg">
                    <div className="text-blue-600">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-900">{activity.description}</p>
                      <p className="text-xs text-gray-500">{formatTime(activity.timestamp)}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No recent activity
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Top Pages */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Top Pages (Last 5 min)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics.topPages.length > 0 ? (
                metrics.topPages.map((page, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{page.path}</p>
                    </div>
                    <Badge variant="secondary">{page.views} views</Badge>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No page views in the last 5 minutes
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Indicators */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="w-5 h-5" />
            System Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {metrics.averageSessionTime.toFixed(1)}ms
              </div>
              <div className="text-sm text-gray-600">Avg Response Time</div>
            </div>
            
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {(metrics.bounceRate * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Error Rate</div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {systemMetrics?.performance?.requestsPerSecond?.toFixed(2) || '0.00'}
              </div>
              <div className="text-sm text-gray-600">Requests/sec</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
