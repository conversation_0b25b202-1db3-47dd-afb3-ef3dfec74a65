import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatAnalyticsData } from "@/hooks/useAnalytics";
import { FileText, Eye, Edit, Upload, Trash2, TrendingUp } from "lucide-react";

interface ContentPerformance {
  contentId: string;
  views: number;
  edits: number;
  publishes: number;
}

interface ContentPerformanceTableProps {
  data: ContentPerformance[];
}

export const ContentPerformanceTable = ({ data }: ContentPerformanceTableProps) => {
  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Content Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            No content performance data available
          </div>
        </CardContent>
      </Card>
    );
  }

  const getContentTitle = (contentId: string): string => {
    // In a real app, you'd fetch this from the content database
    // For now, we'll generate a title based on the ID
    const titles: Record<string, string> = {
      'hero-section': 'Hero Section',
      'services-overview': 'Services Overview',
      'network-solutions': 'Network Solutions',
      'about-us': 'About Us',
      'contact-info': 'Contact Information',
      'testimonials': 'Customer Testimonials',
      'features': 'Key Features',
      'pricing': 'Pricing Information',
    };

    return titles[contentId] || contentId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getContentType = (contentId: string): string => {
    if (contentId.includes('hero')) return 'Hero Section';
    if (contentId.includes('service')) return 'Service';
    if (contentId.includes('testimonial')) return 'Testimonial';
    if (contentId.includes('feature')) return 'Feature';
    if (contentId.includes('pricing')) return 'Pricing';
    if (contentId.includes('about')) return 'About';
    if (contentId.includes('contact')) return 'Contact';
    return 'Content Block';
  };

  const getTypeColor = (type: string): string => {
    const colors: Record<string, string> = {
      'Hero Section': 'bg-blue-100 text-blue-800',
      'Service': 'bg-green-100 text-green-800',
      'Testimonial': 'bg-purple-100 text-purple-800',
      'Feature': 'bg-orange-100 text-orange-800',
      'Pricing': 'bg-pink-100 text-pink-800',
      'About': 'bg-indigo-100 text-indigo-800',
      'Contact': 'bg-yellow-100 text-yellow-800',
      'Content Block': 'bg-gray-100 text-gray-800',
    };
    return colors[type] || colors['Content Block'];
  };

  const getEngagementScore = (content: ContentPerformance): number => {
    // Simple engagement score based on views, edits, and publishes
    return (content.views * 1) + (content.edits * 5) + (content.publishes * 10);
  };

  const sortedData = [...data].sort((a, b) => getEngagementScore(b) - getEngagementScore(a));
  const totalViews = data.reduce((sum, content) => sum + content.views, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="w-5 h-5" />
          Content Performance
        </CardTitle>
        <p className="text-sm text-gray-600">
          Content engagement and interaction metrics
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedData.map((content, index) => {
            const title = getContentTitle(content.contentId);
            const type = getContentType(content.contentId);
            const engagementScore = getEngagementScore(content);
            const viewPercentage = totalViews > 0 ? (content.views / totalViews) * 100 : 0;

            return (
              <div key={content.contentId} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-4 flex-1">
                  {/* Rank */}
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-sm font-semibold text-gray-600">
                    {index + 1}
                  </div>

                  {/* Content Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-gray-900 truncate">
                        {title}
                      </h4>
                      <Badge className={getTypeColor(type)}>
                        {type}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className="truncate">ID: {content.contentId}</span>
                      <span className="text-blue-600 font-medium">
                        Score: {engagementScore}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Metrics */}
                <div className="flex items-center space-x-6">
                  {/* Views */}
                  <div className="text-center">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 mb-1">
                      <Eye className="w-4 h-4 text-blue-600" />
                    </div>
                    <div className="font-semibold text-gray-900 text-sm">
                      {formatAnalyticsData.formatNumber(content.views)}
                    </div>
                    <div className="text-xs text-gray-500">views</div>
                  </div>

                  {/* Edits */}
                  <div className="text-center">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 mb-1">
                      <Edit className="w-4 h-4 text-green-600" />
                    </div>
                    <div className="font-semibold text-gray-900 text-sm">
                      {content.edits}
                    </div>
                    <div className="text-xs text-gray-500">edits</div>
                  </div>

                  {/* Publishes */}
                  <div className="text-center">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 mb-1">
                      <Upload className="w-4 h-4 text-purple-600" />
                    </div>
                    <div className="font-semibold text-gray-900 text-sm">
                      {content.publishes}
                    </div>
                    <div className="text-xs text-gray-500">publishes</div>
                  </div>

                  {/* Visual Bar */}
                  <div className="w-20">
                    <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-blue-500 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min(viewPercentage, 100)}%` }}
                      />
                    </div>
                    <div className="text-xs text-gray-500 text-center mt-1">
                      {viewPercentage.toFixed(1)}%
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Summary */}
        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              Showing {data.length} content items
            </span>
            <span className="text-gray-600">
              Total views: {formatAnalyticsData.formatNumber(totalViews)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Component for content insights
export const ContentInsights = ({ data }: ContentPerformanceTableProps) => {
  if (!data || data.length === 0) return null;

  const totalContent = data.length;
  const totalViews = data.reduce((sum, content) => sum + content.views, 0);
  const totalEdits = data.reduce((sum, content) => sum + content.edits, 0);
  const totalPublishes = data.reduce((sum, content) => sum + content.publishes, 0);

  const mostViewedContent = data.reduce((max, content) => 
    content.views > max.views ? content : max, data[0]);

  const mostEditedContent = data.reduce((max, content) => 
    content.edits > max.edits ? content : max, data[0]);

  const avgViewsPerContent = totalViews / totalContent;
  const avgEditsPerContent = totalEdits / totalContent;

  // Content type distribution
  const typeDistribution = data.reduce((acc, content) => {
    const type = getContentType(content.contentId);
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5" />
          Content Insights
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {avgViewsPerContent.toFixed(1)}
            </div>
            <div className="text-sm text-gray-600">Avg Views/Content</div>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {avgEditsPerContent.toFixed(1)}
            </div>
            <div className="text-sm text-gray-600">Avg Edits/Content</div>
          </div>

          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {totalPublishes}
            </div>
            <div className="text-sm text-gray-600">Total Publishes</div>
          </div>

          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">
              {totalContent}
            </div>
            <div className="text-sm text-gray-600">Content Items</div>
          </div>
        </div>

        {/* Top Performers */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Most Viewed Content</h4>
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="font-medium">{getContentTitle(mostViewedContent.contentId)}</div>
              <div className="text-sm text-gray-600">{mostViewedContent.views} views</div>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-2">Most Edited Content</h4>
            <div className="p-3 bg-green-50 rounded-lg">
              <div className="font-medium">{getContentTitle(mostEditedContent.contentId)}</div>
              <div className="text-sm text-gray-600">{mostEditedContent.edits} edits</div>
            </div>
          </div>
        </div>

        {/* Content Type Distribution */}
        <div className="pt-4 border-t">
          <h4 className="font-medium text-gray-900 mb-3">Content Type Distribution</h4>
          <div className="space-y-2">
            {Object.entries(typeDistribution).map(([type, count]) => {
              const percentage = (count / totalContent) * 100;
              return (
                <div key={type} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge className={getTypeColor(type)}>
                      {type}
                    </Badge>
                    <span className="text-sm text-gray-600">{count} items</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-blue-500 rounded-full"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 w-12 text-right">
                      {percentage.toFixed(0)}%
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Helper functions (duplicated for standalone use)
const getContentTitle = (contentId: string): string => {
  const titles: Record<string, string> = {
    'hero-section': 'Hero Section',
    'services-overview': 'Services Overview',
    'network-solutions': 'Network Solutions',
    'about-us': 'About Us',
    'contact-info': 'Contact Information',
    'testimonials': 'Customer Testimonials',
    'features': 'Key Features',
    'pricing': 'Pricing Information',
  };

  return titles[contentId] || contentId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const getContentType = (contentId: string): string => {
  if (contentId.includes('hero')) return 'Hero Section';
  if (contentId.includes('service')) return 'Service';
  if (contentId.includes('testimonial')) return 'Testimonial';
  if (contentId.includes('feature')) return 'Feature';
  if (contentId.includes('pricing')) return 'Pricing';
  if (contentId.includes('about')) return 'About';
  if (contentId.includes('contact')) return 'Contact';
  return 'Content Block';
};

const getTypeColor = (type: string): string => {
  const colors: Record<string, string> = {
    'Hero Section': 'bg-blue-100 text-blue-800',
    'Service': 'bg-green-100 text-green-800',
    'Testimonial': 'bg-purple-100 text-purple-800',
    'Feature': 'bg-orange-100 text-orange-800',
    'Pricing': 'bg-pink-100 text-pink-800',
    'About': 'bg-indigo-100 text-indigo-800',
    'Contact': 'bg-yellow-100 text-yellow-800',
    'Content Block': 'bg-gray-100 text-gray-800',
  };
  return colors[type] || colors['Content Block'];
};
