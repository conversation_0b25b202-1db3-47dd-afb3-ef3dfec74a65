import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { 
  Download, 
  FileText, 
  Calendar,
  Database,
  Mail,
  Settings,
  BarChart3
} from "lucide-react";
import { toast } from "sonner";

interface ExportOptions {
  dateRange: string;
  format: 'csv' | 'json' | 'pdf';
  includePageViews: boolean;
  includeContentInteractions: boolean;
  includeFormSubmissions: boolean;
  includeUserActivity: boolean;
  includeConversions: boolean;
}

export const AnalyticsExport = () => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    dateRange: '30d',
    format: 'csv',
    includePageViews: true,
    includeContentInteractions: true,
    includeFormSubmissions: true,
    includeUserActivity: false,
    includeConversions: true
  });
  const [isExporting, setIsExporting] = useState(false);

  // Get analytics data for export
  const dashboardAnalytics = useQuery(api.analytics.getDashboardAnalytics, { 
    dateRange: exportOptions.dateRange 
  });
  const contentAnalytics = useQuery(api.analytics.getContentAnalytics, { 
    dateRange: exportOptions.dateRange 
  });
  const userAnalytics = useQuery(api.analytics.getUserAnalytics, { 
    dateRange: exportOptions.dateRange 
  });

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // Collect data based on selected options
      const exportData: any = {
        metadata: {
          exportDate: new Date().toISOString(),
          dateRange: exportOptions.dateRange,
          format: exportOptions.format
        }
      };

      if (exportOptions.includePageViews && dashboardAnalytics) {
        exportData.pageViews = {
          total: dashboardAnalytics.overview?.totalPageViews || 0,
          unique: dashboardAnalytics.overview?.uniqueVisitors || 0,
          byDay: dashboardAnalytics.charts?.pageViewsByDay || {},
          topPages: dashboardAnalytics.charts?.topPages || []
        };
      }

      if (exportOptions.includeContentInteractions && contentAnalytics) {
        exportData.contentInteractions = contentAnalytics;
      }

      if (exportOptions.includeFormSubmissions && dashboardAnalytics) {
        exportData.formSubmissions = {
          total: dashboardAnalytics.overview?.totalFormSubmissions || 0,
          successful: dashboardAnalytics.overview?.successfulSubmissions || 0,
          conversionRate: dashboardAnalytics.overview?.conversionRate || 0
        };
      }

      if (exportOptions.includeUserActivity && userAnalytics) {
        exportData.userActivity = userAnalytics;
      }

      // Export based on format
      switch (exportOptions.format) {
        case 'csv':
          exportToCSV(exportData);
          break;
        case 'json':
          exportToJSON(exportData);
          break;
        case 'pdf':
          await exportToPDF(exportData);
          break;
      }

      toast.success(`Analytics data exported successfully as ${exportOptions.format.toUpperCase()}`);
    } catch (error) {
      toast.error('Failed to export analytics data');
      console.error('Export error:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const exportToCSV = (data: any) => {
    let csvContent = "data:text/csv;charset=utf-8,";
    
    // Add metadata
    csvContent += "Analytics Export Report\n";
    csvContent += `Export Date,${data.metadata.exportDate}\n`;
    csvContent += `Date Range,${data.metadata.dateRange}\n\n`;

    // Add page views data
    if (data.pageViews) {
      csvContent += "Page Views Summary\n";
      csvContent += `Total Page Views,${data.pageViews.total}\n`;
      csvContent += `Unique Visitors,${data.pageViews.unique}\n\n`;
      
      csvContent += "Top Pages\n";
      csvContent += "Page,Views\n";
      data.pageViews.topPages.forEach((page: any) => {
        csvContent += `${page.path},${page.views}\n`;
      });
      csvContent += "\n";
    }

    // Add form submissions data
    if (data.formSubmissions) {
      csvContent += "Form Submissions Summary\n";
      csvContent += `Total Submissions,${data.formSubmissions.total}\n`;
      csvContent += `Successful Submissions,${data.formSubmissions.successful}\n`;
      csvContent += `Conversion Rate,${data.formSubmissions.conversionRate}%\n\n`;
    }

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `analytics-export-${Date.now()}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToJSON = (data: any) => {
    const jsonString = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonString], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `analytics-export-${Date.now()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const exportToPDF = async (data: any) => {
    // For PDF export, we'll create a simple HTML report and use the browser's print functionality
    const reportHTML = generateHTMLReport(data);
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(reportHTML);
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 250);
    }
  };

  const generateHTMLReport = (data: any) => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Analytics Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .section { margin-bottom: 30px; }
          .metric { display: inline-block; margin: 10px 20px; text-align: center; }
          .metric-value { font-size: 24px; font-weight: bold; color: #3B82F6; }
          .metric-label { font-size: 14px; color: #666; }
          table { width: 100%; border-collapse: collapse; margin-top: 10px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Analytics Report</h1>
          <p>Generated on ${new Date().toLocaleDateString()}</p>
          <p>Date Range: ${data.metadata.dateRange}</p>
        </div>
        
        ${data.pageViews ? `
        <div class="section">
          <h2>Page Views</h2>
          <div class="metric">
            <div class="metric-value">${data.pageViews.total}</div>
            <div class="metric-label">Total Page Views</div>
          </div>
          <div class="metric">
            <div class="metric-value">${data.pageViews.unique}</div>
            <div class="metric-label">Unique Visitors</div>
          </div>
          
          <h3>Top Pages</h3>
          <table>
            <tr><th>Page</th><th>Views</th></tr>
            ${data.pageViews.topPages.map((page: any) => 
              `<tr><td>${page.path}</td><td>${page.views}</td></tr>`
            ).join('')}
          </table>
        </div>
        ` : ''}
        
        ${data.formSubmissions ? `
        <div class="section">
          <h2>Form Submissions</h2>
          <div class="metric">
            <div class="metric-value">${data.formSubmissions.total}</div>
            <div class="metric-label">Total Submissions</div>
          </div>
          <div class="metric">
            <div class="metric-value">${data.formSubmissions.successful}</div>
            <div class="metric-label">Successful Submissions</div>
          </div>
          <div class="metric">
            <div class="metric-value">${data.formSubmissions.conversionRate}%</div>
            <div class="metric-label">Conversion Rate</div>
          </div>
        </div>
        ` : ''}
      </body>
      </html>
    `;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Export Analytics Data
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Date Range Selection */}
          <div className="space-y-2">
            <Label>Date Range</Label>
            <Select 
              value={exportOptions.dateRange} 
              onValueChange={(value) => setExportOptions(prev => ({ ...prev, dateRange: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Format Selection */}
          <div className="space-y-2">
            <Label>Export Format</Label>
            <Select 
              value={exportOptions.format} 
              onValueChange={(value: 'csv' | 'json' | 'pdf') => setExportOptions(prev => ({ ...prev, format: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="csv">CSV (Spreadsheet)</SelectItem>
                <SelectItem value="json">JSON (Data)</SelectItem>
                <SelectItem value="pdf">PDF (Report)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Data Selection */}
          <div className="space-y-4">
            <Label>Include Data</Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="pageViews"
                  checked={exportOptions.includePageViews}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({ ...prev, includePageViews: !!checked }))
                  }
                />
                <Label htmlFor="pageViews">Page Views</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="contentInteractions"
                  checked={exportOptions.includeContentInteractions}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({ ...prev, includeContentInteractions: !!checked }))
                  }
                />
                <Label htmlFor="contentInteractions">Content Interactions</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="formSubmissions"
                  checked={exportOptions.includeFormSubmissions}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({ ...prev, includeFormSubmissions: !!checked }))
                  }
                />
                <Label htmlFor="formSubmissions">Form Submissions</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="userActivity"
                  checked={exportOptions.includeUserActivity}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({ ...prev, includeUserActivity: !!checked }))
                  }
                />
                <Label htmlFor="userActivity">User Activity</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="conversions"
                  checked={exportOptions.includeConversions}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({ ...prev, includeConversions: !!checked }))
                  }
                />
                <Label htmlFor="conversions">Conversions</Label>
              </div>
            </div>
          </div>

          {/* Export Button */}
          <Button 
            onClick={handleExport}
            disabled={isExporting}
            className="w-full"
          >
            <Download className="w-4 h-4 mr-2" />
            {isExporting ? 'Exporting...' : `Export as ${exportOptions.format.toUpperCase()}`}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
