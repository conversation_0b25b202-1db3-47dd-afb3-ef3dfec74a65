import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatAnalyticsData } from "@/hooks/useAnalytics";
import { Eye, TrendingUp, ExternalLink } from "lucide-react";

interface TopPage {
  path: string;
  views: number;
}

interface TopPagesTableProps {
  data: TopPage[];
}

export const TopPagesTable = ({ data }: TopPagesTableProps) => {
  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="w-5 h-5" />
            Top Pages
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            No page data available
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalViews = data.reduce((sum, page) => sum + page.views, 0);

  const getPageTitle = (path: string): string => {
    const pathTitles: Record<string, string> = {
      '/': 'Home',
      '/services': 'Services',
      '/network-solutions': 'Network Solutions',
      '/network-solutions/national-connectivity': 'National Connectivity',
      '/network-solutions/branch-office': 'Branch Office Connectivity',
      '/network-solutions/managed-internet': 'Managed Enterprise Internet',
      '/about': 'About Us',
      '/contact': 'Contact',
      '/admin': 'Admin Dashboard',
    };

    return pathTitles[path] || path.split('/').pop()?.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || path;
  };

  const getPageCategory = (path: string): string => {
    if (path === '/') return 'Home';
    if (path.startsWith('/admin')) return 'Admin';
    if (path.startsWith('/network-solutions')) return 'Network Solutions';
    if (path === '/services') return 'Services';
    if (path === '/about') return 'About';
    if (path === '/contact') return 'Contact';
    return 'Other';
  };

  const getCategoryColor = (category: string): string => {
    const colors: Record<string, string> = {
      'Home': 'bg-blue-100 text-blue-800',
      'Network Solutions': 'bg-green-100 text-green-800',
      'Services': 'bg-purple-100 text-purple-800',
      'About': 'bg-orange-100 text-orange-800',
      'Contact': 'bg-pink-100 text-pink-800',
      'Admin': 'bg-gray-100 text-gray-800',
      'Other': 'bg-gray-100 text-gray-600',
    };
    return colors[category] || colors['Other'];
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="w-5 h-5" />
          Top Pages
        </CardTitle>
        <p className="text-sm text-gray-600">
          Most visited pages by total views
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((page, index) => {
            const percentage = (page.views / totalViews) * 100;
            const category = getPageCategory(page.path);
            const title = getPageTitle(page.path);

            return (
              <div key={page.path} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-4 flex-1">
                  {/* Rank */}
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-sm font-semibold text-gray-600">
                    {index + 1}
                  </div>

                  {/* Page Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-gray-900 truncate">
                        {title}
                      </h4>
                      <Badge className={getCategoryColor(category)}>
                        {category}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <span className="truncate">{page.path}</span>
                      <ExternalLink className="w-3 h-3 flex-shrink-0" />
                    </div>
                  </div>
                </div>

                {/* Metrics */}
                <div className="flex items-center space-x-6">
                  {/* Views */}
                  <div className="text-right">
                    <div className="font-semibold text-gray-900">
                      {formatAnalyticsData.formatNumber(page.views)}
                    </div>
                    <div className="text-xs text-gray-500">views</div>
                  </div>

                  {/* Percentage */}
                  <div className="text-right min-w-[60px]">
                    <div className="font-semibold text-gray-900">
                      {percentage.toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-500">of total</div>
                  </div>

                  {/* Visual Bar */}
                  <div className="w-20">
                    <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-blue-500 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Summary */}
        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              Showing top {data.length} pages
            </span>
            <span className="text-gray-600">
              Total views: {formatAnalyticsData.formatNumber(totalViews)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Component for page performance insights
export const PageInsights = ({ data }: TopPagesTableProps) => {
  if (!data || data.length === 0) return null;

  const totalViews = data.reduce((sum, page) => sum + page.views, 0);
  const topPage = data[0];
  const homePageViews = data.find(p => p.path === '/')?.views || 0;
  const networkSolutionsViews = data.filter(p => p.path.startsWith('/network-solutions')).reduce((sum, p) => sum + p.views, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5" />
          Page Insights
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {((homePageViews / totalViews) * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Homepage Traffic</div>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {((networkSolutionsViews / totalViews) * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">Network Solutions</div>
          </div>

          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {data.length}
            </div>
            <div className="text-sm text-gray-600">Active Pages</div>
          </div>
        </div>

        <div className="pt-4 border-t">
          <h4 className="font-medium text-gray-900 mb-2">Top Performing Page</h4>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <div className="font-medium">{getPageTitle(topPage.path)}</div>
              <div className="text-sm text-gray-600">{topPage.path}</div>
            </div>
            <div className="text-right">
              <div className="font-semibold text-gray-900">
                {formatAnalyticsData.formatNumber(topPage.views)}
              </div>
              <div className="text-xs text-gray-500">
                {((topPage.views / totalViews) * 100).toFixed(1)}% of traffic
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Helper function (duplicated from TopPagesTable for standalone use)
const getPageTitle = (path: string): string => {
  const pathTitles: Record<string, string> = {
    '/': 'Home',
    '/services': 'Services',
    '/network-solutions': 'Network Solutions',
    '/network-solutions/national-connectivity': 'National Connectivity',
    '/network-solutions/branch-office': 'Branch Office Connectivity',
    '/network-solutions/managed-internet': 'Managed Enterprise Internet',
    '/about': 'About Us',
    '/contact': 'Contact',
    '/admin': 'Admin Dashboard',
  };

  return pathTitles[path] || path.split('/').pop()?.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || path;
};
