import { useState } from "react";
import { useContentTypes, CONTENT_TYPE_CATEGORIES, FIELD_TYPE_OPTIONS } from "@/hooks/useContentTypes";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Settings,
  FileText,
  Image,
  Type,
  Hash,
  ToggleLeft,
  Calendar,
  Mail,
  Link,
  Palette,
  List,
  Package
} from "lucide-react";
import { toast } from "sonner";

const getFieldTypeIcon = (type: string) => {
  switch (type) {
    case "text": return <Type className="w-4 h-4" />;
    case "richText": return <FileText className="w-4 h-4" />;
    case "number": return <Hash className="w-4 h-4" />;
    case "boolean": return <ToggleLeft className="w-4 h-4" />;
    case "date": return <Calendar className="w-4 h-4" />;
    case "email": return <Mail className="w-4 h-4" />;
    case "url": return <Link className="w-4 h-4" />;
    case "color": return <Palette className="w-4 h-4" />;
    case "image": return <Image className="w-4 h-4" />;
    case "select":
    case "multiSelect": return <List className="w-4 h-4" />;
    case "array":
    case "object": return <Package className="w-4 h-4" />;
    default: return <Type className="w-4 h-4" />;
  }
};

export const ContentTypeManager = () => {
  const { contentTypes, createContentType, updateContentType, deleteContentType, initializePredefined, isLoading } = useContentTypes();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingType, setEditingType] = useState<any>(null);

  const handleInitializePredefined = async () => {
    try {
      const results = await initializePredefined();
      const created = results.filter(r => r.status === "created").length;
      const existing = results.filter(r => r.status === "exists").length;
      
      toast.success(`Initialized ${created} new content types. ${existing} already existed.`);
    } catch (error) {
      toast.error("Failed to initialize predefined content types");
    }
  };

  const handleDeleteContentType = async (id: string) => {
    if (!confirm("Are you sure you want to delete this content type? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteContentType({ id: id as any });
      toast.success("Content type deleted successfully");
    } catch (error) {
      toast.error("Failed to delete content type");
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const groupedTypes = contentTypes?.reduce((acc, type) => {
    const category = type.category || "general";
    if (!acc[category]) acc[category] = [];
    acc[category].push(type);
    return acc;
  }, {} as Record<string, any[]>) || {};

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Content Types</h2>
          <p className="text-gray-600">Manage content type definitions and field structures</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleInitializePredefined}>
            <Settings className="w-4 h-4 mr-2" />
            Initialize Predefined
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create Content Type
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create Content Type</DialogTitle>
              </DialogHeader>
              <ContentTypeForm 
                onSubmit={async (data) => {
                  try {
                    await createContentType(data);
                    setIsCreateDialogOpen(false);
                    toast.success("Content type created successfully");
                  } catch (error) {
                    toast.error("Failed to create content type");
                  }
                }}
                onCancel={() => setIsCreateDialogOpen(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Content Types Grid */}
      <div className="space-y-8">
        {Object.entries(groupedTypes).map(([category, types]) => (
          <div key={category}>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 capitalize">
              {CONTENT_TYPE_CATEGORIES.find(c => c.value === category)?.label || category}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {types.map((type) => (
                <Card key={type._id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg flex items-center gap-2">
                        {type.icon && <span className="text-blue-600">{type.icon}</span>}
                        {type.label}
                      </CardTitle>
                      <div className="flex items-center space-x-1">
                        {type.settings?.isSystem && (
                          <Badge variant="secondary" className="text-xs">
                            System
                          </Badge>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingType(type)}
                          disabled={type.settings?.isSystem}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteContentType(type._id)}
                          disabled={type.settings?.isSystem}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-3">{type.description}</p>
                    <div className="space-y-2">
                      <div className="text-xs text-gray-500">
                        {type.fields.length} field{type.fields.length !== 1 ? 's' : ''}
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {type.fields.slice(0, 4).map((field, index) => (
                          <Badge key={index} variant="outline" className="text-xs flex items-center gap-1">
                            {getFieldTypeIcon(field.type)}
                            {field.label}
                          </Badge>
                        ))}
                        {type.fields.length > 4 && (
                          <Badge variant="outline" className="text-xs">
                            +{type.fields.length - 4} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Edit Dialog */}
      {editingType && (
        <Dialog open={!!editingType} onOpenChange={() => setEditingType(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Content Type</DialogTitle>
            </DialogHeader>
            <ContentTypeForm 
              initialData={editingType}
              onSubmit={async (data) => {
                try {
                  await updateContentType({ id: editingType._id, ...data });
                  setEditingType(null);
                  toast.success("Content type updated successfully");
                } catch (error) {
                  toast.error("Failed to update content type");
                }
              }}
              onCancel={() => setEditingType(null)}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

// Content Type Form Component
const ContentTypeForm = ({ initialData, onSubmit, onCancel }: {
  initialData?: any;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}) => {
  const [formData, setFormData] = useState({
    name: initialData?.name || "",
    label: initialData?.label || "",
    description: initialData?.description || "",
    category: initialData?.category || "general",
    icon: initialData?.icon || "",
    fields: initialData?.fields || [],
    settings: initialData?.settings || {
      allowMultiple: true,
      isSystem: false,
      sortable: true,
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="name">Name (ID)</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="hero_section"
            disabled={!!initialData}
            required
          />
        </div>
        <div>
          <Label htmlFor="label">Display Label</Label>
          <Input
            id="label"
            value={formData.label}
            onChange={(e) => setFormData({ ...formData, label: e.target.value })}
            placeholder="Hero Section"
            required
          />
        </div>
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Describe what this content type is used for"
          required
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="category">Category</Label>
          <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {CONTENT_TYPE_CATEGORIES.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="icon">Icon (Lucide name)</Label>
          <Input
            id="icon"
            value={formData.icon}
            onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
            placeholder="Layout"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {initialData ? "Update" : "Create"} Content Type
        </Button>
      </div>
    </form>
  );
};
