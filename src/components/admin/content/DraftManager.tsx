import { useState } from "react";
import { useVersionedContentManager, formatVersionNumber, getVersionStatus } from "@/hooks/useContentVersioning";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { 
  Save, 
  Eye, 
  Trash2, 
  Calendar,
  Clock,
  FileText,
  AlertCircle,
  CheckCircle,
  GitBranch
} from "lucide-react";
import { toast } from "sonner";

interface DraftManagerProps {
  identifier: string;
  language: string;
  data: any;
  onDataChange: (data: any) => void;
  className?: string;
}

export const DraftManager = ({ 
  identifier, 
  language, 
  data, 
  onDataChange,
  className 
}: DraftManagerProps) => {
  const {
    publishedContent,
    draftContent,
    isLoading,
    isPublished,
    hasDraft,
    hasUnpublishedChanges,
    saveDraft,
    publishContent,
    discardChanges,
    publishedVersion,
    draftVersion,
    lastModified,
  } = useVersionedContentManager(identifier, language);

  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [showScheduleDialog, setShowScheduleDialog] = useState(false);
  const [scheduleData, setScheduleData] = useState({
    date: "",
    time: "",
    note: "",
  });

  const versionStatus = getVersionStatus(publishedVersion, draftVersion);

  const handleSaveDraft = async (changeNote?: string) => {
    setIsSaving(true);
    try {
      await saveDraft(data, changeNote);
      toast.success("Draft saved successfully");
    } catch (error) {
      toast.error("Failed to save draft");
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublishNow = async (publishNote?: string) => {
    setIsPublishing(true);
    try {
      await publishContent(publishNote);
      toast.success("Content published successfully");
    } catch (error) {
      toast.error("Failed to publish content");
      console.error(error);
    } finally {
      setIsPublishing(false);
    }
  };

  const handleSchedulePublish = async () => {
    if (!scheduleData.date || !scheduleData.time) {
      toast.error("Please select a date and time");
      return;
    }

    const scheduledDate = new Date(`${scheduleData.date}T${scheduleData.time}`);
    if (scheduledDate <= new Date()) {
      toast.error("Scheduled time must be in the future");
      return;
    }

    setIsPublishing(true);
    try {
      await publishContent(scheduleData.note, scheduledDate.getTime());
      toast.success("Content scheduled for publication");
      setShowScheduleDialog(false);
      setScheduleData({ date: "", time: "", note: "" });
    } catch (error) {
      toast.error("Failed to schedule publication");
      console.error(error);
    } finally {
      setIsPublishing(false);
    }
  };

  const handleDiscardDraft = async () => {
    try {
      await discardChanges();
      toast.success("Draft discarded");
      // Reset to published data
      if (publishedContent?.data) {
        onDataChange(publishedContent.data);
      }
    } catch (error) {
      toast.error("Failed to discard draft");
      console.error(error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <GitBranch className="w-5 h-5" />
              Draft Management
            </div>
            <div className="flex items-center gap-2">
              {isPublished && (
                <Badge variant="default" className="flex items-center gap-1">
                  <CheckCircle className="w-3 h-3" />
                  Published {formatVersionNumber(publishedVersion || 0)}
                </Badge>
              )}
              {hasDraft && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <FileText className="w-3 h-3" />
                  Draft {formatVersionNumber(draftVersion || 0)}
                </Badge>
              )}
              {hasUnpublishedChanges && (
                <Badge variant="outline" className="flex items-center gap-1 text-orange-600">
                  <AlertCircle className="w-3 h-3" />
                  Unsaved Changes
                </Badge>
              )}
            </div>
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Status Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="space-y-1">
              <span className="font-medium text-gray-600">Status:</span>
              <p className="text-gray-900 capitalize">{versionStatus}</p>
            </div>
            
            {lastModified && (
              <div className="space-y-1">
                <span className="font-medium text-gray-600">Last Modified:</span>
                <p className="text-gray-900 flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  {new Date(lastModified).toLocaleString()}
                </p>
              </div>
            )}

            <div className="space-y-1">
              <span className="font-medium text-gray-600">Versions:</span>
              <div className="flex items-center gap-2">
                {publishedVersion && (
                  <Badge variant="outline" className="text-xs">
                    Published: v{publishedVersion}
                  </Badge>
                )}
                {draftVersion && (
                  <Badge variant="outline" className="text-xs">
                    Draft: v{draftVersion}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-wrap gap-2">
            {/* Save Draft */}
            <SaveDraftDialog onSave={handleSaveDraft} disabled={isSaving} />

            {/* Publish Actions */}
            {hasDraft && (
              <>
                <PublishNowDialog onPublish={handlePublishNow} disabled={isPublishing} />
                
                <Dialog open={showScheduleDialog} onOpenChange={setShowScheduleDialog}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" disabled={isPublishing}>
                      <Calendar className="w-4 h-4 mr-2" />
                      Schedule
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Schedule Publication</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="schedule-date">Date</Label>
                          <Input
                            id="schedule-date"
                            type="date"
                            value={scheduleData.date}
                            onChange={(e) => setScheduleData(prev => ({ ...prev, date: e.target.value }))}
                            min={new Date().toISOString().split('T')[0]}
                          />
                        </div>
                        <div>
                          <Label htmlFor="schedule-time">Time</Label>
                          <Input
                            id="schedule-time"
                            type="time"
                            value={scheduleData.time}
                            onChange={(e) => setScheduleData(prev => ({ ...prev, time: e.target.value }))}
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="schedule-note">Publication Note (Optional)</Label>
                        <Textarea
                          id="schedule-note"
                          value={scheduleData.note}
                          onChange={(e) => setScheduleData(prev => ({ ...prev, note: e.target.value }))}
                          placeholder="Add a note about this scheduled publication..."
                          rows={3}
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setShowScheduleDialog(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleSchedulePublish} disabled={isPublishing}>
                        {isPublishing ? "Scheduling..." : "Schedule Publication"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </>
            )}

            {/* Discard Draft */}
            {hasDraft && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Discard Draft
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Discard Draft</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to discard this draft? All unsaved changes will be lost.
                      This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDiscardDraft} className="bg-red-600 hover:bg-red-700">
                      Discard Draft
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>

          {/* Draft vs Published Comparison */}
          {hasDraft && isPublished && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start gap-2">
                <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-blue-900">You have unpublished changes</p>
                  <p className="text-blue-700 mt-1">
                    Your draft (v{draftVersion}) contains changes that are not yet published. 
                    The current published version is v{publishedVersion}.
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Save Draft Dialog
const SaveDraftDialog = ({ onSave, disabled }: { onSave: (note?: string) => void; disabled: boolean }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [changeNote, setChangeNote] = useState("");

  const handleSave = () => {
    onSave(changeNote || undefined);
    setIsOpen(false);
    setChangeNote("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button size="sm" disabled={disabled}>
          <Save className="w-4 h-4 mr-2" />
          {disabled ? "Saving..." : "Save Draft"}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Save Draft</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="change-note">Change Note (Optional)</Label>
            <Textarea
              id="change-note"
              value={changeNote}
              onChange={(e) => setChangeNote(e.target.value)}
              placeholder="Describe what changes you made..."
              rows={3}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Draft
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Publish Now Dialog
const PublishNowDialog = ({ onPublish, disabled }: { onPublish: (note?: string) => void; disabled: boolean }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [publishNote, setPublishNote] = useState("");

  const handlePublish = () => {
    onPublish(publishNote || undefined);
    setIsOpen(false);
    setPublishNote("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button size="sm" disabled={disabled}>
          <Eye className="w-4 h-4 mr-2" />
          {disabled ? "Publishing..." : "Publish Now"}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Publish Content</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="publish-note">Publication Note (Optional)</Label>
            <Textarea
              id="publish-note"
              value={publishNote}
              onChange={(e) => setPublishNote(e.target.value)}
              placeholder="Add a note about this publication..."
              rows={3}
            />
          </div>
          <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="flex items-start gap-2">
              <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">This will make your changes live</p>
                <p className="mt-1">
                  Publishing will make your draft content visible to all website visitors immediately.
                </p>
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handlePublish}>
            Publish Now
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
