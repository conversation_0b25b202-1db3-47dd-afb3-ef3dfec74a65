import { useState } from "react";
import { useAllContent, useContentStats } from "@/hooks/useContent";
import { useScheduledContent } from "@/hooks/useContentVersioning";
import { useAuth } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { 
  FileText, 
  Plus, 
  Clock,
  CheckCircle,
  AlertCircle,
  Globe,
  TrendingUp,
  Calendar,
  Eye,
  Edit,
  BarChart3
} from "lucide-react";
import { Link } from "react-router-dom";
import { formatTimeAgo } from "@/hooks/useAdminRoles";

export const ContentOverview = () => {
  const { canEditContent } = useAuth();
  const { content: allContent, isLoading } = useAllContent();
  const { stats } = useContentStats();
  const { scheduled } = useScheduledContent(10);

  // Get recent content
  const recentContent = allContent?.slice(0, 10) || [];
  const draftContent = allContent?.filter(c => c.status === "draft").slice(0, 5) || [];
  const publishedContent = allContent?.filter(c => c.status === "published").slice(0, 5) || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Content Overview</h2>
          <p className="text-gray-600">
            Monitor and manage all your website content
          </p>
        </div>
        
        {canEditContent && (
          <div className="flex space-x-2">
            <Link to="/admin/content">
              <Button variant="outline">
                <FileText className="w-4 h-4 mr-2" />
                All Content
              </Button>
            </Link>
            <Link to="/admin/content/new">
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create Content
              </Button>
            </Link>
          </div>
        )}
      </div>

      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <StatCard
            title="Total Content"
            value={stats.total}
            icon={FileText}
            color="blue"
            description="All content items"
          />
          <StatCard
            title="Published"
            value={stats.published}
            icon={CheckCircle}
            color="green"
            description="Live on website"
          />
          <StatCard
            title="Drafts"
            value={stats.draft}
            icon={Clock}
            color="yellow"
            description="Work in progress"
          />
          <StatCard
            title="Languages"
            value={Object.keys(stats.byLanguage).length}
            icon={Globe}
            color="purple"
            description="Supported languages"
          />
        </div>
      )}

      {/* Content Tabs */}
      <Tabs defaultValue="recent" className="space-y-4">
        <TabsList>
          <TabsTrigger value="recent">Recent Activity</TabsTrigger>
          <TabsTrigger value="drafts">Drafts</TabsTrigger>
          <TabsTrigger value="published">Published</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Recent Content Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              {recentContent.length > 0 ? (
                <div className="space-y-3">
                  {recentContent.map((item) => (
                    <ContentListItem key={`${item.identifier}-${item.language}`} content={item} />
                  ))}
                </div>
              ) : (
                <EmptyState
                  icon={FileText}
                  title="No content yet"
                  description="Start by creating your first piece of content"
                  action={
                    canEditContent && (
                      <Link to="/admin/content/new">
                        <Button>
                          <Plus className="w-4 h-4 mr-2" />
                          Create Content
                        </Button>
                      </Link>
                    )
                  }
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="drafts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Draft Content
              </CardTitle>
            </CardHeader>
            <CardContent>
              {draftContent.length > 0 ? (
                <div className="space-y-3">
                  {draftContent.map((item) => (
                    <ContentListItem key={`${item.identifier}-${item.language}`} content={item} />
                  ))}
                  {draftContent.length >= 5 && (
                    <Link to="/admin/content?status=draft">
                      <Button variant="outline" size="sm" className="w-full">
                        View All Drafts
                      </Button>
                    </Link>
                  )}
                </div>
              ) : (
                <EmptyState
                  icon={Clock}
                  title="No drafts"
                  description="All your content is published or you haven't started any drafts yet"
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="published" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                Published Content
              </CardTitle>
            </CardHeader>
            <CardContent>
              {publishedContent.length > 0 ? (
                <div className="space-y-3">
                  {publishedContent.map((item) => (
                    <ContentListItem key={`${item.identifier}-${item.language}`} content={item} />
                  ))}
                  {publishedContent.length >= 5 && (
                    <Link to="/admin/content?status=published">
                      <Button variant="outline" size="sm" className="w-full">
                        View All Published
                      </Button>
                    </Link>
                  )}
                </div>
              ) : (
                <EmptyState
                  icon={CheckCircle}
                  title="No published content"
                  description="Publish some content to make it live on your website"
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Scheduled Content
              </CardTitle>
            </CardHeader>
            <CardContent>
              {scheduled && scheduled.length > 0 ? (
                <div className="space-y-3">
                  {scheduled.map((item) => (
                    <ScheduledContentItem key={item._id} content={item} />
                  ))}
                </div>
              ) : (
                <EmptyState
                  icon={Calendar}
                  title="No scheduled content"
                  description="Schedule content to be published automatically at a future date"
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Content Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              {stats && (
                <div className="space-y-6">
                  {/* Language Distribution */}
                  <div>
                    <h4 className="font-medium mb-3">Content by Language</h4>
                    <div className="space-y-2">
                      {Object.entries(stats.byLanguage).map(([language, count]) => (
                        <div key={language} className="flex items-center justify-between">
                          <span className="text-sm capitalize">
                            {language === 'en' ? 'English' : language === 'es' ? 'Spanish' : language}
                          </span>
                          <div className="flex items-center space-x-2">
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full" 
                                style={{ width: `${(count / stats.total) * 100}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-medium">{count}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Status Distribution */}
                  <div>
                    <h4 className="font-medium mb-3">Content Status</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <p className="text-2xl font-bold text-green-600">{stats.published}</p>
                        <p className="text-sm text-green-700">Published</p>
                      </div>
                      <div className="text-center p-3 bg-yellow-50 rounded-lg">
                        <p className="text-2xl font-bold text-yellow-600">{stats.draft}</p>
                        <p className="text-sm text-yellow-700">Drafts</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Content List Item Component
const ContentListItem = ({ content }: { content: any }) => {
  const getContentTitle = (data: any) => {
    return data.title || data.heading || data.name || "Untitled Content";
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "published":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
          <FileText className="w-4 h-4 text-blue-600" />
        </div>
        <div>
          <p className="font-medium text-gray-900">{getContentTitle(content.data)}</p>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <span>{content.identifier}</span>
            <Badge variant="outline" className="text-xs">
              {content.language.toUpperCase()}
            </Badge>
            <Badge className={`text-xs ${getStatusColor(content.status)}`}>
              {content.status}
            </Badge>
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <span className="text-xs text-gray-500">
          {formatTimeAgo(content.updatedAt)}
        </span>
        <Link to={`/admin/content/edit/${content.identifier}?lang=${content.language}`}>
          <Button variant="ghost" size="sm">
            <Edit className="w-4 h-4" />
          </Button>
        </Link>
      </div>
    </div>
  );
};

// Scheduled Content Item Component
const ScheduledContentItem = ({ content }: { content: any }) => {
  return (
    <div className="flex items-center justify-between p-3 border rounded-lg">
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
          <Calendar className="w-4 h-4 text-orange-600" />
        </div>
        <div>
          <p className="font-medium text-gray-900">{content.identifier}</p>
          <p className="text-sm text-gray-500">
            Scheduled for {new Date(content.scheduledPublishAt).toLocaleString()}
          </p>
        </div>
      </div>
      <Badge variant="outline" className="text-orange-600">
        Scheduled
      </Badge>
    </div>
  );
};

// Stat Card Component
const StatCard = ({ 
  title, 
  value, 
  icon: Icon, 
  color, 
  description 
}: {
  title: string;
  value: number;
  icon: any;
  color: string;
  description: string;
}) => {
  const colorClasses = {
    blue: "text-blue-600 bg-blue-100",
    green: "text-green-600 bg-green-100",
    yellow: "text-yellow-600 bg-yellow-100",
    purple: "text-purple-600 bg-purple-100",
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            <p className="text-xs text-gray-500">{description}</p>
          </div>
          <div className={`p-2 rounded-full ${colorClasses[color as keyof typeof colorClasses]}`}>
            <Icon className="w-5 h-5" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Empty State Component
const EmptyState = ({ 
  icon: Icon, 
  title, 
  description, 
  action 
}: {
  icon: any;
  title: string;
  description: string;
  action?: React.ReactNode;
}) => (
  <div className="text-center py-8">
    <Icon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
    <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-500 mb-4">{description}</p>
    {action}
  </div>
);
