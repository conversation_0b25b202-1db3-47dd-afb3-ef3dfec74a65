import { useState } from "react";
import { useAllContent, useContentStats } from "@/hooks/useContent";
import { useAuth } from "@/hooks/useAuth";
import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  FileText,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Eye,
  Copy,
  Trash2,
  Globe,
  Clock,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  TrendingUp,
  Home,
  Briefcase,
  Users,
  Phone,
  Network,
  GraduationCap
} from "lucide-react";
import { Link } from "react-router-dom";
import { formatTimeAgo } from "@/hooks/useAdminRoles";
import { ContentEditModal } from "./ContentEditModal";
import { ContentPreviewModal } from "./ContentPreviewModal";
import { ContentSetupButton } from "../setup/ContentSetupButton";
import { NetworkTrainingSetup } from "../NetworkTrainingSetup";
import { useDuplicateContent } from "@/hooks/useContent";
import { Id } from "../../../../convex/_generated/dataModel";

// Page configuration for organizing content
const PAGE_CONFIGS = {
  home: {
    label: "Home",
    icon: Home,
    sections: [
      "hero",
      "home-hero",
      "services",
      "about-intro",
      "testimonials",
      "contact",
      "cta-section"
    ]
  },
  services: {
    label: "Services",
    icon: Briefcase,
    sections: [
      "services-hero",
      "services-banner",
      "network-solutions-detail",
      "domestic-services-detail",
      "training-programs-detail",
      "service-cybersecurity",
      "service-network",
      "service-surveillance",
      "service-training"
    ]
  },
  about: {
    label: "About",
    icon: Users,
    sections: [
      "about-hero",
      "value-mission",
      "value-excellence",
      "value-partnership",
      "value-reliability",
      "timeline-2009",
      "timeline-2012",
      "timeline-2016",
      "timeline-2020",
      "timeline-2024"
    ]
  },
  contact: {
    label: "Contact",
    icon: Phone,
    sections: [
      "contact-hero",
      "contact-info",
      "office-locations"
    ]
  },
  network: {
    label: "Network Solutions",
    icon: Network,
    sections: [
      "network-solutions-hero",
      "network-international-connectivity",
      "network-national-connectivity",
      "network-branch-office"
    ]
  },
  training: {
    label: "Training",
    icon: GraduationCap,
    sections: [
      "training-programs-hero",
      "training-corporate-pc-skills",
      "training-leadership-development",
      "training-time-management"
    ]
  }
};

export const ContentManagement = () => {
  const { canEditContent, canPublishContent, canDeleteContent } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [languageFilter, setLanguageFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [activePageTab, setActivePageTab] = useState<string>("all");

  // Modal states
  const [editingContentId, setEditingContentId] = useState<Id<"content"> | null>(null);
  const [editingContentIdentifier, setEditingContentIdentifier] = useState<string | null>(null);
  const [editingContentVersions, setEditingContentVersions] = useState<any[] | null>(null);
  const [previewingContentId, setPreviewingContentId] = useState<Id<"content"> | null>(null);

  const { content, isLoading } = useAllContent({
    status: statusFilter === "all" ? undefined : statusFilter as any,
    language: languageFilter === "all" ? undefined : languageFilter,
  });

  const { stats } = useContentStats();
  const { hasDuplicates, duplicateCount } = useDuplicateContent();

  // Debug queries
  const networkSolutionContent = useQuery(api.content.debugContentByType, { contentTypeName: "network_solution" });
  const trainingProgramContent = useQuery(api.content.debugContentByType, { contentTypeName: "training_program" });

  // Debug logging
  console.log('=== CONTENT MANAGEMENT DEBUG ===');
  console.log('Network Solution Content:', networkSolutionContent);
  console.log('Training Program Content:', trainingProgramContent);
  console.log('All Content:', content);
  console.log('================================');

  // Handlers for editing content
  const handleEditContent = (identifierOrId: string | Id<"content">, contentVersions?: any[]) => {
    if (contentVersions) {
      // Multi-language editing
      setEditingContentIdentifier(identifierOrId as string);
      setEditingContentVersions(contentVersions);
      setEditingContentId(null);
    } else {
      // Single content editing (fallback)
      setEditingContentId(identifierOrId as Id<"content">);
      setEditingContentIdentifier(null);
      setEditingContentVersions(null);
    }
  };

  const handleCloseEditModal = () => {
    setEditingContentId(null);
    setEditingContentIdentifier(null);
    setEditingContentVersions(null);
  };

  // Filter content based on search, filters, and page
  const filteredContent = content?.filter(item => {
    const matchesSearch = searchQuery === "" ||
      item.identifier.toLowerCase().includes(searchQuery.toLowerCase()) ||
      JSON.stringify(item.data).toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = typeFilter === "all" || item.contentType?.name === typeFilter;

    // Filter by page if a specific page is selected
    const matchesPage = activePageTab === "all" || (() => {
      const pageConfig = PAGE_CONFIGS[activePageTab as keyof typeof PAGE_CONFIGS];
      if (!pageConfig) return false;

      // For network and training pages, match by content type instead of exact identifier
      if (activePageTab === "network" && item.contentType?.name === "network_solution") {
        return true;
      }
      if (activePageTab === "training" && item.contentType?.name === "training_program") {
        return true;
      }

      // For other pages, match by identifier
      return pageConfig.sections.includes(item.identifier);
    })();

    return matchesSearch && matchesType && matchesPage;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Content Management</h2>
          <p className="text-gray-600">
            Manage all website content, pages, and sections
          </p>
        </div>
        
        {canEditContent && (
          <div className="flex space-x-2">
            <Link to="/admin/content-types">
              <Button variant="outline">
                <FileText className="w-4 h-4 mr-2" />
                Content Types
              </Button>
            </Link>
          </div>
        )}
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <StatCard
            title="Total Content"
            value={stats.total}
            icon={FileText}
            color="blue"
          />
          <StatCard
            title="Published"
            value={stats.published}
            icon={CheckCircle}
            color="green"
          />
          <StatCard
            title="Drafts"
            value={stats.draft}
            icon={Clock}
            color="yellow"
          />
          <StatCard
            title="Languages"
            value={Object.keys(stats.byLanguage).length}
            icon={Globe}
            color="purple"
          />
        </div>
      )}

      {/* Duplicate Content Alert */}
      {hasDuplicates && (
        <Alert className="border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertTitle className="text-orange-800">Duplicate Content Detected</AlertTitle>
          <AlertDescription className="text-orange-700">
            Found {duplicateCount} duplicate content entries. Use the "Manage Duplicates" tab in the content setup section below to clean them up.
          </AlertDescription>
        </Alert>
      )}

      {/* Content Setup Button */}
      {canEditContent && (
        <div className="flex justify-center">
          <ContentSetupButton />
        </div>
      )}

      {/* Network & Training Setup */}
      {canEditContent && (
        <div className="mt-8">
          <NetworkTrainingSetup />
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>

            <Select value={languageFilter} onValueChange={setLanguageFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Languages</SelectItem>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Content Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="Hero Section">Hero Section</SelectItem>
                <SelectItem value="Service Card">Service Card</SelectItem>
                <SelectItem value="Network Solution">Network Solution</SelectItem>
                <SelectItem value="Training Program">Training Program</SelectItem>
                <SelectItem value="Value Card">Value Card</SelectItem>
                <SelectItem value="Timeline Item">Timeline Item</SelectItem>
                <SelectItem value="Testimonial">Testimonial</SelectItem>
                <SelectItem value="Page Content">Page Content</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Content Organization by Pages */}
      <Tabs value={activePageTab} onValueChange={setActivePageTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            All Content
          </TabsTrigger>
          {Object.entries(PAGE_CONFIGS).map(([key, config]) => {
            const Icon = config.icon;
            return (
              <TabsTrigger key={key} value={key} className="flex items-center gap-2">
                <Icon className="w-4 h-4" />
                {config.label}
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value="all">
          <Card>
            <CardHeader>
              <CardTitle>All Content Items ({filteredContent?.length || 0})</CardTitle>
            </CardHeader>
            <CardContent>
              <ContentList
                content={filteredContent}
                canEdit={canEditContent}
                canPublish={canPublishContent}
                canDelete={canDeleteContent}
                onEdit={handleEditContent}
                onPreview={setPreviewingContentId}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {Object.entries(PAGE_CONFIGS).map(([pageKey, pageConfig]) => (
          <TabsContent key={pageKey} value={pageKey}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <pageConfig.icon className="w-5 h-5" />
                  {pageConfig.label} Page Content
                </CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Manage content sections for the {pageConfig.label.toLowerCase()} page
                </p>
              </CardHeader>
              <CardContent>
                <ContentList
                  content={filteredContent}
                  canEdit={canEditContent}
                  canPublish={canPublishContent}
                  canDelete={canDeleteContent}
                  onEdit={handleEditContent}
                  onPreview={setPreviewingContentId}
                  pageContext={pageKey}
                />
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Modals */}
      <ContentEditModal
        contentId={editingContentId}
        contentIdentifier={editingContentIdentifier}
        contentVersions={editingContentVersions}
        isOpen={!!(editingContentId || editingContentIdentifier)}
        onClose={handleCloseEditModal}
        onSave={handleCloseEditModal}
      />

      <ContentPreviewModal
        contentId={previewingContentId}
        isOpen={!!previewingContentId}
        onClose={() => setPreviewingContentId(null)}
      />
    </div>
  );
};

// Individual content item component
const ContentItem = ({
  content,
  canEdit,
  canPublish,
  canDelete,
  onEdit,
  onPreview,
}: {
  content: any;
  canEdit: boolean;
  canPublish: boolean;
  canDelete: boolean;
  onEdit: (contentId: Id<"content">) => void;
  onPreview: (contentId: Id<"content">) => void;
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "published":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getContentPreview = (data: any) => {
    if (data.title) return data.title;
    if (data.heading) return data.heading;
    if (data.name) return data.name;
    if (data.text) return data.text.substring(0, 50) + "...";
    return "Untitled Content";
  };

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
      <div className="flex items-center space-x-4 flex-1">
        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
          <FileText className="w-5 h-5 text-blue-600" />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h3 className="font-medium text-gray-900 truncate">
              {getContentPreview(content.data)}
            </h3>
            <Badge className={getStatusColor(content.status)}>
              {content.status}
            </Badge>
            <Badge variant="outline" className="text-xs">
              {content.language.toUpperCase()}
            </Badge>
          </div>
          
          <p className="text-sm text-gray-600 truncate">
            {content.identifier} • {content.contentType?.name}
          </p>
          
          <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
            <span>v{content.version}</span>
            <span>Updated {formatTimeAgo(content.updatedAt)}</span>
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        {canEdit && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              
              <DropdownMenuItem onClick={() => onEdit(content._id)}>
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </DropdownMenuItem>

              <DropdownMenuItem onClick={() => onPreview(content._id)}>
                <Eye className="w-4 h-4 mr-2" />
                Preview
              </DropdownMenuItem>
              
              <DropdownMenuItem>
                <Copy className="w-4 h-4 mr-2" />
                Duplicate
              </DropdownMenuItem>
              
              {canPublish && content.status === "draft" && (
                <DropdownMenuItem>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Publish
                </DropdownMenuItem>
              )}
              
              {canDelete && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-red-600">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </div>
  );
};

// Combined Content Item Component (groups multiple language versions)
interface CombinedContentItemProps {
  identifier: string;
  contentVersions: any[];
  canEdit: boolean;
  canPublish: boolean;
  canDelete: boolean;
  onEdit: (identifier: string, contentVersions: any[]) => void;
  onPreview: (contentId: Id<"content">) => void;
}

const CombinedContentItem = ({
  identifier,
  contentVersions,
  canEdit,
  onEdit,
}: CombinedContentItemProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "published":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getContentPreview = (data: any) => {
    if (data.title) return data.title;
    if (data.heading) return data.heading;
    if (data.name) return data.name;
    if (data.text) return data.text.substring(0, 50) + "...";
    return "Untitled Content";
  };

  const handleClick = () => {
    if (canEdit && contentVersions.length > 0) {
      // Open edit modal with the identifier so it can load all language versions
      onEdit(identifier, contentVersions);
    }
  };

  if (contentVersions.length === 0) return null;

  // Sort versions by language (English first, then Spanish)
  const sortedVersions = [...contentVersions].sort((a, b) => {
    if (a.language === "en") return -1;
    if (b.language === "en") return 1;
    return a.language.localeCompare(b.language);
  });

  return (
    <div
      className="border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
      onClick={handleClick}
    >
      {sortedVersions.map((content, index) => (
        <div
          key={`${content.identifier}-${content.language}`}
          className={`flex items-center justify-between p-4 ${
            index < sortedVersions.length - 1 ? 'border-b border-gray-100' : ''
          }`}
        >
          <div className="flex items-center space-x-4 flex-1">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-blue-600" />
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="font-medium text-gray-900 truncate">
                  {getContentPreview(content.data)}
                </h3>
                <Badge className={getStatusColor(content.status)}>
                  {content.status}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {content.language === "en" ? "🇺🇸 EN" : "🇪🇸 ES"}
                </Badge>
              </div>

              <p className="text-sm text-gray-600 truncate">
                {content.identifier} • {content.contentType?.name}
              </p>

              <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                <span>v{content.version}</span>
                <span>Updated {formatTimeAgo(content.updatedAt)}</span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Stat Card Component
const StatCard = ({ 
  title, 
  value, 
  icon: Icon, 
  color 
}: {
  title: string;
  value: number;
  icon: any;
  color: string;
}) => {
  const colorClasses = {
    blue: "text-blue-600 bg-blue-100",
    green: "text-green-600 bg-green-100",
    yellow: "text-yellow-600 bg-yellow-100",
    purple: "text-purple-600 bg-purple-100",
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
          </div>
          <div className={`p-2 rounded-full ${colorClasses[color as keyof typeof colorClasses]}`}>
            <Icon className="w-5 h-5" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Content List Component
interface ContentListProps {
  content: any[] | undefined;
  canEdit: boolean;
  canPublish: boolean;
  canDelete: boolean;
  onEdit: (identifierOrId: string | Id<"content">, contentVersions?: any[]) => void;
  onPreview: (contentId: Id<"content">) => void;
  pageContext?: string;
}

const ContentList = ({
  content,
  canEdit,
  canPublish,
  canDelete,
  onEdit,
  onPreview,
  pageContext
}: ContentListProps) => {
  if (!content || content.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <p>
          {pageContext
            ? `No content found for the ${PAGE_CONFIGS[pageContext as keyof typeof PAGE_CONFIGS]?.label} page`
            : "No content found matching your criteria"
          }
        </p>
      </div>
    );
  }

  // Group content by identifier to combine language versions
  const groupedByIdentifier = content.reduce((acc, item) => {
    if (!acc[item.identifier]) {
      acc[item.identifier] = [];
    }
    acc[item.identifier].push(item);
    return acc;
  }, {} as Record<string, any[]>);

  // Group content by section for page-specific views
  const groupedContent = pageContext ?
    PAGE_CONFIGS[pageContext as keyof typeof PAGE_CONFIGS]?.sections.map(sectionId => {
      const sectionContent = groupedByIdentifier[sectionId] || [];
      return {
        sectionId,
        content: sectionContent
      };
    }).filter(section => section.content.length > 0) : null;

  if (pageContext && groupedContent) {
    return (
      <div className="space-y-6">
        {groupedContent.map(({ sectionId, content: sectionContent }) => (
          <div key={sectionId} className="border rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-3 capitalize">
              {sectionId.replace(/-/g, ' ').replace(/_/g, ' ')} Section
            </h3>
            <div className="space-y-3">
              <CombinedContentItem
                identifier={sectionId}
                contentVersions={sectionContent}
                canEdit={canEdit}
                canPublish={canPublish}
                canDelete={canDelete}
                onEdit={onEdit}
                onPreview={onPreview}
              />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {Object.entries(groupedByIdentifier).map(([identifier, versions]) => (
        <CombinedContentItem
          key={identifier}
          identifier={identifier}
          contentVersions={versions}
          canEdit={canEdit}
          canPublish={canPublish}
          canDelete={canDelete}
          onEdit={onEdit}
          onPreview={onPreview}
        />
      ))}
    </div>
  );
};
