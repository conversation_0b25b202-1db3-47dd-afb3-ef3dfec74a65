import { useState, useEffect } from "react";
import { useFooterContentManager } from "@/hooks/useFooterContent";
import { useContentMutations } from "@/hooks/useContent";
import { useContentType } from "@/hooks/useContentTypes";
import { useLanguage } from "@/hooks/useLanguage";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Save, 
  Eye, 
  EyeOff, 
  Globe,
  AlertCircle,
  CheckCircle,
  Plus,
  Trash2
} from "lucide-react";
import { toast } from "sonner";
import { RichTextEditor } from "./RichTextEditor";

interface SocialLink {
  name: string;
  url: string;
}

export const FooterContentManager = () => {
  const { language } = useLanguage();
  const [selectedLanguage, setSelectedLanguage] = useState(language);
  const { content, isLoading, hasContent, defaultContent } = useFooterContentManager(selectedLanguage);
  const { contentType } = useContentType("footer_content");
  const mutations = useContentMutations();
  
  const [formData, setFormData] = useState<any>({});
  const [socialLinks, setSocialLinks] = useState<SocialLink[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data
  useEffect(() => {
    if (content?.data) {
      setFormData(content.data);
      setSocialLinks(content.data.socialLinks || []);
    } else if (defaultContent) {
      setFormData(defaultContent);
      setSocialLinks(defaultContent.socialLinks || []);
    }
  }, [content, defaultContent]);

  const handleFieldChange = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSocialLinkChange = (index: number, field: 'name' | 'url', value: string) => {
    const updatedLinks = [...socialLinks];
    updatedLinks[index] = { ...updatedLinks[index], [field]: value };
    setSocialLinks(updatedLinks);
    handleFieldChange('socialLinks', updatedLinks);
  };

  const addSocialLink = () => {
    const newLinks = [...socialLinks, { name: '', url: '' }];
    setSocialLinks(newLinks);
    handleFieldChange('socialLinks', newLinks);
  };

  const removeSocialLink = (index: number) => {
    const updatedLinks = socialLinks.filter((_, i) => i !== index);
    setSocialLinks(updatedLinks);
    handleFieldChange('socialLinks', updatedLinks);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.companyName?.trim()) {
      newErrors.companyName = "Company name is required";
    }
    
    if (!formData.companyDescription?.trim()) {
      newErrors.companyDescription = "Company description is required";
    }

    // Validate social links
    socialLinks.forEach((link, index) => {
      if (link.name && !link.url) {
        newErrors[`socialLink_${index}_url`] = "URL is required when name is provided";
      }
      if (link.url && !link.name) {
        newErrors[`socialLink_${index}_name`] = "Name is required when URL is provided";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async (shouldPublish = false) => {
    if (!validateForm()) {
      toast.error("Please fix the validation errors");
      return;
    }

    if (!contentType) {
      toast.error("Content type not found");
      return;
    }

    setIsSaving(true);
    try {
      const saveData = {
        ...formData,
        socialLinks: socialLinks.filter(link => link.name && link.url)
      };

      await mutations.upsertContent({
        identifier: "footer-content",
        language: selectedLanguage,
        data: saveData,
        contentTypeId: contentType._id,
        status: shouldPublish ? "published" : "draft",
      });

      toast.success(`Footer content ${shouldPublish ? "published" : "saved as draft"} successfully`);
    } catch (error) {
      console.error("Error saving footer content:", error);
      toast.error("Failed to save footer content");
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const hasErrors = Object.keys(errors).length > 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Footer Content</h2>
          <p className="text-gray-600">
            Manage footer information displayed across all pages
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Language Selector */}
          <div className="flex items-center space-x-2">
            <Globe className="w-4 h-4 text-gray-500" />
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">EN</SelectItem>
                <SelectItem value="es">ES</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Status Badge */}
          <Badge variant={content?.status === "published" ? "default" : "secondary"}>
            {content?.status || "draft"}
          </Badge>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center space-x-2">
        <Button
          onClick={() => handleSave(false)}
          disabled={isSaving}
          variant="outline"
        >
          <Save className="w-4 h-4 mr-2" />
          {isSaving ? "Saving..." : "Save Draft"}
        </Button>
        
        <Button
          onClick={() => handleSave(true)}
          disabled={isSaving}
        >
          <Eye className="w-4 h-4 mr-2" />
          {isSaving ? "Publishing..." : "Publish"}
        </Button>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Footer Content Fields
            {hasErrors && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                {Object.keys(errors).length} error{Object.keys(errors).length !== 1 ? 's' : ''}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Company Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Company Information</h3>
            
            <div>
              <Label htmlFor="companyName">Company Name *</Label>
              <Input
                id="companyName"
                value={formData.companyName || ''}
                onChange={(e) => handleFieldChange('companyName', e.target.value)}
                placeholder="Enter company name"
                className={errors.companyName ? "border-red-500" : ""}
              />
              {errors.companyName && (
                <p className="text-sm text-red-500 mt-1">{errors.companyName}</p>
              )}
            </div>

            <div>
              <Label htmlFor="companyDescription">Company Description *</Label>
              <RichTextEditor
                value={formData.companyDescription || ''}
                onChange={(value) => handleFieldChange('companyDescription', value)}
                placeholder="Enter company description"
                className={errors.companyDescription ? "border-red-500" : ""}
              />
              {errors.companyDescription && (
                <p className="text-sm text-red-500 mt-1">{errors.companyDescription}</p>
              )}
            </div>
          </div>

          <Separator />

          {/* CTA Button */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Call-to-Action Button</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="ctaButtonText">Button Text</Label>
                <Input
                  id="ctaButtonText"
                  value={formData.ctaButtonText || ''}
                  onChange={(e) => handleFieldChange('ctaButtonText', e.target.value)}
                  placeholder="Get Started"
                />
              </div>
              
              <div>
                <Label htmlFor="ctaButtonLink">Button Link</Label>
                <Input
                  id="ctaButtonLink"
                  value={formData.ctaButtonLink || ''}
                  onChange={(e) => handleFieldChange('ctaButtonLink', e.target.value)}
                  placeholder="/contact"
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Section Titles */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Section Titles</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="quickLinksTitle">Quick Links Title</Label>
                <Input
                  id="quickLinksTitle"
                  value={formData.quickLinksTitle || ''}
                  onChange={(e) => handleFieldChange('quickLinksTitle', e.target.value)}
                  placeholder="Quick Links"
                />
              </div>
              
              <div>
                <Label htmlFor="contactTitle">Contact Title</Label>
                <Input
                  id="contactTitle"
                  value={formData.contactTitle || ''}
                  onChange={(e) => handleFieldChange('contactTitle', e.target.value)}
                  placeholder="Contact"
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Contact Information</h3>
            
            <div>
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                value={formData.address || ''}
                onChange={(e) => handleFieldChange('address', e.target.value)}
                placeholder="Malabo, Equatorial Guinea"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  value={formData.phone || ''}
                  onChange={(e) => handleFieldChange('phone', e.target.value)}
                  placeholder="+240 XXX XXX XXX"
                />
              </div>
              
              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email || ''}
                  onChange={(e) => handleFieldChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Social Links */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Social Media Links</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addSocialLink}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Link
              </Button>
            </div>
            
            {socialLinks.map((link, index) => (
              <div key={index} className="flex items-end space-x-2">
                <div className="flex-1">
                  <Label htmlFor={`socialName_${index}`}>Name</Label>
                  <Input
                    id={`socialName_${index}`}
                    value={link.name}
                    onChange={(e) => handleSocialLinkChange(index, 'name', e.target.value)}
                    placeholder="Facebook"
                    className={errors[`socialLink_${index}_name`] ? "border-red-500" : ""}
                  />
                  {errors[`socialLink_${index}_name`] && (
                    <p className="text-sm text-red-500 mt-1">{errors[`socialLink_${index}_name`]}</p>
                  )}
                </div>
                
                <div className="flex-1">
                  <Label htmlFor={`socialUrl_${index}`}>URL</Label>
                  <Input
                    id={`socialUrl_${index}`}
                    value={link.url}
                    onChange={(e) => handleSocialLinkChange(index, 'url', e.target.value)}
                    placeholder="https://facebook.com/company"
                    className={errors[`socialLink_${index}_url`] ? "border-red-500" : ""}
                  />
                  {errors[`socialLink_${index}_url`] && (
                    <p className="text-sm text-red-500 mt-1">{errors[`socialLink_${index}_url`]}</p>
                  )}
                </div>
                
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeSocialLink(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            ))}
          </div>

          <Separator />

          {/* Copyright */}
          <div>
            <Label htmlFor="copyrightText">Copyright Text</Label>
            <Input
              id="copyrightText"
              value={formData.copyrightText || ''}
              onChange={(e) => handleFieldChange('copyrightText', e.target.value)}
              placeholder="© 2024 OfficeTech Guinea. All rights reserved."
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
