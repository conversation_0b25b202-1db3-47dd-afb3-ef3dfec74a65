import { useState } from "react";
import { useAllContent, useContentSearch } from "@/hooks/useContent";
import { useContentTypes } from "@/hooks/useContentTypes";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Eye, 
  EyeOff, 
  Copy, 
  Trash2,
  MoreHorizontal,
  Globe,
  Calendar
} from "lucide-react";
import { getContentPreview } from "@/hooks/useContent";

interface ContentListProps {
  onEditContent?: (identifier: string, contentType: string, language: string) => void;
  onCreateContent?: (contentType: string) => void;
}

export const ContentList = ({ onEditContent, onCreateContent }: ContentListProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState<string>("all");
  const [selectedContentType, setSelectedContentType] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<"draft" | "published" | "all">("all");

  const { contentTypes } = useContentTypes();
  const { content, isLoading } = useAllContent({
    language: selectedLanguage === "all" ? undefined : selectedLanguage,
    status: selectedStatus === "all" ? undefined : selectedStatus,
    limit: 50,
  });

  const { results: searchResults } = useContentSearch(searchQuery, {
    language: selectedLanguage === "all" ? undefined : selectedLanguage,
    contentType: selectedContentType === "all" ? undefined : selectedContentType,
    status: selectedStatus === "all" ? undefined : selectedStatus,
  });

  const displayContent = searchQuery ? searchResults : content;

  const getStatusBadge = (status: string) => {
    return (
      <Badge variant={status === "published" ? "default" : "secondary"}>
        {status === "published" ? (
          <>
            <Eye className="w-3 h-3 mr-1" />
            Published
          </>
        ) : (
          <>
            <EyeOff className="w-3 h-3 mr-1" />
            Draft
          </>
        )}
      </Badge>
    );
  };

  const getLanguageBadge = (language: string) => {
    return (
      <Badge variant="outline" className="flex items-center gap-1">
        <Globe className="w-3 h-3" />
        {language.toUpperCase()}
      </Badge>
    );
  };

  const handleEdit = (item: any) => {
    onEditContent?.(item.identifier, item.contentType?.name, item.language);
  };

  const handleCreate = (contentTypeName: string) => {
    onCreateContent?.(contentTypeName);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Content Management</h2>
          <p className="text-gray-600">Manage all your website content</p>
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Content
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            {contentTypes?.map((type) => (
              <DropdownMenuItem
                key={type._id}
                onClick={() => handleCreate(type.name)}
              >
                <div className="flex items-center space-x-2">
                  {type.icon && <span>{type.icon}</span>}
                  <span>{type.label}</span>
                </div>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger>
                <SelectValue placeholder="Language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Languages</SelectItem>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
              </SelectContent>
            </Select>

            <Select value={selectedContentType} onValueChange={setSelectedContentType}>
              <SelectTrigger>
                <SelectValue placeholder="Content Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {contentTypes?.map((type) => (
                  <SelectItem key={type._id} value={type.name}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus as any}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Content Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            Content Items ({displayContent?.length || 0})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {displayContent && displayContent.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Content</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Language</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Updated</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {displayContent.map((item) => (
                  <TableRow key={`${item.identifier}-${item.language}`}>
                    <TableCell>
                      <div>
                        <p className="font-medium text-gray-900">
                          {item.identifier.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())}
                        </p>
                        <p className="text-sm text-gray-500 truncate max-w-xs">
                          {getContentPreview(item, 80)}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {item.contentType?.label || "Unknown"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {getLanguageBadge(item.language)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(item.status)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar className="w-3 h-3 mr-1" />
                        {new Date(item.updatedAt).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEdit(item)}>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Copy className="w-4 h-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-500">
                {searchQuery ? "No content found matching your search." : "No content created yet."}
              </div>
              {!searchQuery && (
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => contentTypes?.[0] && handleCreate(contentTypes[0].name)}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Your First Content
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
