import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { Id } from "../../../../convex/_generated/dataModel";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Eye, Globe } from "lucide-react";
import { DynamicContent } from "@/components/content/DynamicContent";

interface ContentPreviewModalProps {
  contentId: Id<"content"> | null;
  isOpen: boolean;
  onClose: () => void;
}

export const ContentPreviewModal = ({ 
  contentId, 
  isOpen, 
  onClose 
}: ContentPreviewModalProps) => {
  // Queries
  const content = useQuery(
    api.content.getContentById,
    contentId ? { id: contentId } : "skip"
  );
  const contentType = useQuery(
    api.contentTypes.getContentTypeById,
    content?.contentTypeId ? { id: content.contentTypeId } : "skip"
  );

  if (!content || !contentType) {
    return null;
  }

  // Find where this content is used on the website
  const getUsageLocations = (identifier: string) => {
    const locations = [];
    
    // Common content identifiers and their locations
    const locationMap: Record<string, string[]> = {
      "home-hero": ["/"],
      "services-banner": ["/", "/services"],
      "contact-info": ["/contact"],
      "about-hero": ["/about"],
      "about-values": ["/about"],
      "services-hero": ["/services"],
      "contact-hero": ["/contact"],
      "cta-section": ["/"],
      "service-cybersecurity": ["/", "/services"],
      "service-network": ["/", "/services"],
      "service-surveillance": ["/", "/services"],
      "service-training": ["/", "/services"],
    };

    return locationMap[identifier] || [];
  };

  const usageLocations = getUsageLocations(content.identifier);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl font-bold flex items-center">
                <Eye className="w-5 h-5 mr-2" />
                Preview: {contentType.label}
              </DialogTitle>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant={content.status === "published" ? "default" : "secondary"}>
                  {content.status}
                </Badge>
                <span className="text-sm text-gray-500">
                  {content.identifier} • {content.language}
                </span>
              </div>
            </div>
            
            {usageLocations.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">Used on:</span>
                {usageLocations.map((location) => (
                  <Button
                    key={location}
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(location, '_blank')}
                  >
                    <ExternalLink className="w-3 h-3 mr-1" />
                    {location === "/" ? "Home" : location}
                  </Button>
                ))}
              </div>
            )}
          </div>
        </DialogHeader>

        {/* Content Preview */}
        <div className="flex-1 overflow-y-auto">
          <div className="border rounded-lg bg-white">
            {/* Preview Header */}
            <div className="border-b bg-gray-50 px-4 py-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Live Preview
                </span>
                <div className="flex items-center space-x-2 text-xs text-gray-500">
                  <Globe className="w-3 h-3" />
                  <span>How it appears on the website</span>
                </div>
              </div>
            </div>

            {/* Preview Content */}
            <div className="p-6">
              <DynamicContent
                identifier={content.identifier}
                language={content.language}
                fallback={
                  <div className="text-center py-8 text-gray-500">
                    <Eye className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p>Preview not available for this content type</p>
                    <p className="text-sm mt-1">
                      This content will be rendered using the {contentType.name} component
                    </p>
                  </div>
                }
              />
            </div>
          </div>

          {/* Content Data Debug (for development) */}
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-4">
              <summary className="text-sm text-gray-500 cursor-pointer hover:text-gray-700">
                Debug: Raw Content Data
              </summary>
              <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                {JSON.stringify(content.data, null, 2)}
              </pre>
            </details>
          )}
        </div>

        {/* Footer */}
        <div className="border-t pt-4">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              Last updated: {new Date(content.updatedAt).toLocaleString()}
            </div>
            <Button onClick={onClose}>
              Close Preview
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
