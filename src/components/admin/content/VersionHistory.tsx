import { useState } from "react";
import { 
  useContentTimeline, 
  useVersionComparison,
  formatVersionNumber,
  getChangeTypeIcon,
  getChangeTypeColor,
  formatTimeAgo
} from "@/hooks/useContentVersioning";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { 
  History, 
  Eye, 
  GitCompare, 
  RotateCcw,
  User,
  Clock,
  FileText,
  ChevronRight,
  ChevronDown
} from "lucide-react";

interface VersionHistoryProps {
  identifier: string;
  language: string;
  onRevert?: (version: number) => void;
  className?: string;
}

export const VersionHistory = ({ 
  identifier, 
  language, 
  onRevert,
  className 
}: VersionHistoryProps) => {
  const { timeline, isLoading } = useContentTimeline(identifier, language, 20);
  const [selectedVersions, setSelectedVersions] = useState<[number, number] | null>(null);
  const [expandedEntries, setExpandedEntries] = useState<Set<string>>(new Set());

  const toggleExpanded = (entryId: string) => {
    setExpandedEntries(prev => {
      const newSet = new Set(prev);
      if (newSet.has(entryId)) {
        newSet.delete(entryId);
      } else {
        newSet.add(entryId);
      }
      return newSet;
    });
  };

  const handleCompareVersions = (version1: number, version2: number) => {
    setSelectedVersions([Math.min(version1, version2), Math.max(version1, version2)]);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!timeline || timeline.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <History className="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <p>No version history available</p>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <History className="w-5 h-5" />
            Version History
          </h3>
          {timeline.length > 1 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (timeline.length >= 2) {
                  handleCompareVersions(timeline[0].version, timeline[1].version);
                }
              }}
            >
              <GitCompare className="w-4 h-4 mr-2" />
              Compare Latest
            </Button>
          )}
        </div>

        {/* Timeline */}
        <div className="space-y-3">
          {timeline.map((entry, index) => (
            <VersionEntry
              key={entry._id}
              entry={entry}
              isLatest={index === 0}
              isExpanded={expandedEntries.has(entry._id)}
              onToggleExpanded={() => toggleExpanded(entry._id)}
              onCompare={(version) => {
                if (timeline[0] && timeline[0].version !== version) {
                  handleCompareVersions(timeline[0].version, version);
                }
              }}
              onRevert={onRevert}
            />
          ))}
        </div>

        {/* Version Comparison Dialog */}
        {selectedVersions && (
          <VersionComparisonDialog
            identifier={identifier}
            language={language}
            version1={selectedVersions[0]}
            version2={selectedVersions[1]}
            onClose={() => setSelectedVersions(null)}
          />
        )}
      </div>
    </div>
  );
};

// Individual version entry component
const VersionEntry = ({ 
  entry, 
  isLatest, 
  isExpanded, 
  onToggleExpanded, 
  onCompare, 
  onRevert 
}: {
  entry: any;
  isLatest: boolean;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  onCompare: (version: number) => void;
  onRevert?: (version: number) => void;
}) => {
  const changeTypeColor = getChangeTypeColor(entry.changeType);
  const changeTypeIcon = getChangeTypeIcon(entry.changeType);

  return (
    <Card className={`transition-all ${isLatest ? 'ring-2 ring-blue-500' : ''}`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            {/* Change Type Icon */}
            <div className={`p-2 rounded-full ${changeTypeColor}`}>
              <span className="text-lg">{changeTypeIcon}</span>
            </div>

            {/* Version Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-1">
                <h4 className="font-semibold text-gray-900">
                  {formatVersionNumber(entry.version)}
                </h4>
                {isLatest && (
                  <Badge variant="default" className="text-xs">
                    Current
                  </Badge>
                )}
                <Badge variant="outline" className="text-xs capitalize">
                  {entry.changeType}
                </Badge>
              </div>

              <p className="text-sm text-gray-600 mb-2">
                {entry.changeNote || `${entry.changeType} content`}
              </p>

              <div className="flex items-center space-x-4 text-xs text-gray-500">
                <div className="flex items-center space-x-1">
                  <User className="w-3 h-3" />
                  <span>
                    {entry.user ? 
                      `${entry.user.firstName} ${entry.user.lastName}` : 
                      entry.changedBy
                    }
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>{formatTimeAgo(entry.createdAt)}</span>
                </div>
                {entry.metadata?.publishedAt && (
                  <div className="flex items-center space-x-1">
                    <span>Published: {new Date(entry.metadata.publishedAt).toLocaleDateString()}</span>
                  </div>
                )}
              </div>

              {/* Expanded Details */}
              {isExpanded && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div>
                      <span className="font-medium text-gray-600">Version:</span>
                      <p className="text-gray-900">{entry.version}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Previous Version:</span>
                      <p className="text-gray-900">
                        {entry.previousVersion || "None"}
                      </p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Change Type:</span>
                      <p className="text-gray-900 capitalize">{entry.changeType}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Timestamp:</span>
                      <p className="text-gray-900">
                        {new Date(entry.createdAt).toLocaleString()}
                      </p>
                    </div>
                  </div>

                  {entry.metadata?.fieldsChanged && (
                    <div className="mt-3">
                      <span className="font-medium text-gray-600 text-xs">Fields Changed:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {entry.metadata.fieldsChanged.map((field: string) => (
                          <Badge key={field} variant="outline" className="text-xs">
                            {field}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleExpanded}
            >
              {isExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </Button>

            {!isLatest && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onCompare(entry.version)}
                >
                  <GitCompare className="w-4 h-4" />
                </Button>

                {onRevert && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onRevert(entry.version)}
                  >
                    <RotateCcw className="w-4 h-4" />
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Version comparison dialog
const VersionComparisonDialog = ({ 
  identifier, 
  language, 
  version1, 
  version2, 
  onClose 
}: {
  identifier: string;
  language: string;
  version1: number;
  version2: number;
  onClose: () => void;
}) => {
  const { comparison, isLoading } = useVersionComparison(identifier, language, version1, version2);

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GitCompare className="w-5 h-5" />
            Compare Versions: {formatVersionNumber(version1)} vs {formatVersionNumber(version2)}
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        ) : comparison ? (
          <div className="space-y-6">
            {/* Version Info */}
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">
                    {formatVersionNumber(version1)} (Older)
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-xs text-gray-600">
                  <p>{comparison.version1.changeNote}</p>
                  <p className="mt-1">
                    {new Date(comparison.version1.createdAt).toLocaleString()}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">
                    {formatVersionNumber(version2)} (Newer)
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-xs text-gray-600">
                  <p>{comparison.version2.changeNote}</p>
                  <p className="mt-1">
                    {new Date(comparison.version2.createdAt).toLocaleString()}
                  </p>
                </CardContent>
              </Card>
            </div>

            <Separator />

            {/* Changes */}
            <div>
              <h4 className="font-semibold mb-4 flex items-center gap-2">
                <FileText className="w-4 h-4" />
                Changes ({comparison.changes.length})
              </h4>

              {comparison.changes.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No changes detected</p>
              ) : (
                <div className="space-y-3">
                  {comparison.changes.map((change, index) => (
                    <Card key={index} className="p-4">
                      <div className="space-y-2">
                        <h5 className="font-medium text-sm">{change.field}</h5>
                        <div className="grid grid-cols-2 gap-4 text-xs">
                          <div>
                            <span className="font-medium text-red-600">Before:</span>
                            <div className="mt-1 p-2 bg-red-50 rounded border">
                              <pre className="whitespace-pre-wrap">
                                {change.oldValue === null ? 
                                  "(not set)" : 
                                  JSON.stringify(change.oldValue, null, 2)
                                }
                              </pre>
                            </div>
                          </div>
                          <div>
                            <span className="font-medium text-green-600">After:</span>
                            <div className="mt-1 p-2 bg-green-50 rounded border">
                              <pre className="whitespace-pre-wrap">
                                {change.newValue === null ? 
                                  "(not set)" : 
                                  JSON.stringify(change.newValue, null, 2)
                                }
                              </pre>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p>Unable to load version comparison</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
