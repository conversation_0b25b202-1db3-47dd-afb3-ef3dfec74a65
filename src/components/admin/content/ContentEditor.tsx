import { useState, useEffect } from "react";
import { useContentManager } from "@/hooks/useContent";
import { useContentType } from "@/hooks/useContentTypes";
import { useAnalyticsContext } from "@/components/analytics/AnalyticsProvider";
import { validateContentData } from "@/hooks/useContentTypes";
import { FieldEditor } from "./FieldEditor";
import { VersionHistory } from "./VersionHistory";
import { DraftManager } from "./DraftManager";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Separator } from "@/components/ui/separator";
import { 
  Save, 
  Eye, 
  EyeOff, 
  History, 
  Copy, 
  RotateCcw,
  Globe,
  AlertCircle,
  CheckCircle
} from "lucide-react";
import { toast } from "sonner";

interface ContentEditorProps {
  identifier: string;
  contentTypeName: string;
  language?: string;
  onSave?: (content: any) => void;
  onCancel?: () => void;
}

export const ContentEditor = ({ 
  identifier, 
  contentTypeName, 
  language = "en",
  onSave,
  onCancel 
}: ContentEditorProps) => {
  const { contentType, isLoading: isContentTypeLoading } = useContentType(contentTypeName);
  const { content, history, isLoading, actions } = useContentManager(identifier, language);
  const { trackContent } = useAnalyticsContext();
  
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(language);

  // Initialize form data when content loads
  useEffect(() => {
    if (content?.data) {
      setFormData(content.data);
    } else if (contentType?.fields) {
      // Initialize with default values
      const defaultData: Record<string, any> = {};
      contentType.fields.forEach(field => {
        defaultData[field.name] = field.defaultValue ?? getDefaultValueForFieldType(field.type);
      });
      setFormData(defaultData);
    }
  }, [content, contentType]);

  const getDefaultValueForFieldType = (type: string): any => {
    switch (type) {
      case "text":
      case "richText":
      case "email":
      case "url":
      case "color":
        return "";
      case "number":
        return 0;
      case "boolean":
        return false;
      case "array":
        return [];
      case "object":
        return {};
      case "select":
        return "";
      case "multiSelect":
        return [];
      case "date":
        return null;
      case "image":
        return null;
      default:
        return null;
    }
  };

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));

    // Track field changes
    trackContent(`${identifier}-field-${fieldName}`, 'content-field', 'edit', {
      contentType: contentTypeName,
      fieldName,
      language: selectedLanguage,
      hasValue: value !== null && value !== undefined && value !== ''
    });

    // Clear error for this field
    if (errors[fieldName]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    if (!contentType) return false;

    const validation = validateContentData(contentType, formData);
    setErrors(validation.errors);
    return validation.isValid;
  };

  const handleSave = async (status: "draft" | "published" = "draft") => {
    if (!validateForm()) {
      toast.error("Please fix the validation errors before saving");
      return;
    }

    setIsSaving(true);
    try {
      await actions.save(formData, status);

      // Track content save/publish
      trackContent(identifier, 'content', status === 'published' ? 'publish' : 'save', {
        contentType: contentTypeName,
        language: selectedLanguage,
        status,
        fieldCount: Object.keys(formData).length,
        isNewContent: !content
      });

      toast.success(`Content ${status === "published" ? "published" : "saved as draft"} successfully`);
      onSave?.(formData);
    } catch (error) {
      // Track failed save
      trackContent(identifier, 'content', 'save_failed', {
        contentType: contentTypeName,
        language: selectedLanguage,
        status,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      toast.error("Failed to save content");
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublish = async () => {
    if (content?.status === "draft") {
      await handleSave("published");
    } else {
      try {
        await actions.publish();

        // Track content publish
        trackContent(identifier, 'content', 'publish', {
          contentType: contentTypeName,
          language: selectedLanguage,
          wasAlreadyPublished: true
        });

        toast.success("Content published successfully");
      } catch (error) {
        // Track failed publish
        trackContent(identifier, 'content', 'publish_failed', {
          contentType: contentTypeName,
          language: selectedLanguage,
          error: error instanceof Error ? error.message : 'Unknown error'
        });

        toast.error("Failed to publish content");
      }
    }
  };

  const handleUnpublish = async () => {
    try {
      await actions.unpublish();
      toast.success("Content unpublished successfully");
    } catch (error) {
      toast.error("Failed to unpublish content");
    }
  };

  const handleDuplicate = async (targetLanguage: string) => {
    try {
      await actions.duplicate(targetLanguage, "draft");
      toast.success(`Content duplicated to ${targetLanguage}`);
    } catch (error) {
      toast.error("Failed to duplicate content");
    }
  };

  const handleRevert = async (version: number) => {
    try {
      await actions.revert(version, `Reverted to version ${version}`);
      toast.success("Content reverted successfully");
    } catch (error) {
      toast.error("Failed to revert content");
    }
  };

  if (isLoading || isContentTypeLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!contentType) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Content Type Not Found</h3>
        <p className="text-gray-600">The content type "{contentTypeName}" could not be found.</p>
      </div>
    );
  }

  const hasErrors = Object.keys(errors).length > 0;
  const isPublished = content?.status === "published";
  const isDraft = content?.status === "draft";

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Edit {contentType.label}
          </h1>
          <div className="flex items-center space-x-2 mt-1">
            <Badge variant={isPublished ? "default" : "secondary"}>
              {isPublished ? "Published" : "Draft"}
            </Badge>
            <Badge variant="outline">
              <Globe className="w-3 h-3 mr-1" />
              {selectedLanguage.toUpperCase()}
            </Badge>
            {content?.version && (
              <Badge variant="outline">
                Version {content.version}
              </Badge>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowHistory(!showHistory)}
          >
            <History className="w-4 h-4 mr-2" />
            History
          </Button>

          <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">EN</SelectItem>
              <SelectItem value="es">ES</SelectItem>
            </SelectContent>
          </Select>

          {onCancel && (
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Content Editor */}
        <div className="lg:col-span-3 space-y-6">
          {/* Draft Management */}
          <DraftManager
            identifier={identifier}
            language={selectedLanguage}
            data={formData}
            onDataChange={setFormData}
          />

          {/* Content Fields */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Content Fields
                {hasErrors && (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    {Object.keys(errors).length} error{Object.keys(errors).length !== 1 ? 's' : ''}
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {contentType.fields.map((field) => (
                <FieldEditor
                  key={field.name}
                  field={field}
                  value={formData[field.name]}
                  onChange={(value) => handleFieldChange(field.name, value)}
                  error={errors[field.name]}
                  disabled={isSaving}
                />
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                onClick={() => handleSave("draft")}
                disabled={isSaving}
                className="w-full"
                variant="outline"
              >
                <Save className="w-4 h-4 mr-2" />
                Save Draft
              </Button>

              <Button
                onClick={handlePublish}
                disabled={isSaving || hasErrors}
                className="w-full"
              >
                {isPublished ? (
                  <>
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Update Published
                  </>
                ) : (
                  <>
                    <Eye className="w-4 h-4 mr-2" />
                    Publish
                  </>
                )}
              </Button>

              {isPublished && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" className="w-full">
                      <EyeOff className="w-4 h-4 mr-2" />
                      Unpublish
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Unpublish Content</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will change the content status from published to draft. 
                        The content will no longer be visible on the public website.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={handleUnpublish}>
                        Unpublish
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}

              <Separator />

              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => handleDuplicate(selectedLanguage === "en" ? "es" : "en")}
              >
                <Copy className="w-4 h-4 mr-2" />
                Duplicate to {selectedLanguage === "en" ? "Spanish" : "English"}
              </Button>
            </CardContent>
          </Card>

          {/* Content Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Content Info</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Identifier:</span>
                <p className="text-gray-600 font-mono text-xs">{identifier}</p>
              </div>
              <div>
                <span className="font-medium">Type:</span>
                <p className="text-gray-600">{contentType.label}</p>
              </div>
              {content?.createdAt && (
                <div>
                  <span className="font-medium">Created:</span>
                  <p className="text-gray-600">
                    {new Date(content.createdAt).toLocaleDateString()}
                  </p>
                </div>
              )}
              {content?.updatedAt && (
                <div>
                  <span className="font-medium">Updated:</span>
                  <p className="text-gray-600">
                    {new Date(content.updatedAt).toLocaleDateString()}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Version History */}
          {showHistory && (
            <VersionHistory
              identifier={identifier}
              language={selectedLanguage}
              onRevert={handleRevert}
            />
          )}
        </div>
      </div>
    </div>
  );
};
