import { useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { Language } from "@/i18n/translations";
import {
  getAllTranslations,
  searchTranslations,
  getTranslationStats,
  getCategoryStats,
  getTranslationCategories,
  exportTranslationsToJSON,
  formatCategoryName
} from "@/utils/translationHelpers";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  Languages, 
  Search, 
  Save, 
  Download,
  Upload,
  Globe,
  Edit,
  Check,
  X
} from "lucide-react";
import { toast } from "sonner";

export const TranslationManager = () => {
  const { canManageUsers } = useAuth(); // Using existing permission for translations
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [editValues, setEditValues] = useState<Record<Language, string>>({ en: "", es: "" });

  if (!canManageUsers) {
    return (
      <div className="text-center py-8">
        <Languages className="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <p className="text-gray-500">You don't have permission to manage translations</p>
      </div>
    );
  }

  // Get translations data
  const allTranslations = getAllTranslations();
  const categories = getTranslationCategories();
  const filteredTranslations = searchTranslations(searchTerm, selectedCategory);

  const handleEdit = (key: string, en: string, es: string) => {
    setEditingKey(key);
    setEditValues({ en, es });
  };

  const handleSave = () => {
    // In a real implementation, this would save to the database
    toast.success("Translation updated successfully");
    setEditingKey(null);
  };

  const handleCancel = () => {
    setEditingKey(null);
    setEditValues({ en: "", es: "" });
  };

  const exportTranslations = () => {
    const dataStr = exportTranslationsToJSON();
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'translations.json';
    link.click();
    URL.revokeObjectURL(url);
    toast.success("Translations exported successfully");
  };

  const stats = getTranslationStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Translation Management</h2>
          <p className="text-gray-600">
            Manage translations for English and Spanish content
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={exportTranslations}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Keys</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <Globe className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Translated</p>
                <p className="text-2xl font-bold text-green-600">{stats.translated}</p>
              </div>
              <Check className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Missing</p>
                <p className="text-2xl font-bold text-orange-600">{stats.missing}</p>
              </div>
              <X className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search & Filter */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search translations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="All categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {formatCategoryName(category)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Translations Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Languages className="w-5 h-5" />
            Translations ({filteredTranslations.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredTranslations.map((item) => (
              <div key={item.key} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{item.key}</Badge>
                    <Badge variant="secondary">{formatCategoryName(item.category)}</Badge>
                    {item.es === item.en && (
                      <Badge variant="destructive">Missing Translation</Badge>
                    )}
                  </div>
                  {editingKey !== item.key && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(item.key, item.en, item.es)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                {editingKey === item.key ? (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        English
                      </label>
                      <Textarea
                        value={editValues.en}
                        onChange={(e) => setEditValues(prev => ({ ...prev, en: e.target.value }))}
                        rows={2}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Spanish
                      </label>
                      <Textarea
                        value={editValues.es}
                        onChange={(e) => setEditValues(prev => ({ ...prev, es: e.target.value }))}
                        rows={2}
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button size="sm" onClick={handleSave}>
                        <Save className="w-4 h-4 mr-2" />
                        Save
                      </Button>
                      <Button variant="outline" size="sm" onClick={handleCancel}>
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        🇺🇸 English
                      </label>
                      <p className="text-gray-900 bg-gray-50 p-2 rounded border">
                        {item.en}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        🇪🇸 Spanish
                      </label>
                      <p className="text-gray-900 bg-gray-50 p-2 rounded border">
                        {item.es}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {filteredTranslations.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No translations found matching your search.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
