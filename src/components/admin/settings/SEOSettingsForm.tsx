import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Eye, Search } from "lucide-react";

interface SEOSettingsFormProps {
  settings: Record<string, any>;
  onChange: (key: string, value: any) => void;
  getValue: (key: string) => any;
}

export const SEOSettingsForm = ({ settings, onChange, getValue }: SEOSettingsFormProps) => {
  const metaTitle = getValue("meta_title") || "";
  const metaDescription = getValue("meta_description") || "";

  return (
    <div className="space-y-6">
      {/* Meta Tags */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Meta Tags</h3>
        
        <div>
          <Label htmlFor="meta_title">Meta Title</Label>
          <Input
            id="meta_title"
            value={metaTitle}
            onChange={(e) => onChange("meta_title", e.target.value)}
            placeholder="OfficeTech Guinea - Technology & Security Solutions"
            maxLength={60}
          />
          <div className="flex justify-between items-center mt-1">
            <p className="text-xs text-gray-500">
              The title that appears in search engine results
            </p>
            <Badge variant={metaTitle.length > 60 ? "destructive" : "secondary"}>
              {metaTitle.length}/60
            </Badge>
          </div>
        </div>

        <div>
          <Label htmlFor="meta_description">Meta Description</Label>
          <Textarea
            id="meta_description"
            value={metaDescription}
            onChange={(e) => onChange("meta_description", e.target.value)}
            placeholder="Leading provider of technology and security solutions in Guinea. Network infrastructure, enterprise services, and IT support."
            rows={3}
            maxLength={160}
          />
          <div className="flex justify-between items-center mt-1">
            <p className="text-xs text-gray-500">
              The description that appears in search engine results
            </p>
            <Badge variant={metaDescription.length > 160 ? "destructive" : "secondary"}>
              {metaDescription.length}/160
            </Badge>
          </div>
        </div>

        <div>
          <Label htmlFor="meta_keywords">Meta Keywords</Label>
          <Input
            id="meta_keywords"
            value={getValue("meta_keywords") || ""}
            onChange={(e) => onChange("meta_keywords", e.target.value)}
            placeholder="technology, security, network, Guinea, enterprise, IT support"
          />
          <p className="text-xs text-gray-500 mt-1">
            Comma-separated keywords (less important for modern SEO)
          </p>
        </div>
      </div>

      {/* Open Graph */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Open Graph (Social Media)</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="og_title">OG Title</Label>
            <Input
              id="og_title"
              value={getValue("og_title") || ""}
              onChange={(e) => onChange("og_title", e.target.value)}
              placeholder="OfficeTech Guinea - Technology Solutions"
            />
            <p className="text-xs text-gray-500 mt-1">
              Title for social media sharing (defaults to meta title)
            </p>
          </div>

          <div>
            <Label htmlFor="og_type">OG Type</Label>
            <Input
              id="og_type"
              value={getValue("og_type") || "website"}
              onChange={(e) => onChange("og_type", e.target.value)}
              placeholder="website"
            />
            <p className="text-xs text-gray-500 mt-1">
              Type of content (website, article, etc.)
            </p>
          </div>
        </div>

        <div>
          <Label htmlFor="og_description">OG Description</Label>
          <Textarea
            id="og_description"
            value={getValue("og_description") || ""}
            onChange={(e) => onChange("og_description", e.target.value)}
            placeholder="Transform your business with cutting-edge network infrastructure and security solutions."
            rows={2}
          />
          <p className="text-xs text-gray-500 mt-1">
            Description for social media sharing (defaults to meta description)
          </p>
        </div>

        <div>
          <Label htmlFor="og_image">OG Image URL</Label>
          <Input
            id="og_image"
            value={getValue("og_image") || ""}
            onChange={(e) => onChange("og_image", e.target.value)}
            placeholder="/images/og-default.jpg"
          />
          <p className="text-xs text-gray-500 mt-1">
            Image displayed when sharing on social media (1200x630px recommended)
          </p>
        </div>
      </div>

      {/* Twitter Cards */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Twitter Cards</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="twitter_card">Twitter Card Type</Label>
            <Input
              id="twitter_card"
              value={getValue("twitter_card") || "summary_large_image"}
              onChange={(e) => onChange("twitter_card", e.target.value)}
              placeholder="summary_large_image"
            />
            <p className="text-xs text-gray-500 mt-1">
              Type of Twitter card (summary, summary_large_image, etc.)
            </p>
          </div>

          <div>
            <Label htmlFor="twitter_handle">Twitter Handle</Label>
            <Input
              id="twitter_handle"
              value={getValue("twitter_handle") || ""}
              onChange={(e) => onChange("twitter_handle", e.target.value)}
              placeholder="@officetechgn"
            />
            <p className="text-xs text-gray-500 mt-1">
              Your Twitter username (with @)
            </p>
          </div>
        </div>
      </div>

      {/* Analytics & Tracking */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Analytics & Tracking</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="google_analytics_id">Google Analytics ID</Label>
            <Input
              id="google_analytics_id"
              value={getValue("google_analytics_id") || ""}
              onChange={(e) => onChange("google_analytics_id", e.target.value)}
              placeholder="G-XXXXXXXXXX"
            />
            <p className="text-xs text-gray-500 mt-1">
              Google Analytics 4 measurement ID
            </p>
          </div>

          <div>
            <Label htmlFor="google_tag_manager_id">Google Tag Manager ID</Label>
            <Input
              id="google_tag_manager_id"
              value={getValue("google_tag_manager_id") || ""}
              onChange={(e) => onChange("google_tag_manager_id", e.target.value)}
              placeholder="GTM-XXXXXXX"
            />
            <p className="text-xs text-gray-500 mt-1">
              Google Tag Manager container ID
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="facebook_pixel_id">Facebook Pixel ID</Label>
            <Input
              id="facebook_pixel_id"
              value={getValue("facebook_pixel_id") || ""}
              onChange={(e) => onChange("facebook_pixel_id", e.target.value)}
              placeholder="123456789012345"
            />
            <p className="text-xs text-gray-500 mt-1">
              Facebook Pixel ID for conversion tracking
            </p>
          </div>

          <div>
            <Label htmlFor="google_site_verification">Google Site Verification</Label>
            <Input
              id="google_site_verification"
              value={getValue("google_site_verification") || ""}
              onChange={(e) => onChange("google_site_verification", e.target.value)}
              placeholder="verification_code"
            />
            <p className="text-xs text-gray-500 mt-1">
              Google Search Console verification code
            </p>
          </div>
        </div>
      </div>

      {/* Search Engine Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Search Engine Settings</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="robots_index">Allow Search Engine Indexing</Label>
              <p className="text-xs text-gray-500">
                Allow search engines to index your website
              </p>
            </div>
            <Switch
              id="robots_index"
              checked={getValue("robots_index") !== false}
              onCheckedChange={(checked) => onChange("robots_index", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="robots_follow">Allow Following Links</Label>
              <p className="text-xs text-gray-500">
                Allow search engines to follow links on your website
              </p>
            </div>
            <Switch
              id="robots_follow"
              checked={getValue("robots_follow") !== false}
              onCheckedChange={(checked) => onChange("robots_follow", checked)}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="sitemap_url">Sitemap URL</Label>
          <Input
            id="sitemap_url"
            value={getValue("sitemap_url") || ""}
            onChange={(e) => onChange("sitemap_url", e.target.value)}
            placeholder="/sitemap.xml"
          />
          <p className="text-xs text-gray-500 mt-1">
            URL to your XML sitemap
          </p>
        </div>

        <div>
          <Label htmlFor="robots_txt">Custom Robots.txt</Label>
          <Textarea
            id="robots_txt"
            value={getValue("robots_txt") || ""}
            onChange={(e) => onChange("robots_txt", e.target.value)}
            placeholder="User-agent: *&#10;Disallow: /admin/&#10;Sitemap: https://officetech.gn/sitemap.xml"
            rows={4}
          />
          <p className="text-xs text-gray-500 mt-1">
            Custom robots.txt content (optional)
          </p>
        </div>
      </div>

      {/* SEO Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            Search Engine Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg p-4 bg-gray-50">
            <div className="text-blue-600 text-lg hover:underline cursor-pointer">
              {metaTitle || "Your Page Title"}
            </div>
            <div className="text-green-700 text-sm">
              {getValue("site_url") || "https://yoursite.com"} 
              <ExternalLink className="w-3 h-3 inline ml-1" />
            </div>
            <div className="text-gray-600 text-sm mt-1">
              {metaDescription || "Your page description will appear here..."}
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            This is how your page might appear in Google search results
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
