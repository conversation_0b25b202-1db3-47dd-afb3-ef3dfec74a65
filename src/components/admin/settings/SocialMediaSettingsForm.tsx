import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Facebook, 
  Twitter, 
  Linkedin, 
  Instagram, 
  Youtube,
  ExternalLink,
  Share2
} from "lucide-react";

interface SocialMediaSettingsFormProps {
  settings: Record<string, any>;
  onChange: (key: string, value: any) => void;
  getValue: (key: string) => any;
}

export const SocialMediaSettingsForm = ({ settings, onChange, getValue }: SocialMediaSettingsFormProps) => {
  const socialPlatforms = [
    {
      key: "facebook_url",
      label: "Facebook",
      icon: Facebook,
      placeholder: "https://facebook.com/officetechguinea",
      color: "text-blue-600",
    },
    {
      key: "twitter_url",
      label: "Twitter",
      icon: Twitter,
      placeholder: "https://twitter.com/officetechgn",
      color: "text-sky-500",
    },
    {
      key: "linkedin_url",
      label: "LinkedIn",
      icon: Linkedin,
      placeholder: "https://linkedin.com/company/officetech-guinea",
      color: "text-blue-700",
    },
    {
      key: "instagram_url",
      label: "Instagram",
      icon: Instagram,
      placeholder: "https://instagram.com/officetechguinea",
      color: "text-pink-600",
    },
    {
      key: "youtube_url",
      label: "YouTube",
      icon: Youtube,
      placeholder: "https://youtube.com/c/officetechguinea",
      color: "text-red-600",
    },
  ];

  const validateUrl = (url: string): boolean => {
    if (!url) return true; // Empty is valid
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  return (
    <div className="space-y-6">
      {/* Social Media Links */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Social Media Profiles</h3>
        <p className="text-gray-600">
          Add your social media profile URLs to display social links on your website.
        </p>
        
        <div className="space-y-4">
          {socialPlatforms.map((platform) => {
            const Icon = platform.icon;
            const value = getValue(platform.key) || "";
            const isValid = validateUrl(value);
            
            return (
              <div key={platform.key} className="space-y-2">
                <Label htmlFor={platform.key} className="flex items-center gap-2">
                  <Icon className={`w-4 h-4 ${platform.color}`} />
                  {platform.label}
                </Label>
                <div className="flex gap-2">
                  <Input
                    id={platform.key}
                    type="url"
                    value={value}
                    onChange={(e) => onChange(platform.key, e.target.value)}
                    placeholder={platform.placeholder}
                    className={!isValid && value ? "border-red-300" : ""}
                  />
                  {value && isValid && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(value, '_blank')}
                    >
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  )}
                </div>
                {!isValid && value && (
                  <p className="text-xs text-red-600">Please enter a valid URL</p>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Social Media Display Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Display Settings</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="show_social_header">Show in Header</Label>
              <p className="text-xs text-gray-500">
                Display social media icons in the website header
              </p>
            </div>
            <Switch
              id="show_social_header"
              checked={getValue("show_social_header") !== false}
              onCheckedChange={(checked) => onChange("show_social_header", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="show_social_footer">Show in Footer</Label>
              <p className="text-xs text-gray-500">
                Display social media icons in the website footer
              </p>
            </div>
            <Switch
              id="show_social_footer"
              checked={getValue("show_social_footer") !== false}
              onCheckedChange={(checked) => onChange("show_social_footer", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="show_social_contact">Show on Contact Page</Label>
              <p className="text-xs text-gray-500">
                Display social media links on the contact page
              </p>
            </div>
            <Switch
              id="show_social_contact"
              checked={getValue("show_social_contact") !== false}
              onCheckedChange={(checked) => onChange("show_social_contact", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="social_open_new_tab">Open in New Tab</Label>
              <p className="text-xs text-gray-500">
                Open social media links in a new browser tab
              </p>
            </div>
            <Switch
              id="social_open_new_tab"
              checked={getValue("social_open_new_tab") !== false}
              onCheckedChange={(checked) => onChange("social_open_new_tab", checked)}
            />
          </div>
        </div>
      </div>

      {/* Social Sharing Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Social Sharing</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="enable_social_sharing">Enable Social Sharing</Label>
              <p className="text-xs text-gray-500">
                Show social sharing buttons on content pages
              </p>
            </div>
            <Switch
              id="enable_social_sharing"
              checked={getValue("enable_social_sharing") !== false}
              onCheckedChange={(checked) => onChange("enable_social_sharing", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="sharing_facebook">Facebook Sharing</Label>
              <p className="text-xs text-gray-500">
                Allow sharing content on Facebook
              </p>
            </div>
            <Switch
              id="sharing_facebook"
              checked={getValue("sharing_facebook") !== false}
              onCheckedChange={(checked) => onChange("sharing_facebook", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="sharing_twitter">Twitter Sharing</Label>
              <p className="text-xs text-gray-500">
                Allow sharing content on Twitter
              </p>
            </div>
            <Switch
              id="sharing_twitter"
              checked={getValue("sharing_twitter") !== false}
              onCheckedChange={(checked) => onChange("sharing_twitter", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="sharing_linkedin">LinkedIn Sharing</Label>
              <p className="text-xs text-gray-500">
                Allow sharing content on LinkedIn
              </p>
            </div>
            <Switch
              id="sharing_linkedin"
              checked={getValue("sharing_linkedin") !== false}
              onCheckedChange={(checked) => onChange("sharing_linkedin", checked)}
            />
          </div>
        </div>
      </div>

      {/* Social Media Integration */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Social Media Integration</h3>
        
        <div>
          <Label htmlFor="facebook_app_id">Facebook App ID</Label>
          <Input
            id="facebook_app_id"
            value={getValue("facebook_app_id") || ""}
            onChange={(e) => onChange("facebook_app_id", e.target.value)}
            placeholder="123456789012345"
          />
          <p className="text-xs text-gray-500 mt-1">
            Facebook App ID for enhanced social features
          </p>
        </div>

        <div>
          <Label htmlFor="twitter_site">Twitter Site Handle</Label>
          <Input
            id="twitter_site"
            value={getValue("twitter_site") || ""}
            onChange={(e) => onChange("twitter_site", e.target.value)}
            placeholder="@officetechgn"
          />
          <p className="text-xs text-gray-500 mt-1">
            Twitter handle for the website (used in Twitter Cards)
          </p>
        </div>
      </div>

      {/* Social Media Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="w-5 h-5" />
            Social Media Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {socialPlatforms.map((platform) => {
                const Icon = platform.icon;
                const url = getValue(platform.key);
                const isValid = url && validateUrl(url);
                
                return (
                  <div
                    key={platform.key}
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg border ${
                      isValid 
                        ? 'bg-green-50 border-green-200 text-green-800' 
                        : 'bg-gray-50 border-gray-200 text-gray-500'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm">{platform.label}</span>
                    {isValid && <ExternalLink className="w-3 h-3" />}
                  </div>
                );
              })}
            </div>
            
            <p className="text-xs text-gray-500">
              Green badges indicate configured social media profiles that will be displayed on your website.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
