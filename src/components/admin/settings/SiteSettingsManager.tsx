import { useState } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useSettingsByCategories, useSettingsManager } from "@/hooks/useSiteSettings";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Settings, 
  Save, 
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Share2,
  Phone,
  Mail,
  ToggleLeft,
  Zap
} from "lucide-react";
import { toast } from "sonner";
import { GeneralSettingsForm } from "./GeneralSettingsForm";
import { SEOSettingsForm } from "./SEOSettingsForm";
import { SocialMediaSettingsForm } from "./SocialMediaSettingsForm";
import { ContactSettingsForm } from "./ContactSettingsForm";
import { EmailSettingsForm } from "./EmailSettingsForm";
import { FeatureFlagsForm } from "./FeatureFlagsForm";
import { PerformanceSettingsForm } from "./PerformanceSettingsForm";

const categoryIcons = {
  general: Settings,
  seo: TrendingUp,
  social: Share2,
  contact: Phone,
  email: Mail,
  features: ToggleLeft,
  performance: Zap,
};

export const SiteSettingsManager = () => {
  const { canManageUsers } = useAuth(); // Using existing permission for settings
  const { categorizedSettings, categories, isLoading } = useSettingsByCategories();
  const { updateMultipleSettings, initializeDefaultSettings } = useSettingsManager();
  const [activeTab, setActiveTab] = useState("general");
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [pendingChanges, setPendingChanges] = useState<Record<string, any>>({});
  const [isSaving, setIsSaving] = useState(false);

  if (!canManageUsers) {
    return (
      <div className="text-center py-8">
        <Settings className="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <p className="text-gray-500">You don't have permission to manage site settings</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const handleSettingChange = (category: string, key: string, value: any) => {
    const fullKey = `${category}.${key}`;
    setPendingChanges(prev => ({
      ...prev,
      [fullKey]: value,
    }));
    setHasUnsavedChanges(true);
  };

  const handleSaveChanges = async () => {
    if (Object.keys(pendingChanges).length === 0) {
      toast.info("No changes to save");
      return;
    }

    setIsSaving(true);
    try {
      const settings = Object.entries(pendingChanges).map(([key, value]) => ({
        key,
        value,
      }));

      const result = await updateMultipleSettings(settings);
      
      if (result.success) {
        toast.success("Settings saved successfully");
        setPendingChanges({});
        setHasUnsavedChanges(false);
      } else {
        toast.error(result.error || "Failed to save settings");
      }
    } catch (error) {
      toast.error("Failed to save settings");
      console.error("Save error:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDiscardChanges = () => {
    setPendingChanges({});
    setHasUnsavedChanges(false);
    toast.info("Changes discarded");
  };

  const handleInitializeDefaults = async () => {
    try {
      const result = await initializeDefaultSettings();
      if (result.success) {
        toast.success(`Initialized ${result.initialized} default settings`);
      } else {
        toast.error(result.error || "Failed to initialize default settings");
      }
    } catch (error) {
      toast.error("Failed to initialize default settings");
      console.error("Initialize error:", error);
    }
  };

  const getCurrentValue = (category: string, key: string) => {
    const fullKey = `${category}.${key}`;
    return pendingChanges[fullKey] !== undefined 
      ? pendingChanges[fullKey] 
      : categorizedSettings[category]?.[key];
  };

  const renderCategoryForm = (categoryKey: string) => {
    const categorySettings = categorizedSettings[categoryKey] || {};
    const onChange = (key: string, value: any) => handleSettingChange(categoryKey, key, value);
    const getValue = (key: string) => getCurrentValue(categoryKey, key);

    switch (categoryKey) {
      case 'general':
        return <GeneralSettingsForm settings={categorySettings} onChange={onChange} getValue={getValue} />;
      case 'seo':
        return <SEOSettingsForm settings={categorySettings} onChange={onChange} getValue={getValue} />;
      case 'social':
        return <SocialMediaSettingsForm settings={categorySettings} onChange={onChange} getValue={getValue} />;
      case 'contact':
        return <ContactSettingsForm settings={categorySettings} onChange={onChange} getValue={getValue} />;
      case 'email':
        return <EmailSettingsForm settings={categorySettings} onChange={onChange} getValue={getValue} />;
      case 'features':
        return <FeatureFlagsForm settings={categorySettings} onChange={onChange} getValue={getValue} />;
      case 'performance':
        return <PerformanceSettingsForm settings={categorySettings} onChange={onChange} getValue={getValue} />;
      default:
        return <div className="text-center py-8 text-gray-500">Category not found</div>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Site Settings</h2>
          <p className="text-gray-600">
            Manage global site configuration and preferences
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          {hasUnsavedChanges && (
            <Badge variant="outline" className="text-orange-600 border-orange-600">
              <AlertTriangle className="w-3 h-3 mr-1" />
              Unsaved Changes
            </Badge>
          )}
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleInitializeDefaults}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Reset to Defaults
          </Button>
        </div>
      </div>

      {/* Save/Discard Actions */}
      {hasUnsavedChanges && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-orange-600" />
                <span className="text-orange-800 font-medium">
                  You have unsaved changes
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={handleDiscardChanges}
                  disabled={isSaving}
                >
                  Discard
                </Button>
                <Button 
                  size="sm"
                  onClick={handleSaveChanges}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Settings Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-7">
          {categories.map((category) => {
            const Icon = categoryIcons[category.key as keyof typeof categoryIcons];
            const hasChanges = Object.keys(pendingChanges).some(key => 
              key.startsWith(`${category.key}.`)
            );
            
            return (
              <TabsTrigger 
                key={category.key} 
                value={category.key}
                className="flex items-center space-x-2"
              >
                <Icon className="w-4 h-4" />
                <span className="hidden sm:inline">{category.label}</span>
                {hasChanges && (
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                )}
              </TabsTrigger>
            );
          })}
        </TabsList>

        {categories.map((category) => (
          <TabsContent key={category.key} value={category.key}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {React.createElement(categoryIcons[category.key as keyof typeof categoryIcons], {
                    className: "w-5 h-5"
                  })}
                  {category.label}
                </CardTitle>
                <p className="text-gray-600">{category.description}</p>
              </CardHeader>
              <CardContent>
                {renderCategoryForm(category.key)}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Settings Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5" />
            Settings Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {Object.keys(categorizedSettings).reduce((total, category) => 
                  total + Object.keys(categorizedSettings[category]).length, 0
                )}
              </div>
              <div className="text-sm text-gray-600">Total Settings</div>
            </div>

            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {categories.length}
              </div>
              <div className="text-sm text-gray-600">Categories</div>
            </div>

            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {Object.keys(pendingChanges).length}
              </div>
              <div className="text-sm text-gray-600">Pending Changes</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
