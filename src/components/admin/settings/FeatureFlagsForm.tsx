import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON><PERSON><PERSON>riangle, 
  Users, 
  MessageSquare, 
  BarChart3, 
  Mail,
  Settings,
  Eye,
  Lock
} from "lucide-react";

interface FeatureFlagsFormProps {
  settings: Record<string, any>;
  onChange: (key: string, value: any) => void;
  getValue: (key: string) => any;
}

export const FeatureFlagsForm = ({ settings, onChange, getValue }: FeatureFlagsFormProps) => {
  const features = [
    {
      key: "maintenance_mode",
      label: "Maintenance Mode",
      description: "Put the website in maintenance mode (only admins can access)",
      icon: Settings,
      critical: true,
    },
    {
      key: "registration_enabled",
      label: "User Registration",
      description: "Allow new users to register accounts",
      icon: Users,
      critical: false,
    },
    {
      key: "comments_enabled",
      label: "Comments System",
      description: "Enable comments on content pages",
      icon: MessageSquare,
      critical: false,
    },
    {
      key: "analytics_enabled",
      label: "Analytics Tracking",
      description: "Enable analytics and tracking scripts",
      icon: BarChart3,
      critical: false,
    },
    {
      key: "newsletter_enabled",
      label: "Newsletter Signup",
      description: "Show newsletter signup forms",
      icon: Mail,
      critical: false,
    },
    {
      key: "contact_form_enabled",
      label: "Contact Forms",
      description: "Enable contact forms throughout the site",
      icon: Mail,
      critical: false,
    },
    {
      key: "search_enabled",
      label: "Site Search",
      description: "Enable search functionality",
      icon: Eye,
      critical: false,
    },
    {
      key: "user_profiles_public",
      label: "Public User Profiles",
      description: "Allow public viewing of user profiles",
      icon: Users,
      critical: false,
    },
    {
      key: "api_access_enabled",
      label: "API Access",
      description: "Enable API endpoints for external access",
      icon: Settings,
      critical: false,
    },
    {
      key: "debug_mode",
      label: "Debug Mode",
      description: "Enable debug information (development only)",
      icon: AlertTriangle,
      critical: true,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Critical Features Warning */}
      <Card className="border-orange-200 bg-orange-50">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-orange-800">Critical Features</h4>
              <p className="text-sm text-orange-700">
                Some features marked as critical can significantly impact your website's functionality. 
                Please be careful when modifying these settings.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feature Flags */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Feature Flags</h3>
        <p className="text-gray-600">
          Enable or disable specific features across your website.
        </p>
        
        <div className="space-y-4">
          {features.map((feature) => {
            const Icon = feature.icon;
            const isEnabled = getValue(feature.key) !== false;
            
            return (
              <Card key={feature.key} className={feature.critical ? "border-orange-200" : ""}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className={`p-2 rounded-lg ${
                        isEnabled 
                          ? feature.critical 
                            ? "bg-orange-100" 
                            : "bg-green-100"
                          : "bg-gray-100"
                      }`}>
                        <Icon className={`w-5 h-5 ${
                          isEnabled 
                            ? feature.critical 
                              ? "text-orange-600" 
                              : "text-green-600"
                            : "text-gray-500"
                        }`} />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <Label htmlFor={feature.key} className="font-medium text-gray-900">
                            {feature.label}
                          </Label>
                          {feature.critical && (
                            <Badge variant="outline" className="text-orange-600 border-orange-600">
                              Critical
                            </Badge>
                          )}
                          <Badge variant={isEnabled ? "default" : "secondary"}>
                            {isEnabled ? "Enabled" : "Disabled"}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                    
                    <Switch
                      id={feature.key}
                      checked={isEnabled}
                      onCheckedChange={(checked) => onChange(feature.key, checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Feature Status Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Feature Status Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {features.filter(f => getValue(f.key) !== false).length}
              </div>
              <div className="text-sm text-gray-600">Enabled Features</div>
            </div>

            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">
                {features.filter(f => getValue(f.key) === false).length}
              </div>
              <div className="text-sm text-gray-600">Disabled Features</div>
            </div>

            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {features.filter(f => f.critical && getValue(f.key) !== false).length}
              </div>
              <div className="text-sm text-gray-600">Critical Features Active</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
