import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";

interface ContactSettingsFormProps {
  settings: Record<string, any>;
  onChange: (key: string, value: any) => void;
  getValue: (key: string) => any;
}

export const ContactSettingsForm = ({ settings, onChange, getValue }: ContactSettingsFormProps) => {
  return (
    <div className="space-y-6">
      {/* Contact Display Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Contact Display Settings</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="show_phone">Show Phone Number</Label>
              <p className="text-xs text-gray-500">
                Display phone number on the website
              </p>
            </div>
            <Switch
              id="show_phone"
              checked={getValue("show_phone") !== false}
              onCheckedChange={(checked) => onChange("show_phone", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="show_email">Show Email Address</Label>
              <p className="text-xs text-gray-500">
                Display email address on the website
              </p>
            </div>
            <Switch
              id="show_email"
              checked={getValue("show_email") !== false}
              onCheckedChange={(checked) => onChange("show_email", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="show_address">Show Physical Address</Label>
              <p className="text-xs text-gray-500">
                Display physical address on the website
              </p>
            </div>
            <Switch
              id="show_address"
              checked={getValue("show_address") !== false}
              onCheckedChange={(checked) => onChange("show_address", checked)}
            />
          </div>
        </div>
      </div>

      {/* Business Hours */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Business Hours</h3>
        
        <div>
          <Label htmlFor="business_hours">Business Hours Display</Label>
          <Textarea
            id="business_hours"
            value={getValue("business_hours") || ""}
            onChange={(e) => onChange("business_hours", e.target.value)}
            placeholder="Monday-Friday: 8AM-6PM&#10;Saturday: 9AM-2PM&#10;Sunday: Closed"
            rows={3}
          />
          <p className="text-xs text-gray-500 mt-1">
            Business hours text displayed on contact page and footer
          </p>
        </div>

        <div>
          <Label htmlFor="emergency_support">Emergency Support Message</Label>
          <Input
            id="emergency_support"
            value={getValue("emergency_support") || ""}
            onChange={(e) => onChange("emergency_support", e.target.value)}
            placeholder="24/7 Emergency Support Available"
          />
          <p className="text-xs text-gray-500 mt-1">
            Message about emergency or after-hours support
          </p>
        </div>
      </div>

      {/* Contact Form Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Contact Form Settings</h3>
        
        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="contact_form_enabled">Enable Contact Form</Label>
            <p className="text-xs text-gray-500">
              Show contact form on the contact page
            </p>
          </div>
          <Switch
            id="contact_form_enabled"
            checked={getValue("contact_form_enabled") !== false}
            onCheckedChange={(checked) => onChange("contact_form_enabled", checked)}
          />
        </div>

        <div>
          <Label htmlFor="contact_form_recipient">Form Recipient Email</Label>
          <Input
            id="contact_form_recipient"
            type="email"
            value={getValue("contact_form_recipient") || ""}
            onChange={(e) => onChange("contact_form_recipient", e.target.value)}
            placeholder="<EMAIL>"
          />
          <p className="text-xs text-gray-500 mt-1">
            Email address to receive contact form submissions
          </p>
        </div>

        <div>
          <Label htmlFor="contact_form_subject">Default Subject</Label>
          <Input
            id="contact_form_subject"
            value={getValue("contact_form_subject") || ""}
            onChange={(e) => onChange("contact_form_subject", e.target.value)}
            placeholder="New Contact Form Submission"
          />
          <p className="text-xs text-gray-500 mt-1">
            Default subject line for contact form emails
          </p>
        </div>
      </div>
    </div>
  );
};
