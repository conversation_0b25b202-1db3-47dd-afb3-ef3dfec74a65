import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface GeneralSettingsFormProps {
  settings: Record<string, any>;
  onChange: (key: string, value: any) => void;
  getValue: (key: string) => any;
}

export const GeneralSettingsForm = ({ settings, onChange, getValue }: GeneralSettingsFormProps) => {
  const languages = [
    { value: "en", label: "English" },
    { value: "es", label: "Español" },
    { value: "fr", label: "Français" },
  ];

  const currencies = [
    { value: "USD", label: "US Dollar (USD)" },
    { value: "EUR", label: "Euro (EUR)" },
    { value: "GNF", label: "Guinean Franc (GNF)" },
  ];

  const timezones = [
    { value: "Africa/Conakry", label: "Africa/Conakry (GMT+0)" },
    { value: "UTC", label: "UTC" },
    { value: "Europe/London", label: "Europe/London" },
    { value: "Europe/Paris", label: "Europe/Paris" },
    { value: "America/New_York", label: "America/New_York" },
  ];

  return (
    <div className="space-y-6">
      {/* Site Identity */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Site Identity</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="site_name">Site Name</Label>
            <Input
              id="site_name"
              value={getValue("site_name") || ""}
              onChange={(e) => onChange("site_name", e.target.value)}
              placeholder="OfficeTech Guinea"
            />
            <p className="text-xs text-gray-500 mt-1">
              The name of your website displayed in the browser title and header
            </p>
          </div>

          <div>
            <Label htmlFor="site_url">Site URL</Label>
            <Input
              id="site_url"
              type="url"
              value={getValue("site_url") || ""}
              onChange={(e) => onChange("site_url", e.target.value)}
              placeholder="https://officetech.gn"
            />
            <p className="text-xs text-gray-500 mt-1">
              The primary URL of your website
            </p>
          </div>
        </div>

        <div>
          <Label htmlFor="site_description">Site Description</Label>
          <Textarea
            id="site_description"
            value={getValue("site_description") || ""}
            onChange={(e) => onChange("site_description", e.target.value)}
            placeholder="Leading technology and security solutions in Guinea"
            rows={3}
          />
          <p className="text-xs text-gray-500 mt-1">
            A brief description of your website and services
          </p>
        </div>
      </div>

      {/* Contact Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Contact Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="admin_email">Admin Email</Label>
            <Input
              id="admin_email"
              type="email"
              value={getValue("admin_email") || ""}
              onChange={(e) => onChange("admin_email", e.target.value)}
              placeholder="<EMAIL>"
            />
            <p className="text-xs text-gray-500 mt-1">
              Primary admin email for system notifications
            </p>
          </div>

          <div>
            <Label htmlFor="support_email">Support Email</Label>
            <Input
              id="support_email"
              type="email"
              value={getValue("support_email") || ""}
              onChange={(e) => onChange("support_email", e.target.value)}
              placeholder="<EMAIL>"
            />
            <p className="text-xs text-gray-500 mt-1">
              Customer support email address
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              type="tel"
              value={getValue("phone") || ""}
              onChange={(e) => onChange("phone", e.target.value)}
              placeholder="+224 123 456 789"
            />
            <p className="text-xs text-gray-500 mt-1">
              Main business phone number
            </p>
          </div>

          <div>
            <Label htmlFor="address">Business Address</Label>
            <Input
              id="address"
              value={getValue("address") || ""}
              onChange={(e) => onChange("address", e.target.value)}
              placeholder="Conakry, Guinea"
            />
            <p className="text-xs text-gray-500 mt-1">
              Physical business address
            </p>
          </div>
        </div>
      </div>

      {/* Localization */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Localization</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="language">Default Language</Label>
            <Select 
              value={getValue("language") || "en"} 
              onValueChange={(value) => onChange("language", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                {languages.map((lang) => (
                  <SelectItem key={lang.value} value={lang.value}>
                    {lang.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500 mt-1">
              Default language for the website
            </p>
          </div>

          <div>
            <Label htmlFor="currency">Default Currency</Label>
            <Select 
              value={getValue("currency") || "USD"} 
              onValueChange={(value) => onChange("currency", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select currency" />
              </SelectTrigger>
              <SelectContent>
                {currencies.map((currency) => (
                  <SelectItem key={currency.value} value={currency.value}>
                    {currency.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500 mt-1">
              Default currency for pricing
            </p>
          </div>

          <div>
            <Label htmlFor="timezone">Timezone</Label>
            <Select 
              value={getValue("timezone") || "Africa/Conakry"} 
              onValueChange={(value) => onChange("timezone", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select timezone" />
              </SelectTrigger>
              <SelectContent>
                {timezones.map((tz) => (
                  <SelectItem key={tz.value} value={tz.value}>
                    {tz.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500 mt-1">
              Default timezone for dates and times
            </p>
          </div>
        </div>
      </div>

      {/* Additional Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Additional Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="company_registration">Company Registration</Label>
            <Input
              id="company_registration"
              value={getValue("company_registration") || ""}
              onChange={(e) => onChange("company_registration", e.target.value)}
              placeholder="Registration number"
            />
            <p className="text-xs text-gray-500 mt-1">
              Official company registration number
            </p>
          </div>

          <div>
            <Label htmlFor="tax_id">Tax ID</Label>
            <Input
              id="tax_id"
              value={getValue("tax_id") || ""}
              onChange={(e) => onChange("tax_id", e.target.value)}
              placeholder="Tax identification number"
            />
            <p className="text-xs text-gray-500 mt-1">
              Tax identification number
            </p>
          </div>
        </div>

        <div>
          <Label htmlFor="legal_notice">Legal Notice</Label>
          <Textarea
            id="legal_notice"
            value={getValue("legal_notice") || ""}
            onChange={(e) => onChange("legal_notice", e.target.value)}
            placeholder="Legal notice and terms information"
            rows={3}
          />
          <p className="text-xs text-gray-500 mt-1">
            Legal notice displayed in footer or legal pages
          </p>
        </div>
      </div>
    </div>
  );
};
