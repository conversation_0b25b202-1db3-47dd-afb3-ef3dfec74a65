import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Zap, Clock, Image, Archive } from "lucide-react";

interface PerformanceSettingsFormProps {
  settings: Record<string, any>;
  onChange: (key: string, value: any) => void;
  getValue: (key: string) => any;
}

export const PerformanceSettingsForm = ({ settings, onChange, getValue }: PerformanceSettingsFormProps) => {
  const cacheOptions = [
    { value: 300, label: "5 minutes" },
    { value: 900, label: "15 minutes" },
    { value: 1800, label: "30 minutes" },
    { value: 3600, label: "1 hour" },
    { value: 7200, label: "2 hours" },
    { value: 21600, label: "6 hours" },
    { value: 43200, label: "12 hours" },
    { value: 86400, label: "24 hours" },
  ];

  const compressionLevels = [
    { value: 1, label: "Low (Fastest)" },
    { value: 6, label: "Medium (Balanced)" },
    { value: 9, label: "High (Best Compression)" },
  ];

  return (
    <div className="space-y-6">
      {/* Caching Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Caching Settings</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="cache_duration">Cache Duration</Label>
            <Select 
              value={getValue("cache_duration")?.toString() || "3600"} 
              onValueChange={(value) => onChange("cache_duration", parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select cache duration" />
              </SelectTrigger>
              <SelectContent>
                {cacheOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500 mt-1">
              How long to cache static content
            </p>
          </div>

          <div>
            <Label htmlFor="browser_cache_duration">Browser Cache Duration</Label>
            <Select 
              value={getValue("browser_cache_duration")?.toString() || "86400"} 
              onValueChange={(value) => onChange("browser_cache_duration", parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select browser cache duration" />
              </SelectTrigger>
              <SelectContent>
                {cacheOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500 mt-1">
              How long browsers should cache content
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="enable_caching">Enable Caching</Label>
              <p className="text-xs text-gray-500">
                Enable server-side caching for better performance
              </p>
            </div>
            <Switch
              id="enable_caching"
              checked={getValue("enable_caching") !== false}
              onCheckedChange={(checked) => onChange("enable_caching", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="cache_api_responses">Cache API Responses</Label>
              <p className="text-xs text-gray-500">
                Cache API responses to reduce server load
              </p>
            </div>
            <Switch
              id="cache_api_responses"
              checked={getValue("cache_api_responses") !== false}
              onCheckedChange={(checked) => onChange("cache_api_responses", checked)}
            />
          </div>
        </div>
      </div>

      {/* Image Optimization */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Image Optimization</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="image_optimization">Enable Image Optimization</Label>
              <p className="text-xs text-gray-500">
                Automatically optimize images for web delivery
              </p>
            </div>
            <Switch
              id="image_optimization"
              checked={getValue("image_optimization") !== false}
              onCheckedChange={(checked) => onChange("image_optimization", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="lazy_loading">Enable Lazy Loading</Label>
              <p className="text-xs text-gray-500">
                Load images only when they're about to be viewed
              </p>
            </div>
            <Switch
              id="lazy_loading"
              checked={getValue("lazy_loading") !== false}
              onCheckedChange={(checked) => onChange("lazy_loading", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="webp_conversion">WebP Conversion</Label>
              <p className="text-xs text-gray-500">
                Convert images to WebP format for better compression
              </p>
            </div>
            <Switch
              id="webp_conversion"
              checked={getValue("webp_conversion") !== false}
              onCheckedChange={(checked) => onChange("webp_conversion", checked)}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="image_quality">Image Quality (%)</Label>
            <Input
              id="image_quality"
              type="number"
              min="1"
              max="100"
              value={getValue("image_quality") || 85}
              onChange={(e) => onChange("image_quality", parseInt(e.target.value) || 85)}
            />
            <p className="text-xs text-gray-500 mt-1">
              Image compression quality (1-100)
            </p>
          </div>

          <div>
            <Label htmlFor="max_image_width">Max Image Width (px)</Label>
            <Input
              id="max_image_width"
              type="number"
              min="100"
              max="4000"
              value={getValue("max_image_width") || 1920}
              onChange={(e) => onChange("max_image_width", parseInt(e.target.value) || 1920)}
            />
            <p className="text-xs text-gray-500 mt-1">
              Maximum width for uploaded images
            </p>
          </div>
        </div>
      </div>

      {/* Compression Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Compression Settings</h3>
        
        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="compression_enabled">Enable Compression</Label>
            <p className="text-xs text-gray-500">
              Compress content before sending to browsers
            </p>
          </div>
          <Switch
            id="compression_enabled"
            checked={getValue("compression_enabled") !== false}
            onCheckedChange={(checked) => onChange("compression_enabled", checked)}
          />
        </div>

        <div>
          <Label htmlFor="compression_level">Compression Level</Label>
          <Select 
            value={getValue("compression_level")?.toString() || "6"} 
            onValueChange={(value) => onChange("compression_level", parseInt(value))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select compression level" />
            </SelectTrigger>
            <SelectContent>
              {compressionLevels.map((level) => (
                <SelectItem key={level.value} value={level.value.toString()}>
                  {level.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-gray-500 mt-1">
            Higher levels provide better compression but use more CPU
          </p>
        </div>
      </div>

      {/* Loading Optimization */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Loading Optimization</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="preload_critical_resources">Preload Critical Resources</Label>
              <p className="text-xs text-gray-500">
                Preload important CSS and JavaScript files
              </p>
            </div>
            <Switch
              id="preload_critical_resources"
              checked={getValue("preload_critical_resources") !== false}
              onCheckedChange={(checked) => onChange("preload_critical_resources", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="minify_css">Minify CSS</Label>
              <p className="text-xs text-gray-500">
                Remove whitespace and comments from CSS files
              </p>
            </div>
            <Switch
              id="minify_css"
              checked={getValue("minify_css") !== false}
              onCheckedChange={(checked) => onChange("minify_css", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="minify_js">Minify JavaScript</Label>
              <p className="text-xs text-gray-500">
                Remove whitespace and comments from JavaScript files
              </p>
            </div>
            <Switch
              id="minify_js"
              checked={getValue("minify_js") !== false}
              onCheckedChange={(checked) => onChange("minify_js", checked)}
            />
          </div>
        </div>
      </div>

      {/* Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Performance Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Clock className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <div className="text-sm font-medium text-gray-900">Cache Duration</div>
              <div className="text-xs text-gray-600">
                {cacheOptions.find(o => o.value === getValue("cache_duration"))?.label || "1 hour"}
              </div>
            </div>

            <div className="text-center p-4 bg-green-50 rounded-lg">
              <Image className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <div className="text-sm font-medium text-gray-900">Image Quality</div>
              <div className="text-xs text-gray-600">
                {getValue("image_quality") || 85}%
              </div>
            </div>

            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <Archive className="w-6 h-6 text-purple-600 mx-auto mb-2" />
              <div className="text-sm font-medium text-gray-900">Compression</div>
              <div className="text-xs text-gray-600">
                {getValue("compression_enabled") !== false ? "Enabled" : "Disabled"}
              </div>
            </div>

            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <Zap className="w-6 h-6 text-orange-600 mx-auto mb-2" />
              <div className="text-sm font-medium text-gray-900">Optimizations</div>
              <div className="text-xs text-gray-600">
                {[
                  getValue("lazy_loading") !== false,
                  getValue("image_optimization") !== false,
                  getValue("minify_css") !== false,
                  getValue("minify_js") !== false,
                ].filter(Boolean).length}/4 Active
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
