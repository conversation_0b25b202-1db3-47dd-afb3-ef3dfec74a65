import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Mail, TestTube, AlertTriangle } from "lucide-react";

interface EmailSettingsFormProps {
  settings: Record<string, any>;
  onChange: (key: string, value: any) => void;
  getValue: (key: string) => any;
}

export const EmailSettingsForm = ({ settings, onChange, getValue }: EmailSettingsFormProps) => {
  return (
    <div className="space-y-6">
      {/* SMTP Configuration */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">SMTP Configuration</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="smtp_host">SMTP Host</Label>
            <Input
              id="smtp_host"
              value={getValue("smtp_host") || ""}
              onChange={(e) => onChange("smtp_host", e.target.value)}
              placeholder="smtp.gmail.com"
            />
          </div>

          <div>
            <Label htmlFor="smtp_port">SMTP Port</Label>
            <Input
              id="smtp_port"
              type="number"
              value={getValue("smtp_port") || ""}
              onChange={(e) => onChange("smtp_port", parseInt(e.target.value) || 587)}
              placeholder="587"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="smtp_username">SMTP Username</Label>
            <Input
              id="smtp_username"
              value={getValue("smtp_username") || ""}
              onChange={(e) => onChange("smtp_username", e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <Label htmlFor="smtp_password">SMTP Password</Label>
            <Input
              id="smtp_password"
              type="password"
              value={getValue("smtp_password") || ""}
              onChange={(e) => onChange("smtp_password", e.target.value)}
              placeholder="••••••••"
            />
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="smtp_secure">Use SSL/TLS</Label>
            <p className="text-xs text-gray-500">
              Enable secure connection (recommended)
            </p>
          </div>
          <Switch
            id="smtp_secure"
            checked={getValue("smtp_secure") !== false}
            onCheckedChange={(checked) => onChange("smtp_secure", checked)}
          />
        </div>
      </div>

      {/* Email Sender Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Email Sender Settings</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="from_name">From Name</Label>
            <Input
              id="from_name"
              value={getValue("from_name") || ""}
              onChange={(e) => onChange("from_name", e.target.value)}
              placeholder="OfficeTech Guinea"
            />
          </div>

          <div>
            <Label htmlFor="from_email">From Email</Label>
            <Input
              id="from_email"
              type="email"
              value={getValue("from_email") || ""}
              onChange={(e) => onChange("from_email", e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="reply_to_name">Reply-To Name</Label>
            <Input
              id="reply_to_name"
              value={getValue("reply_to_name") || ""}
              onChange={(e) => onChange("reply_to_name", e.target.value)}
              placeholder="OfficeTech Support"
            />
          </div>

          <div>
            <Label htmlFor="reply_to_email">Reply-To Email</Label>
            <Input
              id="reply_to_email"
              type="email"
              value={getValue("reply_to_email") || ""}
              onChange={(e) => onChange("reply_to_email", e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>
        </div>
      </div>

      {/* Email Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="w-5 h-5" />
            Test Email Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="test_email">Test Email Address</Label>
            <Input
              id="test_email"
              type="email"
              placeholder="<EMAIL>"
            />
          </div>
          <Button className="w-full">
            <Mail className="w-4 h-4 mr-2" />
            Send Test Email
          </Button>
          <p className="text-xs text-gray-500">
            Send a test email to verify your SMTP configuration
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
