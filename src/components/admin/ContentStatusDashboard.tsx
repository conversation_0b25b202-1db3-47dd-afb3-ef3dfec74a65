import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  FileText, 
  Globe, 
  CheckCircle, 
  AlertCircle, 
  Network, 
  GraduationCap, 
  Shield, 
  Briefcase,
  Users,
  Clock,
  Award
} from "lucide-react";
import { Link } from "react-router-dom";

export const ContentStatusDashboard = () => {
  const allContent = useQuery(api.content.getAllContent, {
    language: "en",
    status: "published"
  });

  const contentTypes = useQuery(api.contentTypes.getAllContentTypes);

  const getContentByType = (typeName: string) => {
    return allContent?.filter(item => item.contentType?.name === typeName) || [];
  };

  const getIconForType = (typeName: string) => {
    switch (typeName) {
      case "service_card": return Shield;
      case "network_solution": return Network;
      case "training_program": return GraduationCap;
      case "hero_section": return FileText;
      case "value_card": return Briefcase;
      case "timeline_item": return Clock;
      default: return FileText;
    }
  };

  const getStatusColor = (count: number, expectedMin: number = 1) => {
    if (count >= expectedMin) return "bg-green-100 text-green-800";
    if (count > 0) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const contentTypeStats = [
    {
      name: "service_card",
      label: "Service Cards",
      description: "Main services offered by the company",
      expectedCount: 5,
      content: getContentByType("service_card")
    },
    {
      name: "network_solution",
      label: "Network Solutions",
      description: "Network connectivity solutions",
      expectedCount: 4,
      content: getContentByType("network_solution")
    },
    {
      name: "training_program",
      label: "Training Programs",
      description: "Professional training programs",
      expectedCount: 5,
      content: getContentByType("training_program")
    },
    {
      name: "hero_section",
      label: "Hero Sections",
      description: "Page header sections",
      expectedCount: 4,
      content: getContentByType("hero_section")
    },
    {
      name: "value_card",
      label: "Value Cards",
      description: "Company values and mission",
      expectedCount: 3,
      content: getContentByType("value_card")
    },
    {
      name: "timeline_item",
      label: "Timeline Items",
      description: "Company history timeline",
      expectedCount: 5,
      content: getContentByType("timeline_item")
    }
  ];

  if (!allContent || !contentTypes) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Content Status Dashboard</h2>
          <p className="text-gray-600">
            Overview of all content types and their current status
          </p>
        </div>
        <div className="flex space-x-2">
          <Link to="/admin/content">
            <Button>
              <FileText className="w-4 h-4 mr-2" />
              Manage Content
            </Button>
          </Link>
          <Link to="/admin/setup">
            <Button variant="outline">
              Initialize Content
            </Button>
          </Link>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Content Items</p>
                <p className="text-2xl font-bold text-gray-900">{allContent.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Globe className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Content Types</p>
                <p className="text-2xl font-bold text-gray-900">{contentTypes.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Published</p>
                <p className="text-2xl font-bold text-gray-900">
                  {allContent.filter(item => item.status === "published").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <AlertCircle className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Draft</p>
                <p className="text-2xl font-bold text-gray-900">
                  {allContent.filter(item => item.status === "draft").length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Content Type Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {contentTypeStats.map((stat) => {
          const Icon = getIconForType(stat.name);
          const count = stat.content.length;
          const statusColor = getStatusColor(count, stat.expectedCount);
          
          return (
            <Card key={stat.name} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Icon className="w-5 h-5 text-blue-600" />
                    {stat.label}
                  </CardTitle>
                  <Badge className={statusColor}>
                    {count}/{stat.expectedCount}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">{stat.description}</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {stat.content.length > 0 ? (
                    <div className="space-y-1">
                      {stat.content.slice(0, 3).map((item) => (
                        <div key={item._id} className="flex items-center justify-between text-sm">
                          <span className="truncate">{item.data.title || item.identifier}</span>
                          <Badge variant="outline" className="text-xs">
                            {item.language}
                          </Badge>
                        </div>
                      ))}
                      {stat.content.length > 3 && (
                        <p className="text-xs text-gray-500">
                          +{stat.content.length - 3} more items
                        </p>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 italic">No content items found</p>
                  )}
                </div>
                
                <div className="mt-4 pt-4 border-t">
                  <Link to="/admin/content">
                    <Button variant="outline" size="sm" className="w-full">
                      Manage {stat.label}
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <p className="text-sm text-gray-600">Common content management tasks</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link to="/admin/setup">
              <Button variant="outline" className="w-full justify-start">
                <FileText className="w-4 h-4 mr-2" />
                Initialize Content from content.md
              </Button>
            </Link>
            <Link to="/admin/content?type=service_card">
              <Button variant="outline" className="w-full justify-start">
                <Shield className="w-4 h-4 mr-2" />
                Edit Services
              </Button>
            </Link>
            <Link to="/admin/content?type=network_solution">
              <Button variant="outline" className="w-full justify-start">
                <Network className="w-4 h-4 mr-2" />
                Edit Network Solutions
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
