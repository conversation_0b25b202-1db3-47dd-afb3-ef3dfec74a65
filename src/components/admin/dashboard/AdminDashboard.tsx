import { useAdminStats } from "@/hooks/useAdminRoles";
import { useAuth } from "@/hooks/useAuth";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  FileText, 
  Image, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  ArrowRight,
  Activity,
  Globe,
  Shield
} from "lucide-react";
import { Link } from "react-router-dom";

export const AdminDashboard = () => {
  const { user, canEditContent, canManageUsers, canViewAnalytics } = useAuth();
  const { stats, isLoading } = useAdminStats();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              Welcome back, {user?.firstName || 'Admin'}!
            </h1>
            <p className="text-blue-100">
              Here's what's happening with your website today.
            </p>
          </div>
          <div className="hidden md:flex items-center space-x-2">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
              <Shield className="w-3 h-3 mr-1" />
              {user?.role?.replace('_', ' ').toUpperCase()}
            </Badge>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Users"
            value={stats.users.total}
            subtitle={`${stats.users.active} active`}
            icon={Users}
            color="blue"
            trend={`+${stats.users.recentSignups.length} this week`}
          />
          
          <StatCard
            title="Content Items"
            value={stats.content.total}
            subtitle={`${stats.content.published} published`}
            icon={FileText}
            color="green"
            trend={`${stats.content.draft} drafts`}
          />
          
          <StatCard
            title="Media Files"
            value={stats.media.total}
            subtitle={formatFileSize(stats.media.totalSize)}
            icon={Image}
            color="purple"
            trend="Storage used"
          />
          
          <StatCard
            title="Languages"
            value={Object.keys(stats.content.byLanguage).length}
            subtitle="Supported"
            icon={Globe}
            color="orange"
            trend="Multi-language"
          />
        </div>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="w-5 h-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {canEditContent && (
              <>
                <QuickActionButton
                  to="/admin/content/new"
                  icon={FileText}
                  title="Create Content"
                  description="Add new page or section content"
                />
                <QuickActionButton
                  to="/admin/media"
                  icon={Image}
                  title="Upload Media"
                  description="Add images, videos, or documents"
                />
              </>
            )}
            
            {canManageUsers && (
              <QuickActionButton
                to="/admin/users"
                icon={Users}
                title="Manage Users"
                description="Add or modify user accounts"
              />
            )}
            
            <QuickActionButton
              to="/admin/settings"
              icon={Shield}
              title="Site Settings"
              description="Configure website settings"
            />
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            {stats?.users.recentSignups.length ? (
              <div className="space-y-3">
                {stats.users.recentSignups.slice(0, 5).map((signup) => (
                  <div key={signup.id} className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <Users className="w-4 h-4 text-blue-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {signup.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        Joined as {signup.role.replace('_', ' ')} • {formatTimeAgo(signup.createdAt)}
                      </p>
                    </div>
                  </div>
                ))}
                
                {canManageUsers && (
                  <Link to="/admin/users">
                    <Button variant="ghost" size="sm" className="w-full mt-3">
                      View All Users
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                )}
              </div>
            ) : (
              <div className="text-center py-6 text-gray-500">
                <Activity className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No recent activity</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Content Overview */}
      {canEditContent && stats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Content Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Content Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium">Published</span>
                  </div>
                  <Badge variant="default">{stats.content.published}</Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-yellow-600" />
                    <span className="text-sm font-medium">Drafts</span>
                  </div>
                  <Badge variant="secondary">{stats.content.draft}</Badge>
                </div>

                <div className="pt-3 border-t">
                  <Link to="/admin/content">
                    <Button variant="outline" size="sm" className="w-full">
                      Manage Content
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Languages */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="w-5 h-5" />
                Languages
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(stats.content.byLanguage).map(([language, count]) => (
                  <div key={language} className="flex items-center justify-between">
                    <span className="text-sm font-medium capitalize">
                      {language === 'en' ? 'English' : language === 'es' ? 'Spanish' : language}
                    </span>
                    <Badge variant="outline">{count} items</Badge>
                  </div>
                ))}
                
                <div className="pt-3 border-t">
                  <Link to="/admin/translations">
                    <Button variant="outline" size="sm" className="w-full">
                      Manage Translations
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            System Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <StatusItem
              label="Database"
              status="operational"
              description="All systems running normally"
            />
            <StatusItem
              label="Media Storage"
              status="operational"
              description="Files uploading successfully"
            />
            <StatusItem
              label="Authentication"
              status="operational"
              description="User login working properly"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Stat Card Component
const StatCard = ({ 
  title, 
  value, 
  subtitle, 
  icon: Icon, 
  color, 
  trend 
}: {
  title: string;
  value: number;
  subtitle: string;
  icon: any;
  color: string;
  trend: string;
}) => {
  const colorClasses = {
    blue: "text-blue-600 bg-blue-100",
    green: "text-green-600 bg-green-100",
    purple: "text-purple-600 bg-purple-100",
    orange: "text-orange-600 bg-orange-100",
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value.toLocaleString()}</p>
            <p className="text-sm text-gray-500">{subtitle}</p>
          </div>
          <div className={`p-3 rounded-full ${colorClasses[color as keyof typeof colorClasses]}`}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
        <div className="mt-4">
          <p className="text-xs text-gray-500">{trend}</p>
        </div>
      </CardContent>
    </Card>
  );
};

// Quick Action Button Component
const QuickActionButton = ({ 
  to, 
  icon: Icon, 
  title, 
  description 
}: {
  to: string;
  icon: any;
  title: string;
  description: string;
}) => (
  <Link to={to}>
    <Button variant="ghost" className="w-full justify-start h-auto p-3">
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-blue-100 rounded-lg">
          <Icon className="w-4 h-4 text-blue-600" />
        </div>
        <div className="text-left">
          <p className="font-medium">{title}</p>
          <p className="text-sm text-gray-500">{description}</p>
        </div>
      </div>
    </Button>
  </Link>
);

// Status Item Component
const StatusItem = ({ 
  label, 
  status, 
  description 
}: {
  label: string;
  status: string;
  description: string;
}) => (
  <div className="flex items-center space-x-3">
    <div className="flex-shrink-0">
      {status === "operational" ? (
        <CheckCircle className="w-5 h-5 text-green-600" />
      ) : (
        <AlertCircle className="w-5 h-5 text-red-600" />
      )}
    </div>
    <div>
      <p className="text-sm font-medium text-gray-900">{label}</p>
      <p className="text-xs text-gray-500">{description}</p>
    </div>
  </div>
);

// Utility functions
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const formatTimeAgo = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 1) return "Just now";
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  return `${days}d ago`;
};
