import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Layout, CheckCircle, AlertCircle } from "lucide-react";
import { toast } from "sonner";

export const FooterSetupButton = () => {
  const [isInitializing, setIsInitializing] = useState(false);
  const [setupResults, setSetupResults] = useState<any>(null);

  const initializeFooterContent = useMutation(api.content.initializeFooterContent);

  const handleInitialize = async () => {
    setIsInitializing(true);
    try {
      const results = await initializeFooterContent({});
      setSetupResults(results);
      
      const createdCount = results.filter((r: any) => r.status === 'created').length;
      const existingCount = results.filter((r: any) => r.status === 'exists').length;
      
      if (createdCount > 0) {
        toast.success(`Footer content initialized! Created ${createdCount} new entries.`);
      } else if (existingCount > 0) {
        toast.info("Footer content already exists.");
      }
    } catch (error) {
      console.error("Footer setup failed:", error);
      toast.error("Failed to initialize footer content. Check console for details.");
      setSetupResults([]);
    } finally {
      setIsInitializing(false);
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layout className="w-5 h-5" />
            Footer Content Setup
          </CardTitle>
          <p className="text-sm text-gray-600">
            Initialize default footer content for both English and Spanish languages.
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button
              onClick={handleInitialize}
              disabled={isInitializing}
              className="w-full"
            >
              {isInitializing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Initializing Footer Content...
                </>
              ) : (
                <>
                  <Layout className="w-4 h-4 mr-2" />
                  Initialize Footer Content
                </>
              )}
            </Button>

            {setupResults && (
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Setup Results:</h4>
                {setupResults.map((result: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div className="flex items-center gap-2">
                      {result.status === 'created' ? (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      ) : (
                        <AlertCircle className="w-4 h-4 text-yellow-600" />
                      )}
                      <span className="text-sm">
                        Footer Content ({result.language.toUpperCase()})
                      </span>
                    </div>
                    <Badge variant={result.status === 'created' ? 'default' : 'secondary'}>
                      {result.status}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
