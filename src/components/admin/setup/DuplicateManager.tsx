import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  AlertTriangle, 
  Trash2, 
  Eye, 
  Calendar,
  FileText,
  CheckCircle,
  Loader2
} from "lucide-react";
import { toast } from "sonner";
import { formatDistanceToNow } from "date-fns";

export const DuplicateManager = () => {
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const duplicates = useQuery(api.content.findDuplicateContent);
  const deleteContent = useMutation(api.content.deleteContent);

  const handleDeleteDuplicate = async (contentId: string, identifier: string, language: string) => {
    if (!confirm(`Are you sure you want to delete this duplicate content for "${identifier}" (${language})?`)) {
      return;
    }

    setIsDeleting(contentId);
    try {
      await deleteContent({ id: contentId as any });
      toast.success("Duplicate content deleted successfully");
    } catch (error) {
      toast.error("Failed to delete duplicate content");
      console.error("Delete error:", error);
    } finally {
      setIsDeleting(null);
    }
  };

  if (duplicates === undefined) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span>Checking for duplicates...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!duplicates || duplicates.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            No Duplicates Found
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            All content entries are unique. No duplicate content found.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-orange-600" />
          Duplicate Content Found ({duplicates.length})
        </CardTitle>
        <p className="text-sm text-gray-600">
          Multiple content entries found with the same identifier and language. 
          Keep the most recent version and delete the duplicates.
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Warning:</strong> Deleting content is permanent. Make sure to keep the most recent or correct version.
          </AlertDescription>
        </Alert>

        {duplicates.map((duplicate) => (
          <Card key={duplicate.key} className="border-orange-200">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">
                    {duplicate.identifier.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </h4>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline">{duplicate.language.toUpperCase()}</Badge>
                    <Badge variant="destructive">{duplicate.count} duplicates</Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-3">
                {duplicate.items.map((item, index) => (
                  <div 
                    key={item._id} 
                    className={`flex items-center justify-between p-3 rounded-lg border ${
                      index === 0 
                        ? 'border-green-200 bg-green-50' 
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        {index === 0 && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            Keep (Most Recent)
                          </Badge>
                        )}
                        <Badge variant="outline" className="text-xs">
                          {item.status}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          v{item.version}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          <span>
                            Updated {formatDistanceToNow(new Date(item.updatedAt), { addSuffix: true })}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <FileText className="w-3 h-3" />
                          <span>ID: {item._id.slice(-8)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {index === 0 ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Keep This
                        </Badge>
                      ) : (
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteDuplicate(item._id, duplicate.identifier, duplicate.language)}
                          disabled={isDeleting === item._id}
                        >
                          {isDeleting === item._id ? (
                            <>
                              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                              Deleting...
                            </>
                          ) : (
                            <>
                              <Trash2 className="w-3 h-3 mr-1" />
                              Delete
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </CardContent>
    </Card>
  );
};
