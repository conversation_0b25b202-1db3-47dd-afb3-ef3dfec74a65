import { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, AlertCircle, Loader2, Play, RefreshCw } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";

export const CMSSetup = () => {
  const [isInitializing, setIsInitializing] = useState(false);
  const [setupResults, setSetupResults] = useState<any>(null);
  const [isAddingSpanish, setIsAddingSpanish] = useState(false);
  const [spanishResults, setSpanishResults] = useState<any>(null);

  const initializeCMS = useMutation(api.setup.initializeCMS);
  const autoInitializeCMS = useMutation(api.autoSetup.autoInitializeCMS);
  const checkStatus = useMutation(api.setup.checkCMSStatus);
  const addSpanishContent = useMutation(api.setup.addSpanishContent);
  const initializeAllContent = useMutation(api.content.initializeAllContent);
  const autoStatus = useQuery(api.autoSetup.checkInitializationStatus);
  
  const handleInitialize = async () => {
    setIsInitializing(true);
    try {
      const results = await initializeCMS({});
      setSetupResults(results);
    } catch (error) {
      console.error("Setup failed:", error);
      setSetupResults({
        contentTypes: [],
        content: [],
        errors: [error instanceof Error ? error.message : "Unknown error occurred"],
      });
    } finally {
      setIsInitializing(false);
    }
  };

  const handleAutoInitialize = async () => {
    setIsInitializing(true);
    try {
      const results = await autoInitializeCMS({});
      setSetupResults(results);
    } catch (error) {
      console.error("Auto setup failed:", error);
      setSetupResults({
        contentTypes: [],
        content: [],
        errors: [error instanceof Error ? error.message : "Unknown error occurred"],
      });
    } finally {
      setIsInitializing(false);
    }
  };

  const handleCheckStatus = async () => {
    try {
      const status = await checkStatus({});
      setSetupResults(status);
    } catch (error) {
      console.error("Status check failed:", error);
    }
  };

  const handleAddSpanishContent = async () => {
    setIsAddingSpanish(true);
    try {
      const results = await addSpanishContent({});
      setSpanishResults(results);
    } catch (error) {
      console.error("Adding Spanish content failed:", error);
      setSpanishResults({
        success: false,
        message: error instanceof Error ? error.message : "Unknown error occurred",
      });
    } finally {
      setIsAddingSpanish(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">CMS Setup</h2>
        <p className="text-gray-600">
          Initialize the Content Management System with default content types and initial content.
        </p>
      </div>

      {/* Auto Status Display */}
      {autoStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {autoStatus.isInitialized ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertCircle className="w-5 h-5 text-yellow-500" />
              )}
              <span>Current Status</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {autoStatus.contentTypesCount}
                </div>
                <div className="text-sm text-gray-600">Content Types</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {autoStatus.contentItemsCount}
                </div>
                <div className="text-sm text-gray-600">Content Items</div>
              </div>
            </div>
            <div className="mt-4">
              {autoStatus.isInitialized ? (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    ✅ CMS is fully initialized and ready to use! Content is automatically loaded when the app starts.
                  </AlertDescription>
                </Alert>
              ) : (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    ⚠️ CMS needs initialization. Use the buttons below or restart the app to auto-initialize.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex space-x-4">
        <Button
          onClick={handleAutoInitialize}
          disabled={isInitializing}
          className="flex items-center space-x-2"
          variant={autoStatus?.isInitialized ? "outline" : "default"}
        >
          {isInitializing ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Play className="w-4 h-4" />
          )}
          <span>{isInitializing ? "Initializing..." : "Quick Setup (Recommended)"}</span>
        </Button>

        <Button
          onClick={handleInitialize}
          disabled={isInitializing}
          variant="outline"
          className="flex items-center space-x-2"
        >
          {isInitializing ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Play className="w-4 h-4" />
          )}
          <span>Full Setup</span>
        </Button>

        <Button
          variant="outline"
          onClick={handleCheckStatus}
          className="flex items-center space-x-2"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Check Status</span>
        </Button>

        <Button
          onClick={handleAddSpanishContent}
          disabled={isAddingSpanish}
          variant="outline"
          className="flex items-center space-x-2 bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100"
        >
          {isAddingSpanish ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <span>🇪🇸</span>
          )}
          <span>{isAddingSpanish ? "Adding..." : "Add Spanish Content"}</span>
        </Button>

        <Button
          onClick={async () => {
            setIsInitializing(true);
            try {
              await initializeAllContent({});
              toast.success("All content from content.md has been initialized successfully!");
            } catch (error) {
              console.error("Error initializing all content:", error);
              toast.error("Failed to initialize content. Check console for details.");
            } finally {
              setIsInitializing(false);
            }
          }}
          disabled={isInitializing}
          variant="outline"
          className="flex items-center space-x-2 bg-purple-50 border-purple-200 text-purple-800 hover:bg-purple-100"
        >
          {isInitializing ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <span>📄</span>
          )}
          <span>{isInitializing ? "Initializing..." : "Load Content.md Data"}</span>
        </Button>
      </div>

      {setupResults && (
        <div className="space-y-4">
          {/* Status Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {setupResults.errors?.length > 0 ? (
                  <AlertCircle className="w-5 h-5 text-red-500" />
                ) : (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                )}
                <span>Setup Status</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {setupResults.contentTypesCount || setupResults.contentTypes?.length || 0}
                  </div>
                  <div className="text-sm text-gray-600">Content Types</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {setupResults.contentItemsCount || setupResults.content?.length || 0}
                  </div>
                  <div className="text-sm text-gray-600">Content Items</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {setupResults.errors?.length || 0}
                  </div>
                  <div className="text-sm text-gray-600">Errors</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Content Types */}
          {setupResults.contentTypes && setupResults.contentTypes.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Content Types</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {setupResults.contentTypes.map((ct: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="font-medium">{ct.name || ct.label}</span>
                      <span className={`px-2 py-1 text-xs rounded ${
                        ct.status === 'created' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                      }`}>
                        {ct.status || 'exists'}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Content Items */}
          {setupResults.content && setupResults.content.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Content Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {setupResults.content.map((item: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="font-medium">{item.identifier}</span>
                      <div className="flex items-center space-x-2">
                        {item.language && (
                          <span className="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded">
                            {item.language}
                          </span>
                        )}
                        <span className={`px-2 py-1 text-xs rounded ${
                          item.status === 'created' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                        }`}>
                          {item.status || 'exists'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Content Items List (for status check) */}
          {setupResults.contentItems && setupResults.contentItems.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Existing Content Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {setupResults.contentItems.map((item: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="font-medium">{item.identifier}</span>
                      <div className="flex items-center space-x-2">
                        <span className="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded">
                          {item.language}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded ${
                          item.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {item.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Errors */}
          {setupResults.errors && setupResults.errors.length > 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <strong>Errors occurred during setup:</strong>
                  {setupResults.errors.map((error: string, index: number) => (
                    <div key={index} className="text-sm">• {error}</div>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Success Message */}
          {setupResults.isInitialized && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                CMS is successfully initialized and ready to use!
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}

      {/* Spanish Content Results */}
      {spanishResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {spanishResults.success ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertCircle className="w-5 h-5 text-red-500" />
              )}
              <span>Spanish Content Addition</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className={spanishResults.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
              <AlertDescription>
                {spanishResults.message}
                {spanishResults.addedContent && (
                  <div className="mt-2">
                    <p className="font-medium">Added content:</p>
                    <ul className="list-disc list-inside text-sm">
                      {spanishResults.addedContent.map((item: any) => (
                        <li key={item.identifier}>{item.identifier}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
