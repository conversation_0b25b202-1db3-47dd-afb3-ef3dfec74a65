import { useState } from "react";
import { 
  getRoleHierarchy, 
  getPermissionsForRole, 
  formatPermissions,
  getRoleDisplayName,
  getRoleColor
} from "@/hooks/useAdminRoles";
import { useAuth, UserRole } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Shield, 
  Users, 
  Lock,
  CheckCircle,
  Info,
  Settings,
  Eye,
  Edit,
  Trash2,
  Plus
} from "lucide-react";

export const RoleManagement = () => {
  const { user: currentUser, isSuperAdmin } = useAuth();
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);

  const roleHierarchy = getRoleHierarchy();

  if (!isSuperAdmin) {
    return (
      <div className="text-center py-8">
        <Shield className="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <p className="text-gray-500">Super admin privileges required to manage roles</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Role Management</h2>
          <p className="text-gray-600">
            Manage user roles and permissions across the system
          </p>
        </div>
        
        <Button disabled>
          <Plus className="w-4 h-4 mr-2" />
          Create Custom Role
        </Button>
      </div>

      {/* Role Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {roleHierarchy.map((role) => (
          <RoleCard
            key={role.role}
            role={role}
            onViewDetails={() => setSelectedRole(role.role)}
          />
        ))}
      </div>

      {/* Role Hierarchy Visualization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Role Hierarchy
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-2 text-sm text-gray-600 mb-4">
              <Info className="w-4 h-4" />
              <span>Higher level roles inherit permissions from lower levels</span>
            </div>
            
            <div className="space-y-3">
              {roleHierarchy.reverse().map((role, index) => (
                <div key={role.role} className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      index === 0 ? 'bg-red-100 text-red-600' :
                      index === 1 ? 'bg-purple-100 text-purple-600' :
                      index === 2 ? 'bg-blue-100 text-blue-600' :
                      'bg-gray-100 text-gray-600'
                    }`}>
                      <span className="text-sm font-bold">{role.level}</span>
                    </div>
                    <Badge className={getRoleColor(role.role)}>
                      {role.name}
                    </Badge>
                  </div>
                  
                  <div className="flex-1">
                    <p className="text-sm text-gray-700">{role.description}</p>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedRole(role.role)}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permission Matrix */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock className="w-5 h-5" />
            Permission Matrix
          </CardTitle>
        </CardHeader>
        <CardContent>
          <PermissionMatrix />
        </CardContent>
      </Card>

      {/* Role Details Dialog */}
      {selectedRole && (
        <RoleDetailsDialog
          role={selectedRole}
          onClose={() => setSelectedRole(null)}
        />
      )}
    </div>
  );
};

// Role Card Component
const RoleCard = ({ 
  role, 
  onViewDetails 
}: { 
  role: any; 
  onViewDetails: () => void; 
}) => {
  const permissions = getPermissionsForRole(role.role);
  
  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={onViewDetails}>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Badge className={getRoleColor(role.role)}>
              {role.name}
            </Badge>
            <div className="text-xs text-gray-500">
              Level {role.level}
            </div>
          </div>
          
          <p className="text-sm text-gray-600">{role.description}</p>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">Permissions:</span>
              <span className="font-medium">{permissions.length}</span>
            </div>
            
            <div className="text-xs text-gray-500">
              {permissions.includes("*") ? "All permissions" : 
               permissions.slice(0, 2).join(", ") + 
               (permissions.length > 2 ? ` +${permissions.length - 2} more` : "")
              }
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Permission Matrix Component
const PermissionMatrix = () => {
  const roles = getRoleHierarchy();
  const allPermissions = [
    { category: "Content", permissions: ["read", "write", "publish", "delete"] },
    { category: "Media", permissions: ["read", "write", "upload", "delete"] },
    { category: "Users", permissions: ["read", "write"] },
    { category: "Analytics", permissions: ["read"] },
    { category: "Settings", permissions: ["read", "write"] },
  ];

  const hasPermission = (role: UserRole, category: string, permission: string): boolean => {
    const rolePermissions = getPermissionsForRole(role);
    if (rolePermissions.includes("*")) return true;
    
    const permissionKey = `${category.toLowerCase()}:${permission}`;
    return rolePermissions.includes(permissionKey);
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full text-sm">
        <thead>
          <tr className="border-b">
            <th className="text-left p-2">Permission</th>
            {roles.map(role => (
              <th key={role.role} className="text-center p-2 min-w-[100px]">
                <Badge className={getRoleColor(role.role)} variant="outline">
                  {role.name}
                </Badge>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {allPermissions.map(category => (
            category.permissions.map(permission => (
              <tr key={`${category.category}-${permission}`} className="border-b hover:bg-gray-50">
                <td className="p-2">
                  <div>
                    <span className="font-medium">{category.category}</span>
                    <span className="text-gray-500"> • {permission}</span>
                  </div>
                </td>
                {roles.map(role => (
                  <td key={role.role} className="text-center p-2">
                    {hasPermission(role.role, category.category, permission) ? (
                      <CheckCircle className="w-4 h-4 text-green-600 mx-auto" />
                    ) : (
                      <div className="w-4 h-4 mx-auto"></div>
                    )}
                  </td>
                ))}
              </tr>
            ))
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Role Details Dialog Component
const RoleDetailsDialog = ({ 
  role, 
  onClose 
}: { 
  role: UserRole; 
  onClose: () => void; 
}) => {
  const roleInfo = getRoleHierarchy().find(r => r.role === role);
  const permissions = getPermissionsForRole(role);

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            {roleInfo?.name} Role Details
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Role Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-600">Role Name</label>
              <p className="text-lg font-semibold">{roleInfo?.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Hierarchy Level</label>
              <p className="text-lg font-semibold">Level {roleInfo?.level}</p>
            </div>
          </div>

          <div>
            <label className="text-sm font-medium text-gray-600">Description</label>
            <p className="text-gray-900 mt-1">{roleInfo?.description}</p>
          </div>

          {/* Permissions */}
          <div>
            <label className="text-sm font-medium text-gray-600 mb-3 block">Permissions</label>
            <div className="bg-gray-50 rounded-lg p-4">
              {permissions.includes("*") ? (
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-800">All System Permissions</span>
                </div>
              ) : (
                <div className="space-y-2">
                  {permissions.map((permission, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm text-gray-700 capitalize">
                        {permission.replace(":", " • ")}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button disabled>
              <Edit className="w-4 h-4 mr-2" />
              Edit Role
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
