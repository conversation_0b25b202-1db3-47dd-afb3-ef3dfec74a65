import { useState } from "react";
import { useUserRoleHistory, formatUserName, formatTimeAgo, getRoleDisplayName, getRoleColor } from "@/hooks/useAdminRoles";
import { useAuth } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { 
  User, 
  Shield, 
  Clock, 
  Mail,
  Calendar,
  Activity,
  Settings,
  History,
  ArrowLeft
} from "lucide-react";
import { Link, useParams, useNavigate } from "react-router-dom";

export const UserProfile = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const { user: currentUser, canManageUsers } = useAuth();
  const { history, isLoading } = useUserRoleHistory(userId as any, 20);

  // In a real app, you'd fetch the user data here
  // For now, we'll use mock data
  const user = {
    _id: userId,
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    role: "content_editor",
    isActive: true,
    createdAt: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30 days ago
    updatedAt: Date.now() - 2 * 24 * 60 * 60 * 1000, // 2 days ago
    lastLoginAt: Date.now() - 1 * 60 * 60 * 1000, // 1 hour ago
  };

  if (!canManageUsers) {
    return (
      <div className="text-center py-8">
        <Shield className="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <p className="text-gray-500">You don't have permission to view user profiles</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate("/admin/users")}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Users
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">User Profile</h2>
            <p className="text-gray-600">{formatUserName(user)}</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge className={getRoleColor(user.role as any)}>
            {getRoleDisplayName(user.role as any)}
          </Badge>
          <Badge variant={user.isActive ? "default" : "secondary"}>
            {user.isActive ? "Active" : "Inactive"}
          </Badge>
        </div>
      </div>

      {/* User Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Profile Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">
                  {formatUserName(user).charAt(0).toUpperCase()}
                </span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                {formatUserName(user)}
              </h3>
              <p className="text-gray-600">{user.email}</p>
            </div>

            <div className="space-y-3 pt-4 border-t">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Role:</span>
                <Badge className={getRoleColor(user.role as any)}>
                  {getRoleDisplayName(user.role as any)}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Status:</span>
                <Badge variant={user.isActive ? "default" : "secondary"}>
                  {user.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Member since:</span>
                <span className="text-gray-900">
                  {new Date(user.createdAt).toLocaleDateString()}
                </span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Last login:</span>
                <span className="text-gray-900">
                  {formatTimeAgo(user.lastLoginAt)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-4">
          <StatCard
            title="Role Changes"
            value={history?.length || 0}
            icon={Shield}
            color="blue"
          />
          <StatCard
            title="Days Active"
            value={Math.floor((Date.now() - user.createdAt) / (1000 * 60 * 60 * 24))}
            icon={Calendar}
            color="green"
          />
          <StatCard
            title="Last Activity"
            value={formatTimeAgo(user.lastLoginAt)}
            icon={Activity}
            color="purple"
            isText={true}
          />
        </div>
      </div>

      {/* Detailed Information */}
      <Tabs defaultValue="activity" className="space-y-4">
        <TabsList>
          <TabsTrigger value="activity">Activity History</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="w-5 h-5" />
                Role Change History
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                </div>
              ) : history && history.length > 0 ? (
                <div className="space-y-3">
                  {history.map((entry) => (
                    <RoleHistoryItem key={entry._id} entry={entry} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <History className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">No role changes recorded</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Current Permissions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <UserPermissions role={user.role as any} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Account Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Email Notifications</p>
                    <p className="text-sm text-gray-600">Receive email updates about account activity</p>
                  </div>
                  <Badge variant="outline">Enabled</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Two-Factor Authentication</p>
                    <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
                  </div>
                  <Badge variant="outline">Disabled</Badge>
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Session Timeout</p>
                    <p className="text-sm text-gray-600">Automatically log out after inactivity</p>
                  </div>
                  <Badge variant="outline">24 hours</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Role History Item Component
const RoleHistoryItem = ({ entry }: { entry: any }) => {
  return (
    <div className="flex items-start space-x-3 p-3 border rounded-lg">
      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mt-1">
        <Shield className="w-4 h-4 text-blue-600" />
      </div>
      <div className="flex-1">
        <div className="flex items-center space-x-2 mb-1">
          <p className="text-sm font-medium">
            Role changed from{" "}
            <Badge className={getRoleColor(entry.previousRole as any)} variant="outline">
              {getRoleDisplayName(entry.previousRole as any)}
            </Badge>
            {" "}to{" "}
            <Badge className={getRoleColor(entry.newRole as any)}>
              {getRoleDisplayName(entry.newRole as any)}
            </Badge>
          </p>
        </div>
        
        {entry.reason && (
          <p className="text-sm text-gray-600 mb-2">{entry.reason}</p>
        )}
        
        <div className="flex items-center space-x-4 text-xs text-gray-500">
          <span>
            Changed by {entry.changedByUser ? 
              `${entry.changedByUser.firstName} ${entry.changedByUser.lastName}` : 
              "System"
            }
          </span>
          <span>{formatTimeAgo(entry.createdAt)}</span>
        </div>
      </div>
    </div>
  );
};

// User Permissions Component
const UserPermissions = ({ role }: { role: any }) => {
  const permissions = {
    viewer: ["View content", "View media"],
    content_editor: [
      "View content", "Create content", "Edit content", "Publish content",
      "View media", "Upload media", "Edit media"
    ],
    admin: [
      "All content permissions", "All media permissions",
      "View users", "Manage users", "View analytics", "Manage settings"
    ],
    super_admin: ["All system permissions", "Manage roles", "System administration"]
  };

  const userPermissions = permissions[role as keyof typeof permissions] || [];

  return (
    <div className="space-y-3">
      {userPermissions.map((permission, index) => (
        <div key={index} className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-sm text-gray-700">{permission}</span>
        </div>
      ))}
    </div>
  );
};

// Stat Card Component
const StatCard = ({ 
  title, 
  value, 
  icon: Icon, 
  color,
  isText = false
}: {
  title: string;
  value: number | string;
  icon: any;
  color: string;
  isText?: boolean;
}) => {
  const colorClasses = {
    blue: "text-blue-600 bg-blue-100",
    green: "text-green-600 bg-green-100",
    purple: "text-purple-600 bg-purple-100",
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className={`${isText ? 'text-lg' : 'text-2xl'} font-bold text-gray-900`}>
              {value}
            </p>
          </div>
          <div className={`p-2 rounded-full ${colorClasses[color as keyof typeof colorClasses]}`}>
            <Icon className="w-5 h-5" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
