import { useState, useEffect } from "react";
import {
  useAllUsers,
  useAdminStats,
  useUserManagement,
  getRoleDisplayName,
  getRoleColor,
  canModifyRole,
  canModifyUserStatus,
  formatUserName,
  formatTimeAgo,
  getAvailableRoles,
  usePendingInvitations
} from "@/hooks/useAdminRoles";
import { useAuth, UserRole } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Textarea } from "@/components/ui/textarea";
import { 
  Users, 
  UserPlus, 
  Shield, 
  ShieldCheck,
  ShieldX,
  Edit,
  MoreHorizontal,
  Search,
  Filter,
  TrendingUp,
  UserCheck,
  UserX
} from "lucide-react";
import { toast } from "sonner";

export const UserManagement = () => {
  const { user: currentUser, hasRole } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);

  // Fetch data - route protection ensures only admins reach this component
  const { users, isLoading } = useAllUsers({});
  const { stats } = useAdminStats();
  const { invitations } = usePendingInvitations();
  const { changeUserRole, toggleUserStatus, inviteAdmin } = useUserManagement();

  // Filter users based on search and filters
  const filteredUsers = users?.filter(user => {
    const matchesSearch = searchQuery === "" || 
      formatUserName(user).toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesRole = roleFilter === "all" || user.role === roleFilter;
    const matchesStatus = statusFilter === "all" || 
      (statusFilter === "active" && user.isActive) ||
      (statusFilter === "inactive" && !user.isActive);
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const handleRoleChange = async (userId: string, newRole: UserRole, reason?: string) => {
    try {
      await changeUserRole(userId as any, newRole, reason);
      toast.success("User role updated successfully");
    } catch (error: any) {
      const errorMessage = error?.message || "Failed to update user role";
      toast.error(errorMessage);
      console.error("Role change error:", error);
    }
  };

  const handleStatusToggle = async (userId: string, isActive: boolean, reason?: string) => {
    try {
      await toggleUserStatus(userId as any, isActive, reason);
      toast.success(`User ${isActive ? "activated" : "deactivated"} successfully`);
    } catch (error: any) {
      const errorMessage = error?.message || "Failed to update user status";
      toast.error(errorMessage);
      console.error("Status toggle error:", error);
    }
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">User Management</h2>
          <p className="text-gray-600">
            Manage user accounts, roles, and permissions
          </p>
        </div>
        
        {(currentUser?.role === "super_admin" || currentUser?.role === "admin") && (
          <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <UserPlus className="w-4 h-4 mr-2" />
                Invite Admin
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Invite New Admin</DialogTitle>
              </DialogHeader>
              <InviteAdminForm 
                onSuccess={() => setIsInviteDialogOpen(false)}
                onInvite={inviteAdmin}
              />
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="w-8 h-8 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold">{stats.users.total}</p>
                  <p className="text-sm text-gray-600">Total Users</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <UserCheck className="w-8 h-8 text-green-600" />
                <div>
                  <p className="text-2xl font-bold">{stats.users.active}</p>
                  <p className="text-sm text-gray-600">Active Users</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Shield className="w-8 h-8 text-purple-600" />
                <div>
                  <p className="text-2xl font-bold">
                    {stats.users.byRole.admin + stats.users.byRole.super_admin}
                  </p>
                  <p className="text-sm text-gray-600">Administrators</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-8 h-8 text-orange-600" />
                <div>
                  <p className="text-2xl font-bold">{stats.users.byRole.content_editor}</p>
                  <p className="text-sm text-gray-600">Content Editors</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="viewer">Viewer</SelectItem>
                <SelectItem value="content_editor">Content Editor</SelectItem>
                <SelectItem value="admin">Administrator</SelectItem>
                <SelectItem value="super_admin">Super Administrator</SelectItem>
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Pending Invitations */}
      {(currentUser?.role === "super_admin" || currentUser?.role === "admin") && invitations && invitations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserPlus className="w-5 h-5" />
              Pending Invitations ({invitations.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {invitations.map((invitation) => (
                <PendingInvitationCard key={invitation._id} invitation={invitation} />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle>Users ({filteredUsers?.length || 0})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredUsers?.map((user) => (
              <UserCard
                key={user._id}
                user={user}
                currentUser={currentUser}
                onRoleChange={handleRoleChange}
                onStatusToggle={handleStatusToggle}
              />
            ))}

            {filteredUsers?.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No users found matching your criteria</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Individual user card component
const UserCard = ({ 
  user, 
  currentUser, 
  onRoleChange, 
  onStatusToggle 
}: {
  user: any;
  currentUser: any;
  onRoleChange: (userId: string, newRole: UserRole, reason?: string) => void;
  onStatusToggle: (userId: string, isActive: boolean, reason?: string) => void;
}) => {
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);

  const canModifyThisUser = user._id !== currentUser?._id;
  const canChangeRole = canModifyRole(currentUser?.role, user.role, user.role);
  const canChangeStatus = canModifyUserStatus(currentUser?.role, user.role);

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
      <div className="flex items-center space-x-4">
        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
          <span className="text-blue-600 font-semibold">
            {formatUserName(user).charAt(0).toUpperCase()}
          </span>
        </div>
        
        <div>
          <div className="flex items-center space-x-2">
            <h3 className="font-medium text-gray-900">{formatUserName(user)}</h3>
            {user._id === currentUser?._id && (
              <Badge variant="outline" className="text-xs">You</Badge>
            )}
          </div>
          <p className="text-sm text-gray-600">{user.email}</p>
          <div className="flex items-center space-x-2 mt-1">
            <Badge className={getRoleColor(user.role)}>
              {getRoleDisplayName(user.role)}
            </Badge>
            <Badge variant={user.isActive ? "default" : "secondary"}>
              {user.isActive ? "Active" : "Inactive"}
            </Badge>
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <span className="text-xs text-gray-500">
          Joined {formatTimeAgo(user.createdAt)}
        </span>
        
        {canModifyThisUser && (canChangeRole || canChangeStatus) && (
          <div className="flex space-x-1">
            {canChangeRole && (
              <Dialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Shield className="w-4 h-4 mr-1" />
                    Change Role
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Change User Role</DialogTitle>
                  </DialogHeader>
                  <ChangeRoleForm
                    user={user}
                    currentUserRole={currentUser?.role}
                    onRoleChange={(newRole, reason) => {
                      onRoleChange(user._id, newRole, reason);
                      setIsRoleDialogOpen(false);
                    }}
                  />
                </DialogContent>
              </Dialog>
            )}

            {canChangeStatus && (
              <AlertDialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
                <AlertDialogTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm"
                    className={user.isActive ? "text-red-600" : "text-green-600"}
                  >
                    {user.isActive ? (
                      <>
                        <UserX className="w-4 h-4 mr-1" />
                        Deactivate
                      </>
                    ) : (
                      <>
                        <UserCheck className="w-4 h-4 mr-1" />
                        Activate
                      </>
                    )}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      {user.isActive ? "Deactivate" : "Activate"} User
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to {user.isActive ? "deactivate" : "activate"} {formatUserName(user)}?
                      {user.isActive && " This will prevent them from accessing the system."}
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => onStatusToggle(user._id, !user.isActive)}
                      className={user.isActive ? "bg-red-600 hover:bg-red-700" : "bg-green-600 hover:bg-green-700"}
                    >
                      {user.isActive ? "Deactivate" : "Activate"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Change role form component
const ChangeRoleForm = ({ 
  user, 
  currentUserRole, 
  onRoleChange 
}: {
  user: any;
  currentUserRole: UserRole;
  onRoleChange: (newRole: UserRole, reason?: string) => void;
}) => {
  const [selectedRole, setSelectedRole] = useState<UserRole>(user.role);
  const [reason, setReason] = useState("");

  const availableRoles = getAvailableRoles(currentUserRole);

  const handleSubmit = () => {
    if (selectedRole !== user.role) {
      onRoleChange(selectedRole, reason || undefined);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="role">New Role</Label>
        <Select value={selectedRole} onValueChange={(value: UserRole) => setSelectedRole(value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {availableRoles.map(role => (
              <SelectItem key={role} value={role}>
                {getRoleDisplayName(role)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="reason">Reason (Optional)</Label>
        <Textarea
          id="reason"
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          placeholder="Explain why you're changing this user's role..."
          rows={3}
        />
      </div>

      <DialogFooter>
        <Button variant="outline" onClick={() => {}}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit}
          disabled={selectedRole === user.role}
        >
          Update Role
        </Button>
      </DialogFooter>
    </div>
  );
};

// Invite admin form component
const InviteAdminForm = ({ 
  onSuccess, 
  onInvite 
}: {
  onSuccess: () => void;
  onInvite: (email: string, role: "content_editor" | "admin", message?: string) => Promise<any>;
}) => {
  const [email, setEmail] = useState("");
  const [role, setRole] = useState<"content_editor" | "admin">("content_editor");
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!email) return;

    setIsSubmitting(true);
    try {
      await onInvite(email, role, message || undefined);
      toast.success("Admin invitation sent successfully");
      onSuccess();
      setEmail("");
      setRole("content_editor");
      setMessage("");
    } catch (error) {
      toast.error("Failed to send invitation");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="email">Email Address</Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="<EMAIL>"
        />
      </div>

      <div>
        <Label htmlFor="role">Role</Label>
        <Select value={role} onValueChange={(value: "content_editor" | "admin") => setRole(value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="content_editor">Content Editor</SelectItem>
            <SelectItem value="admin">Administrator</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="message">Welcome Message (Optional)</Label>
        <Textarea
          id="message"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Welcome to the admin team..."
          rows={3}
        />
      </div>

      <DialogFooter>
        <Button variant="outline" onClick={onSuccess}>
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit}
          disabled={!email || isSubmitting}
        >
          {isSubmitting ? "Sending..." : "Send Invitation"}
        </Button>
      </DialogFooter>
    </div>
  );
};

// Pending Invitation Card Component
const PendingInvitationCard = ({ invitation }: { invitation: any }) => {
  return (
    <div className="flex items-center justify-between p-3 border rounded-lg bg-blue-50 border-blue-200">
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
          <UserPlus className="w-4 h-4 text-blue-600" />
        </div>
        <div>
          <p className="font-medium text-gray-900">{invitation.email}</p>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Badge className={getRoleColor(invitation.role)}>
              {getRoleDisplayName(invitation.role)}
            </Badge>
            <span>•</span>
            <span>Expires {new Date(invitation.expiresAt).toLocaleDateString()}</span>
          </div>
          {invitation.inviter && (
            <p className="text-xs text-gray-500">
              Invited by {invitation.inviter.firstName} {invitation.inviter.lastName}
            </p>
          )}
        </div>
      </div>
      <Badge variant="outline" className="text-blue-600">
        Pending
      </Badge>
    </div>
  );
};
