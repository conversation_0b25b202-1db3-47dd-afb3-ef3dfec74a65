
import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Globe, User, LogIn, Settings, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SignInButton, UserButton } from '@clerk/clerk-react';
import { useAuth } from '@/hooks/useAuth';
import { LanguageSwitcher } from '@/components/LanguageSwitcher';
import { EditModeToggle, useContentContext } from '@/components/content/ContentProvider';
import { useTranslation, useLanguage } from '@/contexts/I18nContext';
import { useUserBehaviorTracking } from '@/components/analytics/UserBehaviorTracker';
import { useFooterContent } from '@/hooks/useFooterContent';
import { ServicesDropdown } from '@/components/ServicesDropdown';
import { NetworkDropdown } from '@/components/NetworkDropdown';
import { TrainingDropdown } from '@/components/TrainingDropdown';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const Layout = ({ children }: { children: React.ReactNode }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [mobileSubMenus, setMobileSubMenus] = useState({
    services: false,
    networkSolutions: false,
    training: false
  });
  const location = useLocation();
  const { isSignedIn, user, canEditContent } = useAuth();
  const { t } = useTranslation();
  const { language } = useLanguage();
  const { trackLinkClick } = useUserBehaviorTracking();
  const { footerContent } = useFooterContent(language);

  // Try to get content context, but don't fail if not available
  let contentContext;
  try {
    contentContext = useContentContext();
  } catch {
    // Not within ContentProvider, that's okay
    contentContext = null;
  }

  const navItems = [
    { name: t('nav.home'), href: '/' },
    { name: t('nav.about'), href: '/about' },
    { name: t('nav.contact'), href: '/contact' },
  ];

  const isActive = (href: string) => {
    return location.pathname === href;
  };

  const isServicesActive = () => {
    return location.pathname === '/services' || location.pathname.startsWith('/services/');
  };

  const isNetworkActive = () => {
    return location.pathname === '/network-solutions' || location.pathname.startsWith('/network-solutions/') || location.pathname === '/domestic-services';
  };

  const isTrainingActive = () => {
    return location.pathname === '/training' || location.pathname.startsWith('/training/') || location.pathname === '/training-programs';
  };

  const toggleMobileSubMenu = (menu: 'services' | 'networkSolutions' | 'training') => {
    setMobileSubMenus(prev => ({
      ...prev,
      [menu]: !prev[menu]
    }));
  };



  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white/95 backdrop-blur-sm border-b border-gray-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2">
              <img
                src="/logo/officetech_LOGO.png"
                alt="OfficeTech Logo"
                className="h-8 w-auto"
              />
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8 flex-1 ml-12">
              <div className="flex items-center space-x-8">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  to={item.href}
                  className={`font-medium transition-colors duration-200 ${
                    isActive(item.href)
                      ? 'text-blue-600'
                      : 'text-gray-700 hover:text-blue-600'
                  }`}
                >
                  {item.name}
                </Link>
              ))}

              {/* Services Dropdown */}
              <ServicesDropdown
                isActive={isServicesActive()}
                className="font-medium transition-colors duration-200"
              />

              {/* Network Solutions Dropdown */}
              <NetworkDropdown
                isActive={isNetworkActive()}
                className="font-medium transition-colors duration-200"
              />

              {/* Training Dropdown */}
              <TrainingDropdown
                isActive={isTrainingActive()}
                className="font-medium transition-colors duration-200"
              />

              </div>

              {/* Right side - Language Toggle and Authentication */}
              <div className="flex items-center space-x-4 ml-auto">
                {/* Language Toggle */}
                <LanguageSwitcher />

                {/* Authentication */}
                {isSignedIn ? (
                  <div className="flex items-center space-x-4">
                    {canEditContent && contentContext && (
                      <EditModeToggle />
                    )}
                    {canEditContent && (
                      <Link to="/admin">
                        <Button variant="outline" size="sm">
                          <Settings className="w-4 h-4 mr-1" />
                          Admin
                        </Button>
                      </Link>
                    )}
                    <UserButton
                      appearance={{
                        elements: {
                          avatarBox: "w-8 h-8",
                        }
                      }}
                    />
                  </div>
                ) : (
                  <SignInButton mode="modal">
                    <Button variant="outline" size="sm">
                      <LogIn className="w-4 h-4 mr-1" />
                      Sign In
                    </Button>
                  </SignInButton>
                )}
              </div>
            </div>

            {/* Mobile Language Switcher - positioned in the center */}
            <div className="md:hidden flex-1 flex justify-center">
              <LanguageSwitcher />
            </div>

            {/* Mobile menu button - positioned at the right */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="p-2"
              >
                {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t border-gray-100 animate-fade-in">
              <div className="flex flex-col space-y-4">
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    to={item.href}
                    className={`font-medium transition-colors duration-200 ${
                      isActive(item.href)
                        ? 'text-blue-600'
                        : 'text-gray-700 hover:text-blue-600'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}

                {/* Mobile Services Section */}
                <div className="space-y-2">
                  <button
                    onClick={() => toggleMobileSubMenu('services')}
                    className={`w-full flex items-center justify-between font-medium transition-colors duration-200 ${
                      isServicesActive()
                        ? 'text-blue-600'
                        : 'text-gray-700 hover:text-blue-600'
                    }`}
                  >
                    <span>{t('nav.services')}</span>
                    {mobileSubMenus.services ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                  </button>

                  {mobileSubMenus.services && (
                    <div className="pl-4 space-y-2 border-l-2 border-gray-200">
                      <Link
                        to="/services"
                        className="block text-sm text-gray-600 hover:text-blue-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'All Services' : 'Todos los Servicios'}
                      </Link>
                      {/* Service sub-items */}
                      <Link
                        to="/services/cybersecurity"
                        className="block text-sm text-gray-600 hover:text-blue-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'Cybersecurity' : 'Ciberseguridad'}
                      </Link>
                      <Link
                        to="/services/it-support"
                        className="block text-sm text-gray-600 hover:text-blue-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'IT Support' : 'Soporte de TI'}
                      </Link>
                      <Link
                        to="/services/cloud-solutions"
                        className="block text-sm text-gray-600 hover:text-blue-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'Cloud Solutions' : 'Soluciones en la Nube'}
                      </Link>
                    </div>
                  )}
                </div>

                {/* Mobile Network Solutions Section */}
                <div className="space-y-2">
                  <button
                    onClick={() => toggleMobileSubMenu('networkSolutions')}
                    className={`w-full flex items-center justify-between font-medium transition-colors duration-200 ${
                      isNetworkActive()
                        ? 'text-blue-600'
                        : 'text-gray-700 hover:text-blue-600'
                    }`}
                  >
                    <span>{t('nav.networkSolutions')}</span>
                    {mobileSubMenus.networkSolutions ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                  </button>

                  {mobileSubMenus.networkSolutions && (
                    <div className="pl-4 space-y-2 border-l-2 border-gray-200">
                      <Link
                        to="/network-solutions"
                        className="block text-sm text-gray-600 hover:text-blue-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'All Network Solutions' : 'Todas las Soluciones de Red'}
                      </Link>
                      {/* Network solution sub-items */}
                      <Link
                        to="/network-solutions/national-connectivity"
                        className="block text-sm text-gray-600 hover:text-blue-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'National Connectivity' : 'Conectividad Nacional'}
                      </Link>
                      <Link
                        to="/network-solutions/branch-office"
                        className="block text-sm text-gray-600 hover:text-blue-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'Branch Office Connectivity' : 'Conectividad de Sucursales'}
                      </Link>
                      <Link
                        to="/network-solutions/managed-internet"
                        className="block text-sm text-gray-600 hover:text-blue-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'Managed Enterprise Internet' : 'Internet Empresarial Gestionado'}
                      </Link>
                      <Link
                        to="/network-solutions/international-connectivity"
                        className="block text-sm text-gray-600 hover:text-blue-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'International Connectivity' : 'Conectividad Internacional'}
                      </Link>
                    </div>
                  )}
                </div>

                {/* Mobile Training Section */}
                <div className="space-y-2">
                  <button
                    onClick={() => toggleMobileSubMenu('training')}
                    className={`w-full flex items-center justify-between font-medium transition-colors duration-200 ${
                      isTrainingActive()
                        ? 'text-purple-600'
                        : 'text-gray-700 hover:text-purple-600'
                    }`}
                  >
                    <span>{t('nav.training')}</span>
                    {mobileSubMenus.training ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                  </button>

                  {mobileSubMenus.training && (
                    <div className="pl-4 space-y-2 border-l-2 border-gray-200">
                      <Link
                        to="/training"
                        className="block text-sm text-gray-600 hover:text-purple-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'All Training Programs' : 'Todos los Programas de Entrenamiento'}
                      </Link>
                      {/* Training program sub-items */}
                      <Link
                        to="/training/corporate-pc-skills"
                        className="block text-sm text-gray-600 hover:text-purple-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'Corporate PC Skills' : 'Habilidades de PC Corporativas'}
                      </Link>
                      <Link
                        to="/training/leadership-training"
                        className="block text-sm text-gray-600 hover:text-purple-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'Leadership Development' : 'Desarrollo de Liderazgo'}
                      </Link>
                      <Link
                        to="/training/time-management"
                        className="block text-sm text-gray-600 hover:text-purple-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'Time Management' : 'Gestión del Tiempo'}
                      </Link>
                      <Link
                        to="/training/communication-skills"
                        className="block text-sm text-gray-600 hover:text-purple-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'Communication Skills' : 'Habilidades de Comunicación'}
                      </Link>
                      <Link
                        to="/training/oil-gas-training"
                        className="block text-sm text-gray-600 hover:text-purple-600 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {language === 'en' ? 'Oil & Gas Training' : 'Entrenamiento de Petróleo y Gas'}
                      </Link>
                    </div>
                  )}
                </div>

                <LanguageSwitcher />

                {/* Mobile Authentication */}
                <div className="pt-4 border-t border-gray-200">
                  {isSignedIn ? (
                    <div className="space-y-2">
                      <div className="flex items-center space-x-3 px-2">
                        <UserButton
                          appearance={{
                            elements: {
                              avatarBox: "w-8 h-8",
                            }
                          }}
                        />
                        <span className="text-sm text-gray-700">
                          {user?.firstName} {user?.lastName}
                        </span>
                      </div>
                      {canEditContent && contentContext && (
                        <EditModeToggle className="w-full justify-start" />
                      )}
                      {canEditContent && (
                        <Link to="/admin" onClick={() => setIsMenuOpen(false)}>
                          <Button variant="outline" size="sm" className="w-full justify-start">
                            <Settings className="w-4 h-4 mr-2" />
                            Admin Dashboard
                          </Button>
                        </Link>
                      )}
                    </div>
                  ) : (
                    <SignInButton mode="modal">
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <LogIn className="w-4 h-4 mr-2" />
                        Sign In
                      </Button>
                    </SignInButton>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Main Content */}
      <main>{children}</main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <img
                  src="/logo/officetech_LOGO.png"
                  alt="OfficeTech Logo"
                  className="h-8 w-auto"
                />
                {footerContent.companyName && (
                  <span className="font-bold text-xl">{footerContent.companyName}</span>
                )}
              </div>
              <div
                className="text-gray-400 mb-6 max-w-md"
                dangerouslySetInnerHTML={{ __html: footerContent.companyDescription }}
              />
              {footerContent.ctaButtonText && footerContent.ctaButtonLink && (
                <Button asChild className="bg-blue-600 hover:bg-blue-700">
                  <Link to={footerContent.ctaButtonLink}>
                    {footerContent.ctaButtonText}
                  </Link>
                </Button>
              )}
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="font-semibold mb-4">
                {footerContent.quickLinksTitle}
              </h3>
              <ul className="space-y-2">
                {navItems.map((item) => (
                  <li key={item.href}>
                    <Link
                      to={item.href}
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h3 className="font-semibold mb-4">
                {footerContent.contactTitle}
              </h3>
              <div className="space-y-2 text-gray-400">
                {footerContent.address && <p>{footerContent.address}</p>}
                {footerContent.phone && (
                  <p>
                    <a href={`tel:${footerContent.phone}`} className="hover:text-white transition-colors">
                      {footerContent.phone}
                    </a>
                  </p>
                )}
                {footerContent.email && (
                  <p>
                    <a href={`mailto:${footerContent.email}`} className="hover:text-white transition-colors">
                      {footerContent.email}
                    </a>
                  </p>
                )}
              </div>

              {/* Social Links */}
              {footerContent.socialLinks && Array.isArray(footerContent.socialLinks) && footerContent.socialLinks.length > 0 && (
                <div className="mt-4">
                  <div className="flex space-x-4">
                    {footerContent.socialLinks.map((link: { name: string; url: string }, index: number) => (
                      <a
                        key={index}
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-white transition-colors"
                      >
                        {link.name}
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>{footerContent.copyrightText}</p>
          </div>
        </div>
      </footer>


    </div>
  );
};

export default Layout;
