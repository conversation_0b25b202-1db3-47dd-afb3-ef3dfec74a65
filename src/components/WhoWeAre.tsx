
import { Users, Award, Clock } from 'lucide-react';
import { useTranslation } from '@/contexts/I18nContext';

const WhoWeAre = () => {
  const { t } = useTranslation();

  const stats = [
    {
      icon: Clock,
      number: '15+',
      label: t('about.yearsExperience') || 'Years Experience',
      description: t('about.servingGuinea') || 'Serving Equatorial Guinea'
    },
    {
      icon: Users,
      number: '500+',
      label: t('about.satisfiedClients') || 'Satisfied Clients',
      description: t('about.multipleSectors') || 'Across multiple sectors'
    },
    {
      icon: Award,
      number: '99%',
      label: t('about.clientSatisfaction') || 'Client Satisfaction',
      description: t('about.qualityGuaranteed') || 'Quality guaranteed'
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="animate-fade-in-up">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              {t('about.whoWeAreTitle') || 'Who We Are'}
            </h2>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              {t('about.whoWeAreDescription') || 'Leading technology and security solutions provider in Equatorial Guinea, delivering innovative IT services that empower businesses to thrive in the digital age.'}
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div 
                    key={index} 
                    className="text-center p-4 rounded-lg border border-gray-100 hover:shadow-md transition-shadow duration-300 animate-scale-in"
                    style={{ animationDelay: `${index * 200}ms` }}
                  >
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                      <IconComponent className="w-6 h-6 text-primary" />
                    </div>
                    <div className="text-2xl font-bold text-gray-900 mb-1">{stat.number}</div>
                    <div className="text-sm font-medium text-gray-700 mb-1">{stat.label}</div>
                    <div className="text-xs text-gray-500">{stat.description}</div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Image */}
          <div className="relative animate-slide-up animation-delay-300">
            <div className="relative rounded-2xl overflow-hidden shadow-xl">
              <img
                src="/img/Immg1.jpg"
                alt="Professional IT team working"
                className="w-full h-96 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent"></div>
              
              {/* Floating element */}
              <div className="absolute bottom-6 left-6 bg-white/95 backdrop-blur-sm rounded-lg px-4 py-3 animate-float">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                    <Award className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <div className="text-sm font-semibold text-gray-900">Certified Experts</div>
                    <div className="text-xs text-gray-600">Industry Leaders</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhoWeAre;
