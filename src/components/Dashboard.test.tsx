import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import Dashboard from './Dashboard'

describe('Dashboard', () => {
  it('renders dashboard title', () => {
    render(<Dashboard />)
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
  })

  it('renders all metric cards', () => {
    render(<Dashboard />)
    
    // Check for metric cards
    expect(screen.getByText('Total Revenue')).toBeInTheDocument()
    expect(screen.getByText('Active Users')).toBeInTheDocument()
    expect(screen.getByText('Orders')).toBeInTheDocument()
    expect(screen.getByText('Conversion Rate')).toBeInTheDocument()
  })

  it('renders charts section', () => {
    render(<Dashboard />)
    
    // Check for chart titles
    expect(screen.getByText('Revenue Overview')).toBeInTheDocument()
    expect(screen.getByText('User Activity')).toBeInTheDocument()
  })

  it('renders recent activity section', () => {
    render(<Dashboard />)
    
    expect(screen.getByText('Recent Activity')).toBeInTheDocument()
  })
})
