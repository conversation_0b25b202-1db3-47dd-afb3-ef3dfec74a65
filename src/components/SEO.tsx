import { useEffect } from "react";
import { useSEOSettings } from "@/contexts/SettingsContext";

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  noIndex?: boolean;
  canonical?: string;
}

export const SEO = ({
  title,
  description,
  keywords,
  image,
  url,
  type = "website",
  noIndex = false,
  canonical,
}: SEOProps) => {
  // Get SEO settings from context
  const seoSettings = useSEOSettings();

  // Use provided values or fall back to site defaults
  const finalTitle = title
    ? `${title} | OfficeTech Guinea`
    : seoSettings.metaTitle;

  const finalDescription = description || seoSettings.metaDescription;
  const finalKeywords = keywords || seoSettings.metaKeywords;
  const finalImage = image || seoSettings.ogImage;
  const finalUrl = url || (typeof window !== 'undefined' ? window.location.href : '');

  useEffect(() => {
    // Update document title
    document.title = finalTitle;

    // Update meta tags
    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;

      if (!meta) {
        meta = document.createElement('meta');
        if (property) {
          meta.setAttribute('property', name);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    // Basic meta tags
    updateMetaTag('description', finalDescription);
    updateMetaTag('keywords', finalKeywords);

    // Robots
    if (noIndex) {
      updateMetaTag('robots', 'noindex,nofollow');
    }

    // Open Graph
    updateMetaTag('og:title', finalTitle, true);
    updateMetaTag('og:description', finalDescription, true);
    updateMetaTag('og:type', type, true);
    updateMetaTag('og:url', finalUrl, true);
    updateMetaTag('og:image', finalImage, true);
    updateMetaTag('og:site_name', 'OfficeTech Guinea', true);

    // Twitter Card
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:title', finalTitle);
    updateMetaTag('twitter:description', finalDescription);
    updateMetaTag('twitter:image', finalImage);
    if (seoSettings.twitterHandle) {
      updateMetaTag('twitter:site', seoSettings.twitterHandle);
    }

    // Canonical URL
    if (canonical) {
      let link = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      if (!link) {
        link = document.createElement('link');
        link.setAttribute('rel', 'canonical');
        document.head.appendChild(link);
      }
      link.setAttribute('href', canonical);
    }

    // Cleanup function to remove meta tags when component unmounts
    return () => {
      // We don't remove meta tags on unmount as they should persist
      // across route changes for SEO purposes
    };
  }, [finalTitle, finalDescription, finalKeywords, finalImage, finalUrl, type, noIndex, canonical]);

  return null; // This component doesn't render anything visible
};

// Structured Data Component
interface StructuredDataProps {
  type: "Organization" | "LocalBusiness" | "Service" | "Product" | "Article";
  data: Record<string, any>;
}

export const StructuredData = ({ type, data }: StructuredDataProps) => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": type,
    ...data,
  };

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
    </Helmet>
  );
};

// Organization Structured Data
export const OrganizationStructuredData = () => {
  const organizationData = {
    name: "OfficeTech Guinea",
    description: "Leading technology and security solutions provider in Guinea",
    url: "https://officetech.gn",
    logo: "https://officetech.gn/images/logo.png",
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "+***********-789",
      contactType: "customer service",
      availableLanguage: ["English", "Spanish", "French"]
    },
    address: {
      "@type": "PostalAddress",
      streetAddress: "Central Business District",
      addressLocality: "Conakry",
      addressCountry: "Guinea"
    },
    sameAs: [
      "https://facebook.com/officetechguinea",
      "https://twitter.com/officetechgn",
      "https://linkedin.com/company/officetech-guinea"
    ]
  };

  return <StructuredData type="Organization" data={organizationData} />;
};

// Service Structured Data
export const ServiceStructuredData = ({ 
  name, 
  description, 
  provider = "OfficeTech Guinea" 
}: { 
  name: string; 
  description: string; 
  provider?: string; 
}) => {
  const serviceData = {
    name,
    description,
    provider: {
      "@type": "Organization",
      name: provider
    },
    areaServed: {
      "@type": "Country",
      name: "Guinea"
    }
  };

  return <StructuredData type="Service" data={serviceData} />;
};
