import { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ChevronDown, Network, Building2, Globe, Wifi, Home } from 'lucide-react';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useLanguage, useTranslation } from '@/contexts/I18nContext';
import { useAnalyticsContext } from '@/components/analytics/AnalyticsProvider';

interface NetworkDropdownProps {
  isActive: boolean;
  className?: string;
}

export const NetworkDropdown = ({ isActive, className = "" }: NetworkDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { language } = useLanguage();
  const { t } = useTranslation();
  const { trackContent } = useAnalyticsContext();

  // Get all network solution cards in current language
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for network solution cards and sort by order
  const networkSolutions = allContent?.filter(item =>
    item.contentType?.name === "network_solution" && item.data.slug
  ).sort((a, b) => (a.data.order || 0) - (b.data.order || 0)) || [];

  // Fallback data if no dynamic content is available
  const fallbackNetworkSolutions = [
    {
      id: 'national-connectivity',
      title: language === 'en' ? 'National Connectivity' : 'Conectividad Nacional',
      description: language === 'en' ? 'Network solutions across Equatorial Guinea' : 'Soluciones de red en Guinea Ecuatorial',
      icon: Network,
      slug: 'national-connectivity'
    },
    {
      id: 'branch-office',
      title: language === 'en' ? 'Branch Office Connectivity' : 'Conectividad de Sucursales',
      description: language === 'en' ? 'Connect your branches worldwide' : 'Conecta tus sucursales mundialmente',
      icon: Building2,
      slug: 'branch-office'
    },
    {
      id: 'managed-internet',
      title: language === 'en' ? 'Managed Enterprise Internet' : 'Internet Empresarial Gestionado',
      description: language === 'en' ? 'End-to-end internet infrastructure management' : 'Gestión integral de infraestructura de internet',
      icon: Wifi,
      slug: 'managed-internet'
    },
    {
      id: 'international-connectivity',
      title: language === 'en' ? 'International Connectivity' : 'Conectividad Internacional',
      description: language === 'en' ? 'MPLS, SD-WAN and VPL solutions' : 'Soluciones MPLS, SD-WAN y VPL',
      icon: Globe,
      slug: 'international-connectivity'
    },
    {
      id: 'domestic-services',
      title: language === 'en' ? 'Domestic Internet' : 'Internet Doméstico',
      description: language === 'en' ? 'High-speed home internet services' : 'Servicios de internet doméstico de alta velocidad',
      icon: Home,
      slug: '/domestic-services'
    }
  ];

  // Use dynamic content if available, otherwise fallback
  const displaySolutions = networkSolutions.length > 0 ? networkSolutions : fallbackNetworkSolutions;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => {
          setIsOpen(!isOpen);
          trackContent('network-dropdown', 'navigation', isOpen ? 'close' : 'open');
        }}
        className={`font-medium transition-colors duration-200 flex items-center ${className} ${
          isActive
            ? 'text-blue-600 border-b-2 border-blue-600'
            : 'text-gray-700 hover:text-blue-600'
        }`}
      >
        {t('nav.networkSolutions')}
        <ChevronDown className={`ml-1 h-4 w-4 transition-transform duration-200 ${
          isOpen ? 'rotate-180' : ''
        }`} />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {/* All Network Solutions Link */}
          <Link
            to="/network-solutions"
            className="block px-4 py-3 text-gray-900 hover:bg-gray-50 border-b border-gray-100"
            onClick={() => {
              setIsOpen(false);
              trackContent('network-dropdown-all', 'navigation', 'click', { destination: '/network-solutions' });
            }}
          >
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <Network className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <div className="font-medium">{language === 'en' ? 'All Network Solutions' : 'Todas las Soluciones de Red'}</div>
                <div className="text-sm text-gray-500">{language === 'en' ? 'View our complete network portfolio' : 'Ver nuestro portafolio completo de red'}</div>
              </div>
            </div>
          </Link>

          {/* Individual Network Solution Links */}
          {displaySolutions.map((solution) => {
            // For dynamic content, get icon from data, for fallback use icon property
            const IconComponent = solution.data?.icon ?
              (solution.data.icon === 'Network' ? Network :
               solution.data.icon === 'Building2' ? Building2 :
               solution.data.icon === 'Wifi' ? Wifi :
               solution.data.icon === 'Globe' ? Globe :
               solution.data.icon === 'Home' ? Home : Network) :
              solution.icon || Network;

            const title = solution.data?.title || solution.title;
            const description = solution.data?.description || solution.description;
            const slug = solution.data?.slug || solution.slug;
            const linkPath = slug.startsWith('/') ? slug : `/network-solutions/${slug}`;

            return (
              <Link
                key={solution.id || solution._id}
                to={linkPath}
                className="block px-4 py-3 text-gray-900 hover:bg-gray-50 transition-colors"
                onClick={() => {
                  setIsOpen(false);
                  trackContent(`network-dropdown-${solution.id || solution.data?.slug}`, 'navigation', 'click', {
                    destination: linkPath,
                    solutionName: solution.title || solution.data?.title,
                    solutionId: solution.id || solution._id
                  });
                }}
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <IconComponent className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium">{title}</div>
                    <div className="text-sm text-gray-500 line-clamp-1">
                      {description}
                    </div>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
      )}
    </div>
  );
};
