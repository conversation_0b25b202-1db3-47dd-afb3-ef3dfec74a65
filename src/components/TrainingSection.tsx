import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  GraduationCap,
  Users,
  Clock,
  Award,
  ArrowRight,
  Monitor,
  MessageSquare,
  Target,
  Briefcase
} from 'lucide-react';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useLanguage } from '@/contexts/I18nContext';

const TrainingSection = () => {
  const { language } = useLanguage();

  // Get all training program cards in current language
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for training program cards and sort by order
  const trainingPrograms = allContent?.filter(item =>
    item.contentType?.name === "training_program" && item.data.slug
  ).sort((a, b) => (a.data.order || 0) - (b.data.order || 0)) || [];

  // Fallback data if no dynamic content is available
  const fallbackTrainingPrograms = [
    {
      id: 'corporate-pc-skills',
      title: language === 'en' ? 'Corporate PC Skills' : 'Habilidades de PC Corporativas',
      description: language === 'en' ? 'Comprehensive training programs designed to empower your team with essential computer skills for workplace success.' : 'Programas de entrenamiento integrales diseñados para empoderar a tu equipo con habilidades informáticas esenciales para el éxito laboral.',
      icon: Monitor,
      features: language === 'en' ? [
        'Microsoft Office Training',
        'Windows Operating System',
        'File Management & Troubleshooting',
        'System Performance Optimization'
      ] : [
        'Entrenamiento de Microsoft Office',
        'Sistema Operativo Windows',
        'Gestión de Archivos y Solución de Problemas',
        'Optimización del Rendimiento del Sistema'
      ],
      link: '/training/corporate-pc-skills'
    },
    {
      id: 'soft-skills-leadership',
      title: language === 'en' ? 'Leadership Development' : 'Desarrollo de Liderazgo',
      description: language === 'en' ? 'Essential interpersonal and strategic skills needed to lead effectively, inspire others, and drive organizational success.' : 'Habilidades interpersonales y estratégicas esenciales necesarias para liderar efectivamente, inspirar a otros y impulsar el éxito organizacional.',
      icon: Users,
      features: language === 'en' ? [
        'Emotional Intelligence',
        'Effective Communication',
        'Building Trust & Influence',
        'Strategic Vision & Planning'
      ] : [
        'Inteligencia Emocional',
        'Comunicación Efectiva',
        'Construcción de Confianza e Influencia',
        'Visión Estratégica y Planificación'
      ],
      link: '/training/leadership-program'
    },
    {
      id: 'time-management',
      title: language === 'en' ? 'Time Management' : 'Gestión del Tiempo',
      description: language === 'en' ? 'Take control of your schedule, prioritize effectively, and achieve more with less stress through proven strategies.' : 'Toma control de tu horario, prioriza efectivamente y logra más con menos estrés a través de estrategias probadas.',
      icon: Clock,
      features: language === 'en' ? [
        'Goal Setting & Prioritization',
        'Planning & Scheduling',
        'Overcoming Procrastination',
        'Work-Life Balance'
      ] : [
        'Establecimiento de Metas y Priorización',
        'Planificación y Programación',
        'Superación de la Procrastinación',
        'Equilibrio Trabajo-Vida'
      ],
      link: '/training/time-management'
    },
    {
      id: 'communication-skills',
      title: language === 'en' ? 'Communication Skills' : 'Habilidades de Comunicación',
      description: language === 'en' ? 'Master the art of communication to foster stronger connections, reduce misunderstandings, and drive better outcomes.' : 'Domina el arte de la comunicación para fomentar conexiones más fuertes, reducir malentendidos y lograr mejores resultados.',
      icon: MessageSquare,
      features: language === 'en' ? [
        'Active Listening',
        'Verbal & Non-verbal Communication',
        'Conflict Resolution',
        'Public Speaking & Presentations'
      ] : [
        'Escucha Activa',
        'Comunicación Verbal y No Verbal',
        'Resolución de Conflictos',
        'Oratoria y Presentaciones'
      ],
      link: '/training/communication-program'
    },
    {
      id: 'oil-gas-training',
      title: language === 'en' ? 'Oil & Gas Training' : 'Entrenamiento de Petróleo y Gas',
      description: language === 'en' ? 'Professional development and technical training programs for the energy sector with internationally recognized certifications.' : 'Programas de desarrollo profesional y entrenamiento técnico para el sector energético con certificaciones reconocidas internacionalmente.',
      icon: Award,
      features: language === 'en' ? [
        'Offshore/Onshore Safety (OPITO, IWCF)',
        'Process Operations & Maintenance',
        'Pipeline & Refinery Operations',
        'HSE (Health, Safety & Environment)'
      ] : [
        'Seguridad Marina/Terrestre (OPITO, IWCF)',
        'Operaciones de Proceso y Mantenimiento',
        'Operaciones de Tuberías y Refinerías',
        'HSE (Salud, Seguridad y Medio Ambiente)'
      ],
      link: '/training/oil-gas-programs'
    },
    {
      id: 'project-management',
      title: language === 'en' ? 'Project Management' : 'Gestión de Proyectos',
      description: language === 'en' ? 'Develop essential project management skills to deliver successful projects on time and within budget.' : 'Desarrolla habilidades esenciales de gestión de proyectos para entregar proyectos exitosos a tiempo y dentro del presupuesto.',
      icon: Target,
      features: language === 'en' ? [
        'Project Planning & Execution',
        'Risk Management',
        'Team Leadership',
        'Quality Assurance'
      ] : [
        'Planificación y Ejecución de Proyectos',
        'Gestión de Riesgos',
        'Liderazgo de Equipos',
        'Aseguramiento de Calidad'
      ],
      link: '/training/project-management'
    }
  ];

  // Use dynamic content if available, otherwise fallback
  const displayPrograms = trainingPrograms.length > 0 ? trainingPrograms : fallbackTrainingPrograms;

  return (
    <section className="py-20 bg-gradient-to-br from-purple-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mr-4">
              <GraduationCap className="w-8 h-8 text-purple-600" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {language === 'en' ? 'Professional' : 'Programas de'} <span className="text-gradient bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">{language === 'en' ? 'Training Programs' : 'Entrenamiento Profesional'}</span>
            </h2>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {language === 'en'
              ? 'Enhance your skills and advance your career with our comprehensive training solutions. From technical skills to leadership development, we offer programs designed for professional growth.'
              : 'Mejora tus habilidades y avanza en tu carrera con nuestras soluciones de entrenamiento integrales. Desde habilidades técnicas hasta desarrollo de liderazgo, ofrecemos programas diseñados para el crecimiento profesional.'
            }
          </p>
        </div>

        {/* Training Programs Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {displayPrograms.map((program) => {
            // Handle both dynamic content and fallback data
            const IconComponent = program.icon || GraduationCap;
            const title = program.data?.title || program.title;
            const description = program.data?.description || program.description;
            const features = program.data?.features || program.features || [];
            const link = program.data?.slug ? `/training/${program.data.slug}` : program.link;

            return (
              <Card
                key={program.id || program._id}
                className="group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 bg-white border-0"
              >
                <CardContent className="p-6">
                  <div className="flex items-start mb-4">
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-purple-600 transition-colors duration-300">
                      <IconComponent className="w-6 h-6 text-purple-600 group-hover:text-white transition-colors duration-300" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">
                        {title}
                      </h3>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {description}
                  </p>

                  {/* Features List */}
                  <ul className="space-y-2 mb-6">
                    {features.map((feature, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-center">
                        <div className="w-1.5 h-1.5 bg-purple-600 rounded-full mr-3"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>

                  <Link to={link}>
                    <Button 
                      variant="outline" 
                      className="w-full group-hover:bg-purple-600 group-hover:text-white group-hover:border-purple-600 transition-all duration-300"
                    >
                      {language === 'en' ? 'Learn More' : 'Saber Más'}
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* CTA Section */}
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to Invest in Your Team's Growth?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Our expert trainers provide customized solutions, hands-on learning, and flexible formats 
            to meet your organization's specific needs. Contact us to discuss your training requirements.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/training">
              <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white px-8">
                View All Programs
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to="/contact">
              <Button size="lg" variant="outline" className="border-purple-600 text-purple-600 hover:bg-purple-50 px-8">
                <Briefcase className="mr-2 h-5 w-5" />
                Get Custom Quote
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrainingSection;
