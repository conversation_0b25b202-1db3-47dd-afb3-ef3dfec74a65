
import { useTranslation } from '@/contexts/I18nContext';

const TrustedPartners = () => {
  const { t } = useTranslation();
  const partners = [
    'Microsoft Partner',
    'Cisco Systems',
    'VMware Certified',
    'CompTIA Security+',
    'Adobe Creative Partner',
    'Google Workspace',
    'AWS Solutions',
    'Fortinet Partner'
  ];

  return (
    <section className="py-16 bg-white border-t border-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
            {t('partners.title') || 'Trusted Partners'}
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {t('partners.description') || 'We work with industry-leading technology partners to deliver the best solutions for your business.'}
          </p>
        </div>
        
        <div className="relative overflow-hidden">
          <div className="flex animate-scroll-left">
            {/* First set of partners */}
            {partners.map((partner, index) => (
              <div
                key={index}
                className="flex-shrink-0 mx-8 px-6 py-4 bg-gray-50 rounded-lg border hover:shadow-md transition-shadow duration-300"
              >
                <div className="flex items-center justify-center min-w-[200px] h-12">
                  <span className="text-gray-700 font-medium text-sm whitespace-nowrap">
                    {partner}
                  </span>
                </div>
              </div>
            ))}
            {/* Duplicate set for seamless loop */}
            {partners.map((partner, index) => (
              <div
                key={`duplicate-${index}`}
                className="flex-shrink-0 mx-8 px-6 py-4 bg-gray-50 rounded-lg border hover:shadow-md transition-shadow duration-300"
              >
                <div className="flex items-center justify-center min-w-[200px] h-12">
                  <span className="text-gray-700 font-medium text-sm whitespace-nowrap">
                    {partner}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrustedPartners;
