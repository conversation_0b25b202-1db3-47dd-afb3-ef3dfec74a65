/**
 * Caching Strategy Implementation
 * 
 * Provides intelligent caching for API responses, static assets, and user data
 */

interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  maxSize: number; // Maximum number of entries
  strategy: 'lru' | 'fifo' | 'ttl';
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

class MemoryCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private config: CacheConfig;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      ttl: 5 * 60 * 1000, // 5 minutes default
      maxSize: 100,
      strategy: 'lru',
      ...config
    };
  }

  set(key: string, data: T, customTtl?: number): void {
    const now = Date.now();
    const ttl = customTtl || this.config.ttl;

    // Remove expired entries before adding new one
    this.cleanup();

    // If cache is full, remove entries based on strategy
    if (this.cache.size >= this.config.maxSize) {
      this.evict();
    }

    this.cache.set(key, {
      data,
      timestamp: now,
      ttl,
      accessCount: 0,
      lastAccessed: now
    });
  }

  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    const now = Date.now();
    
    // Check if entry has expired
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = now;

    return entry.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    this.cleanup();
    return this.cache.size;
  }

  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
  }

  private evict(): void {
    if (this.cache.size === 0) return;

    let keyToRemove: string;

    switch (this.config.strategy) {
      case 'lru':
        // Remove least recently used
        let oldestAccess = Date.now();
        keyToRemove = '';
        for (const [key, entry] of this.cache.entries()) {
          if (entry.lastAccessed < oldestAccess) {
            oldestAccess = entry.lastAccessed;
            keyToRemove = key;
          }
        }
        break;

      case 'fifo':
        // Remove first in (oldest timestamp)
        let oldestTimestamp = Date.now();
        keyToRemove = '';
        for (const [key, entry] of this.cache.entries()) {
          if (entry.timestamp < oldestTimestamp) {
            oldestTimestamp = entry.timestamp;
            keyToRemove = key;
          }
        }
        break;

      case 'ttl':
        // Remove entry with shortest remaining TTL
        let shortestTtl = Infinity;
        keyToRemove = '';
        const now = Date.now();
        for (const [key, entry] of this.cache.entries()) {
          const remainingTtl = entry.ttl - (now - entry.timestamp);
          if (remainingTtl < shortestTtl) {
            shortestTtl = remainingTtl;
            keyToRemove = key;
          }
        }
        break;

      default:
        // Fallback to first entry
        keyToRemove = this.cache.keys().next().value;
    }

    if (keyToRemove) {
      this.cache.delete(keyToRemove);
    }
  }

  getStats() {
    this.cleanup();
    const entries = Array.from(this.cache.values());
    
    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      totalAccesses: entries.reduce((sum, entry) => sum + entry.accessCount, 0),
      averageAge: entries.length > 0 
        ? entries.reduce((sum, entry) => sum + (Date.now() - entry.timestamp), 0) / entries.length 
        : 0,
      hitRate: 0 // Would need to track misses to calculate this
    };
  }
}

// Persistent cache using localStorage
class PersistentCache<T> {
  private prefix: string;
  private defaultTtl: number;

  constructor(prefix: string = 'cache_', defaultTtl: number = 24 * 60 * 60 * 1000) {
    this.prefix = prefix;
    this.defaultTtl = defaultTtl;
  }

  set(key: string, data: T, ttl?: number): void {
    try {
      const entry = {
        data,
        timestamp: Date.now(),
        ttl: ttl || this.defaultTtl
      };
      
      localStorage.setItem(
        this.prefix + key, 
        JSON.stringify(entry)
      );
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
    }
  }

  get(key: string): T | null {
    try {
      const item = localStorage.getItem(this.prefix + key);
      
      if (!item) {
        return null;
      }

      const entry = JSON.parse(item);
      const now = Date.now();

      // Check if expired
      if (now - entry.timestamp > entry.ttl) {
        this.delete(key);
        return null;
      }

      return entry.data;
    } catch (error) {
      console.warn('Failed to read from localStorage:', error);
      return null;
    }
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): void {
    try {
      localStorage.removeItem(this.prefix + key);
    } catch (error) {
      console.warn('Failed to delete from localStorage:', error);
    }
  }

  clear(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('Failed to clear localStorage:', error);
    }
  }

  cleanup(): void {
    try {
      const keys = Object.keys(localStorage);
      const now = Date.now();

      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          try {
            const item = localStorage.getItem(key);
            if (item) {
              const entry = JSON.parse(item);
              if (now - entry.timestamp > entry.ttl) {
                localStorage.removeItem(key);
              }
            }
          } catch (error) {
            // Remove corrupted entries
            localStorage.removeItem(key);
          }
        }
      });
    } catch (error) {
      console.warn('Failed to cleanup localStorage:', error);
    }
  }
}

// Cache manager with multiple strategies
class CacheManager {
  private memoryCache: MemoryCache<any>;
  private persistentCache: PersistentCache<any>;
  private cacheHits = 0;
  private cacheMisses = 0;

  constructor() {
    this.memoryCache = new MemoryCache({
      ttl: 5 * 60 * 1000, // 5 minutes
      maxSize: 50,
      strategy: 'lru'
    });
    
    this.persistentCache = new PersistentCache('otg_cache_', 24 * 60 * 60 * 1000); // 24 hours
    
    // Cleanup persistent cache on initialization
    this.persistentCache.cleanup();
  }

  // Get from cache with fallback strategy
  async get<T>(key: string, fallback?: () => Promise<T>, options?: {
    memoryTtl?: number;
    persistentTtl?: number;
    skipMemory?: boolean;
    skipPersistent?: boolean;
  }): Promise<T | null> {
    const opts = {
      skipMemory: false,
      skipPersistent: false,
      ...options
    };

    // Try memory cache first
    if (!opts.skipMemory) {
      const memoryResult = this.memoryCache.get(key);
      if (memoryResult !== null) {
        this.cacheHits++;
        return memoryResult;
      }
    }

    // Try persistent cache
    if (!opts.skipPersistent) {
      const persistentResult = this.persistentCache.get(key);
      if (persistentResult !== null) {
        // Store in memory cache for faster access
        this.memoryCache.set(key, persistentResult, opts.memoryTtl);
        this.cacheHits++;
        return persistentResult;
      }
    }

    // Use fallback if provided
    if (fallback) {
      try {
        const result = await fallback();
        
        // Store in both caches
        if (!opts.skipMemory) {
          this.memoryCache.set(key, result, opts.memoryTtl);
        }
        if (!opts.skipPersistent) {
          this.persistentCache.set(key, result, opts.persistentTtl);
        }
        
        this.cacheMisses++;
        return result;
      } catch (error) {
        console.error('Cache fallback failed:', error);
        this.cacheMisses++;
        return null;
      }
    }

    this.cacheMisses++;
    return null;
  }

  // Set in both caches
  set<T>(key: string, data: T, options?: {
    memoryTtl?: number;
    persistentTtl?: number;
    skipMemory?: boolean;
    skipPersistent?: boolean;
  }): void {
    const opts = {
      skipMemory: false,
      skipPersistent: false,
      ...options
    };

    if (!opts.skipMemory) {
      this.memoryCache.set(key, data, opts.memoryTtl);
    }
    
    if (!opts.skipPersistent) {
      this.persistentCache.set(key, data, opts.persistentTtl);
    }
  }

  // Delete from both caches
  delete(key: string): void {
    this.memoryCache.delete(key);
    this.persistentCache.delete(key);
  }

  // Clear both caches
  clear(): void {
    this.memoryCache.clear();
    this.persistentCache.clear();
  }

  // Get cache statistics
  getStats() {
    const total = this.cacheHits + this.cacheMisses;
    const hitRate = total > 0 ? (this.cacheHits / total) * 100 : 0;

    return {
      hits: this.cacheHits,
      misses: this.cacheMisses,
      hitRate: hitRate.toFixed(2) + '%',
      memory: this.memoryCache.getStats(),
      total
    };
  }

  // Cleanup expired entries
  cleanup(): void {
    this.persistentCache.cleanup();
  }
}

// Global cache instance
export const cache = new CacheManager();

// Cache decorators for functions
export function cached<T extends (...args: any[]) => Promise<any>>(
  ttl: number = 5 * 60 * 1000,
  keyGenerator?: (...args: Parameters<T>) => string
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: Parameters<T>) {
      const key = keyGenerator 
        ? keyGenerator(...args)
        : `${propertyKey}_${JSON.stringify(args)}`;

      return cache.get(key, () => originalMethod.apply(this, args), {
        memoryTtl: ttl,
        persistentTtl: ttl * 2
      });
    };

    return descriptor;
  };
}

// Utility functions
export const createCacheKey = (...parts: (string | number | boolean)[]): string => {
  return parts.map(part => String(part)).join('_');
};

export const invalidatePattern = (pattern: string): void => {
  // This would require implementing pattern matching in the cache
  // For now, we'll just clear all caches
  cache.clear();
};

export { MemoryCache, PersistentCache, CacheManager };
