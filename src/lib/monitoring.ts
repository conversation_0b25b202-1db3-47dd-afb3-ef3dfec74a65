/**
 * Production Monitoring and Error Tracking
 * 
 * Provides comprehensive monitoring for production applications
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
}

interface ErrorReport {
  message: string;
  stack?: string;
  url: string;
  userAgent: string;
  timestamp: number;
  userId?: string;
  sessionId?: string;
  tags?: Record<string, string>;
}

interface UserAction {
  action: string;
  element?: string;
  page: string;
  timestamp: number;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

class ProductionMonitor {
  private isProduction: boolean;
  private sessionId: string;
  private userId?: string;
  private performanceObserver?: PerformanceObserver;
  private errorQueue: ErrorReport[] = [];
  private actionQueue: UserAction[] = [];
  private metricsQueue: PerformanceMetric[] = [];
  private flushInterval: number;

  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.sessionId = this.generateSessionId();
    this.flushInterval = 30000; // 30 seconds
    
    if (this.isProduction) {
      this.initializeMonitoring();
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeMonitoring(): void {
    // Error tracking
    this.setupErrorTracking();
    
    // Performance monitoring
    this.setupPerformanceMonitoring();
    
    // User interaction tracking
    this.setupUserInteractionTracking();
    
    // Periodic data flush
    this.setupPeriodicFlush();
    
    // Page visibility changes
    this.setupVisibilityTracking();
    
    // Network status monitoring
    this.setupNetworkMonitoring();
  }

  private setupErrorTracking(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.reportError({
        message: event.message,
        stack: event.error?.stack,
        url: event.filename || window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        userId: this.userId,
        sessionId: this.sessionId,
        tags: {
          type: 'javascript_error',
          line: event.lineno?.toString(),
          column: event.colno?.toString()
        }
      });
    });

    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        userId: this.userId,
        sessionId: this.sessionId,
        tags: {
          type: 'promise_rejection'
        }
      });
    });

    // React error boundary integration
    window.__MONITOR_ERROR__ = (error: Error, errorInfo: any) => {
      this.reportError({
        message: error.message,
        stack: error.stack,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        userId: this.userId,
        sessionId: this.sessionId,
        tags: {
          type: 'react_error',
          componentStack: errorInfo.componentStack
        }
      });
    };
  }

  private setupPerformanceMonitoring(): void {
    // Core Web Vitals
    this.observeWebVitals();
    
    // Navigation timing
    this.observeNavigationTiming();
    
    // Resource timing
    this.observeResourceTiming();
    
    // Long tasks
    this.observeLongTasks();
  }

  private observeWebVitals(): void {
    // Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          
          this.recordMetric({
            name: 'largest_contentful_paint',
            value: lastEntry.startTime,
            timestamp: Date.now(),
            tags: { url: window.location.pathname }
          });
        });
        
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (error) {
        console.warn('LCP observer failed:', error);
      }

      // First Input Delay
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.recordMetric({
              name: 'first_input_delay',
              value: entry.processingStart - entry.startTime,
              timestamp: Date.now(),
              tags: { url: window.location.pathname }
            });
          });
        });
        
        fidObserver.observe({ entryTypes: ['first-input'] });
      } catch (error) {
        console.warn('FID observer failed:', error);
      }

      // Cumulative Layout Shift
      try {
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0;
          const entries = list.getEntries();
          
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          
          this.recordMetric({
            name: 'cumulative_layout_shift',
            value: clsValue,
            timestamp: Date.now(),
            tags: { url: window.location.pathname }
          });
        });
        
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (error) {
        console.warn('CLS observer failed:', error);
      }
    }
  }

  private observeNavigationTiming(): void {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          this.recordMetric({
            name: 'page_load_time',
            value: navigation.loadEventEnd - navigation.fetchStart,
            timestamp: Date.now(),
            tags: { url: window.location.pathname }
          });
          
          this.recordMetric({
            name: 'dom_content_loaded',
            value: navigation.domContentLoadedEventEnd - navigation.fetchStart,
            timestamp: Date.now(),
            tags: { url: window.location.pathname }
          });
          
          this.recordMetric({
            name: 'time_to_first_byte',
            value: navigation.responseStart - navigation.fetchStart,
            timestamp: Date.now(),
            tags: { url: window.location.pathname }
          });
        }
      }, 0);
    });
  }

  private observeResourceTiming(): void {
    if ('PerformanceObserver' in window) {
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          
          entries.forEach((entry: PerformanceResourceTiming) => {
            // Track slow resources
            if (entry.duration > 1000) { // > 1 second
              this.recordMetric({
                name: 'slow_resource',
                value: entry.duration,
                timestamp: Date.now(),
                tags: {
                  url: window.location.pathname,
                  resource: entry.name,
                  type: entry.initiatorType
                }
              });
            }
          });
        });
        
        resourceObserver.observe({ entryTypes: ['resource'] });
      } catch (error) {
        console.warn('Resource observer failed:', error);
      }
    }
  }

  private observeLongTasks(): void {
    if ('PerformanceObserver' in window) {
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          
          entries.forEach((entry) => {
            this.recordMetric({
              name: 'long_task',
              value: entry.duration,
              timestamp: Date.now(),
              tags: {
                url: window.location.pathname,
                startTime: entry.startTime.toString()
              }
            });
          });
        });
        
        longTaskObserver.observe({ entryTypes: ['longtask'] });
      } catch (error) {
        console.warn('Long task observer failed:', error);
      }
    }
  }

  private setupUserInteractionTracking(): void {
    // Click tracking
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      
      this.trackUserAction({
        action: 'click',
        element: this.getElementSelector(target),
        page: window.location.pathname,
        timestamp: Date.now(),
        userId: this.userId,
        sessionId: this.sessionId,
        metadata: {
          x: event.clientX,
          y: event.clientY,
          tagName: target.tagName,
          className: target.className
        }
      });
    });

    // Form submissions
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement;
      
      this.trackUserAction({
        action: 'form_submit',
        element: this.getElementSelector(form),
        page: window.location.pathname,
        timestamp: Date.now(),
        userId: this.userId,
        sessionId: this.sessionId,
        metadata: {
          formId: form.id,
          formName: form.name
        }
      });
    });

    // Page navigation
    let currentPath = window.location.pathname;
    const checkPathChange = () => {
      if (window.location.pathname !== currentPath) {
        this.trackUserAction({
          action: 'page_navigation',
          page: window.location.pathname,
          timestamp: Date.now(),
          userId: this.userId,
          sessionId: this.sessionId,
          metadata: {
            from: currentPath,
            to: window.location.pathname
          }
        });
        currentPath = window.location.pathname;
      }
    };

    // Check for path changes (for SPA navigation)
    setInterval(checkPathChange, 1000);
  }

  private setupPeriodicFlush(): void {
    setInterval(() => {
      this.flush();
    }, this.flushInterval);

    // Flush on page unload
    window.addEventListener('beforeunload', () => {
      this.flush();
    });
  }

  private setupVisibilityTracking(): void {
    document.addEventListener('visibilitychange', () => {
      this.trackUserAction({
        action: document.hidden ? 'page_hidden' : 'page_visible',
        page: window.location.pathname,
        timestamp: Date.now(),
        userId: this.userId,
        sessionId: this.sessionId
      });
    });
  }

  private setupNetworkMonitoring(): void {
    // Network status changes
    window.addEventListener('online', () => {
      this.trackUserAction({
        action: 'network_online',
        page: window.location.pathname,
        timestamp: Date.now(),
        userId: this.userId,
        sessionId: this.sessionId
      });
    });

    window.addEventListener('offline', () => {
      this.trackUserAction({
        action: 'network_offline',
        page: window.location.pathname,
        timestamp: Date.now(),
        userId: this.userId,
        sessionId: this.sessionId
      });
    });
  }

  private getElementSelector(element: HTMLElement): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      return `.${element.className.split(' ').join('.')}`;
    }
    
    return element.tagName.toLowerCase();
  }

  public setUserId(userId: string): void {
    this.userId = userId;
  }

  public reportError(error: ErrorReport): void {
    if (!this.isProduction) return;
    
    this.errorQueue.push(error);
    
    // Immediate flush for critical errors
    if (this.errorQueue.length >= 5) {
      this.flush();
    }
  }

  public recordMetric(metric: PerformanceMetric): void {
    if (!this.isProduction) return;
    
    this.metricsQueue.push(metric);
  }

  public trackUserAction(action: UserAction): void {
    if (!this.isProduction) return;
    
    this.actionQueue.push(action);
  }

  private async flush(): void {
    if (this.errorQueue.length === 0 && this.metricsQueue.length === 0 && this.actionQueue.length === 0) {
      return;
    }

    const payload = {
      errors: [...this.errorQueue],
      metrics: [...this.metricsQueue],
      actions: [...this.actionQueue],
      sessionId: this.sessionId,
      userId: this.userId,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    // Clear queues
    this.errorQueue = [];
    this.metricsQueue = [];
    this.actionQueue = [];

    try {
      // Send to monitoring endpoint
      await fetch('/api/monitoring', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
    } catch (error) {
      console.warn('Failed to send monitoring data:', error);
    }
  }

  public getSessionId(): string {
    return this.sessionId;
  }

  public getUserId(): string | undefined {
    return this.userId;
  }
}

// Global monitor instance
export const monitor = new ProductionMonitor();

// React Error Boundary integration
export class MonitoredErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    if (window.__MONITOR_ERROR__) {
      window.__MONITOR_ERROR__(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong.</h2>
          <p>We've been notified of this error and are working to fix it.</p>
          <button onClick={() => this.setState({ hasError: false })}>
            Try again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Utility functions
export const trackPageView = (page: string) => {
  monitor.trackUserAction({
    action: 'page_view',
    page,
    timestamp: Date.now(),
    sessionId: monitor.getSessionId(),
    userId: monitor.getUserId()
  });
};

export const trackCustomEvent = (eventName: string, metadata?: Record<string, any>) => {
  monitor.trackUserAction({
    action: eventName,
    page: window.location.pathname,
    timestamp: Date.now(),
    sessionId: monitor.getSessionId(),
    userId: monitor.getUserId(),
    metadata
  });
};

export const reportCustomError = (message: string, metadata?: Record<string, any>) => {
  monitor.reportError({
    message,
    url: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: Date.now(),
    userId: monitor.getUserId(),
    sessionId: monitor.getSessionId(),
    tags: {
      type: 'custom_error',
      ...metadata
    }
  });
};
