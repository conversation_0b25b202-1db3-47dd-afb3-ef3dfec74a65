// Environment configuration
export const env = {
  CONVEX_URL: import.meta.env.VITE_CONVEX_URL || "",
  CLERK_PUBLISHABLE_KEY: import.meta.env.VITE_CLERK_PUBLISHABLE_KEY || "",
  APP_NAME: import.meta.env.VITE_APP_NAME || "OfficeTech Guinea",
  APP_DESCRIPTION: import.meta.env.VITE_APP_DESCRIPTION || "Leading technology and security solutions company in Equatorial Guinea",
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
};

// Validate required environment variables
export const validateEnv = () => {
  const required = [
    { key: "VITE_CONVEX_URL", value: env.CONVEX_URL },
    { key: "VITE_CLERK_PUBLISHABLE_KEY", value: env.CLERK_PUBLISHABLE_KEY },
  ];

  const missing = required.filter(({ value }) => !value);
  
  if (missing.length > 0) {
    console.error("Missing required environment variables:", missing.map(({ key }) => key));
    if (env.isProduction) {
      throw new Error(`Missing required environment variables: ${missing.map(({ key }) => key).join(", ")}`);
    }
  }
};
