# Admin Invitation System

## Overview
The invitation system allows admins and super admins to invite new users to join the platform with specific roles. When invited users sign up, they automatically receive the role specified in their invitation.

## How It Works

### 1. **Creating Invitations**
- Admins and super admins can create invitations through `/admin/users`
- Invitations specify the email, role, and optional welcome message
- Invitations expire after 7 days by default
- Available roles for invitations: `content_editor`, `admin`

### 2. **User Sign-Up Flow**
When a user signs up with Clerk:

1. **Check for Invitation**: The system checks if there's a pending invitation for their email
2. **Auto-Accept**: If found, the invitation is automatically accepted
3. **Role Assignment**: User gets the role specified in the invitation
4. **Welcome Screen**: Invited users see a welcome message with their role
5. **Navigation**: Users are redirected to the appropriate admin area

### 3. **Default Behavior**
- **With Invitation**: User gets the invited role (`content_editor` or `admin`)
- **Without Invitation**: User gets the default `viewer` role
- **Existing Users**: No changes to existing user roles

## Database Schema

### Users Table
```typescript
{
  clerkId: string,
  email: string,
  firstName?: string,
  lastName?: string,
  role: "super_admin" | "admin" | "content_editor" | "viewer",
  isActive: boolean,
  lastLogin?: number,
  createdAt: number,
  updatedAt: number
}
```

### Admin Invitations Table
```typescript
{
  email: string,
  role: "content_editor" | "admin",
  message?: string,
  invitedBy: Id<"users">,
  status: "pending" | "accepted" | "expired" | "revoked",
  createdAt: number,
  expiresAt: number,
  acceptedAt?: number
}
```

## API Functions

### Backend (Convex)
- `users:getOrCreateUser` - Handles user creation with invitation acceptance
- `users:checkPendingInvitation` - Check if email has pending invitation
- `adminRoles:createAdminInvitation` - Create new invitation
- `adminRoles:getPendingInvitations` - Get all pending invitations

### Frontend (React)
- `useInvitationFlow` - Hook to check invitation status
- `InvitationWelcome` - Welcome screen for invited users
- `InvitationHandler` - Manages invitation flow in app

## User Experience

### For Admins
1. Go to `/admin/users`
2. Click "Invite Admin" button
3. Enter email, select role, add welcome message
4. Invitation is sent and appears in pending list
5. See when invitations are accepted

### For Invited Users
1. Receive invitation (email notification would be added separately)
2. Sign up normally with Clerk
3. See welcome screen with their assigned role
4. Get redirected to appropriate admin area:
   - `admin` → `/admin`
   - `content_editor` → `/admin/content`

### For Regular Users
1. Sign up normally with Clerk
2. Get default `viewer` role
3. Can access public areas only

## Security Features
- ✅ Invitations expire automatically
- ✅ Only admins/super admins can create invitations
- ✅ Email validation prevents unauthorized access
- ✅ Role-based access control throughout the system
- ✅ Invitation status tracking prevents reuse

## Current Status
✅ **Backend**: Fully implemented and tested
✅ **Frontend**: Invitation flow and welcome screen ready
✅ **Integration**: Seamlessly integrated with existing auth system
✅ **Testing**: All functions tested and working
⚠️ **Email Notifications**: Not implemented (would use Clerk or external service)

## Testing
```bash
# Create invitation
npx convex run adminRoles:createAdminInvitation '{"email": "<EMAIL>", "role": "content_editor", "message": "Welcome!"}'

# Check invitation
npx convex run users:checkPendingInvitation '{"email": "<EMAIL>"}'

# Simulate user signup (auto-accepts invitation)
npx convex run users:getOrCreateUser '{"clerkId": "test_user", "email": "<EMAIL>", "firstName": "Test", "lastName": "User"}'
```

## Future Enhancements
- Email notifications for invitations
- Bulk invitation system
- Custom invitation expiration times
- Invitation templates
- Analytics on invitation acceptance rates
