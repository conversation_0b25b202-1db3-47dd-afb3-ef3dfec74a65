# Deployment Guide

This guide covers various deployment options for the OfficeTech Guinea website.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Git
- Environment variables configured

### Build Process
```bash
# Install dependencies
npm install

# Run tests
npm run test

# Build for production
npm run build
```

## 🌐 Platform-Specific Deployments

### Vercel (Recommended)

**Automatic Deployment**
1. Connect your GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

**Manual Deployment**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to staging
vercel

# Deploy to production
vercel --prod
```

**Configuration**
- Uses `vercel.json` for configuration
- Automatic HTTPS and CDN
- Serverless functions support
- Built-in analytics

### Netlify

**Automatic Deployment**
1. Connect repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `dist`
4. Configure environment variables

**Manual Deployment**
```bash
# Install Netlify CLI
npm i -g netlify-cli

# Deploy to staging
netlify deploy --dir=dist

# Deploy to production
netlify deploy --prod --dir=dist
```

**Configuration**
- Uses `netlify.toml` for configuration
- Form handling and serverless functions
- Split testing and branch deploys
- Built-in CDN and edge functions

### Docker

**Build Image**
```bash
# Build production image
docker build -t officetech-guinea .

# Run container
docker run -p 8080:80 officetech-guinea
```

**Docker Compose**
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

**Configuration**
- Multi-stage build for optimization
- Nginx for serving static files
- Health checks included
- Production-ready security headers

### AWS S3 + CloudFront

**Prerequisites**
- AWS CLI configured
- S3 bucket created
- CloudFront distribution set up

**Deployment**
```bash
# Set environment variables
export AWS_S3_BUCKET=your-bucket-name
export AWS_CLOUDFRONT_DISTRIBUTION_ID=your-distribution-id

# Deploy
./scripts/deploy.sh production aws
```

**Configuration**
- Static website hosting
- Global CDN distribution
- SSL/TLS certificates
- Custom domain support

### GitHub Pages

**Setup**
1. Enable GitHub Pages in repository settings
2. Choose source: GitHub Actions
3. Configure workflow in `.github/workflows/`

**Deployment**
```bash
# Deploy to GitHub Pages
./scripts/deploy.sh production github-pages
```

**Configuration**
- Free hosting for public repositories
- Custom domain support
- HTTPS by default
- Jekyll integration optional

## 🔧 Environment Configuration

### Required Environment Variables

```env
# Authentication
VITE_CLERK_PUBLISHABLE_KEY=pk_test_...

# Database
VITE_CONVEX_URL=https://...

# Analytics (Optional)
VITE_GA_TRACKING_ID=G-...
VITE_FB_PIXEL_ID=...

# API Keys (Optional)
VITE_GOOGLE_MAPS_API_KEY=...
VITE_RECAPTCHA_SITE_KEY=...
```

### Platform-Specific Variables

**Vercel**
```bash
# Set via Vercel CLI
vercel env add VITE_CLERK_PUBLISHABLE_KEY production
vercel env add VITE_CONVEX_URL production
```

**Netlify**
```bash
# Set via Netlify CLI
netlify env:set VITE_CLERK_PUBLISHABLE_KEY your_value
netlify env:set VITE_CONVEX_URL your_value
```

**Docker**
```bash
# Set in docker-compose.yml or pass as arguments
docker run -e VITE_CLERK_PUBLISHABLE_KEY=your_value ...
```

## 🔒 Security Considerations

### HTTPS Configuration
- All platforms provide automatic HTTPS
- Custom domains require SSL certificate setup
- Redirect HTTP to HTTPS

### Security Headers
- Content Security Policy (CSP)
- X-Frame-Options
- X-Content-Type-Options
- Referrer Policy

### Environment Security
- Never commit secrets to repository
- Use platform-specific secret management
- Rotate keys regularly
- Limit access to production environments

## 📊 Monitoring and Logging

### Performance Monitoring
```bash
# Run Lighthouse audit
npm run test:lighthouse

# Monitor Core Web Vitals
npm run test:performance
```

### Error Tracking
- Configure error boundaries
- Set up logging service (Sentry, LogRocket)
- Monitor application health

### Analytics
- Google Analytics integration
- Custom event tracking
- Conversion monitoring

## 🚨 Troubleshooting

### Common Issues

**Build Failures**
```bash
# Clear cache
rm -rf node_modules package-lock.json
npm install

# Check Node.js version
node --version  # Should be 18+
```

**Environment Variable Issues**
```bash
# Verify variables are set
echo $VITE_CLERK_PUBLISHABLE_KEY

# Check build output
npm run build -- --debug
```

**Deployment Failures**
```bash
# Check deployment logs
vercel logs  # For Vercel
netlify logs  # For Netlify

# Verify build locally
npm run build && npm run preview
```

### Debug Mode
```bash
# Enable debug logging
DEBUG=* npm run build

# Verbose deployment
./scripts/deploy.sh production vercel --verbose
```

## 🔄 CI/CD Pipeline

### GitHub Actions
- Automated testing on pull requests
- Security scanning with Trivy
- Performance testing with Lighthouse
- Automatic deployment on merge to main

### Workflow Steps
1. **Test**: Unit tests, linting, type checking
2. **Security**: Vulnerability scanning
3. **Performance**: Lighthouse audit, SEO tests
4. **Build**: Production build with optimization
5. **Deploy**: Platform-specific deployment
6. **Notify**: Slack/email notifications

### Branch Strategy
- `main`: Production deployments
- `develop`: Staging deployments
- `feature/*`: Preview deployments

## 📈 Performance Optimization

### Build Optimization
- Code splitting and lazy loading
- Tree shaking for smaller bundles
- Image optimization and compression
- CSS purging and minification

### Runtime Optimization
- Service worker for caching
- Resource preloading
- Critical CSS inlining
- Progressive image loading

### CDN Configuration
- Static asset caching
- Geographic distribution
- Compression (Gzip/Brotli)
- HTTP/2 and HTTP/3 support

## 🔧 Maintenance

### Regular Tasks
- Update dependencies monthly
- Monitor security vulnerabilities
- Review performance metrics
- Update SSL certificates

### Backup Strategy
- Database backups (Convex handles this)
- Code repository backups
- Environment configuration backups
- Media asset backups

### Rollback Procedures
```bash
# Vercel rollback
vercel rollback

# Netlify rollback
netlify rollback

# Docker rollback
docker tag officetech-guinea:previous officetech-guinea:latest
docker-compose up -d
```

## 📞 Support

For deployment issues:
- Check platform-specific documentation
- Review deployment logs
- Contact platform support
- Create GitHub issue for code-related problems
