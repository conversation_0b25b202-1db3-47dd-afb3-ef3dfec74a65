# Monitoring and Observability Guide

This guide covers the monitoring and observability setup for the OfficeTech Guinea website.

## 🔍 Overview

The monitoring stack includes:
- **Health Checks**: Automated uptime and response time monitoring
- **Performance Monitoring**: Core Web Vitals and user experience metrics
- **Error Tracking**: Application errors and exceptions
- **Security Monitoring**: SSL certificate and security header validation
- **Analytics**: User behavior and business metrics

## 🚀 Quick Start

### Start Monitoring
```bash
# Run a single health check
node scripts/monitor.js check

# Start continuous monitoring
node scripts/monitor.js start

# Stop monitoring
node scripts/monitor.js stop
```

### Docker Monitoring Stack
```bash
# Start monitoring services
docker-compose --profile monitoring up -d

# Access Grafana dashboard
open http://localhost:3000

# Access Prometheus
open http://localhost:9090
```

## 📊 Monitoring Components

### 1. Health Monitoring

**Automated Checks**
- Website uptime monitoring
- Response time measurement
- SSL certificate validation
- HTTP status code tracking

**Configuration**
```javascript
const MONITORING_CONFIG = {
  urls: [
    'https://officetech-guinea.com',
    'https://officetech-guinea.com/about',
    'https://officetech-guinea.com/services',
    'https://officetech-guinea.com/contact'
  ],
  interval: 60000, // 1 minute
  timeout: 10000,  // 10 seconds
  retries: 3
};
```

### 2. Performance Monitoring

**Core Web Vitals**
- Largest Contentful Paint (LCP)
- First Input Delay (FID)
- Cumulative Layout Shift (CLS)

**Custom Metrics**
- Page load times
- Resource loading performance
- JavaScript execution time
- API response times

### 3. Error Tracking

**Application Errors**
- JavaScript runtime errors
- Network request failures
- Component rendering errors
- Authentication failures

**Infrastructure Errors**
- Server downtime
- DNS resolution issues
- SSL certificate problems
- CDN failures

### 4. Security Monitoring

**SSL/TLS Monitoring**
- Certificate expiry tracking
- Certificate chain validation
- Security protocol verification

**Security Headers**
- Content Security Policy (CSP)
- HTTP Strict Transport Security (HSTS)
- X-Frame-Options validation
- X-Content-Type-Options verification

## 🔧 Setup Instructions

### 1. Basic Monitoring

**Install Dependencies**
```bash
npm install --save-dev lighthouse puppeteer @axe-core/puppeteer
```

**Environment Variables**
```env
# Slack notifications (optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...

# Email notifications (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### 2. Prometheus + Grafana

**Start Services**
```bash
docker-compose --profile monitoring up -d
```

**Configure Dashboards**
1. Access Grafana at http://localhost:3000
2. Login with admin/admin
3. Import dashboard from `monitoring/grafana/dashboards/`
4. Configure data sources

### 3. CI/CD Integration

**GitHub Actions**
```yaml
- name: Run monitoring tests
  run: |
    npm run test:lighthouse
    npm run test:seo
    node scripts/monitor.js check
```

**Deployment Hooks**
```bash
# Post-deployment health check
./scripts/deploy.sh production vercel
node scripts/monitor.js check
```

## 📈 Metrics and Alerts

### Key Metrics

**Availability**
- Uptime percentage (target: 99.9%)
- Response time (target: < 2s)
- Error rate (target: < 1%)

**Performance**
- Core Web Vitals scores
- Page load times
- Resource loading times
- Cache hit rates

**User Experience**
- Bounce rate
- Session duration
- Page views per session
- Conversion rates

### Alert Thresholds

**Critical Alerts**
- Website down (immediate)
- SSL certificate expired (immediate)
- Response time > 10s (immediate)

**Warning Alerts**
- Response time > 5s (5 minutes)
- SSL certificate expires < 30 days
- Uptime < 99.9% (1 hour)

**Info Alerts**
- Performance degradation
- High error rates
- Unusual traffic patterns

## 🔔 Notification Channels

### Slack Integration
```javascript
// Configure in scripts/monitor.js
const SLACK_CONFIG = {
  webhookUrl: process.env.SLACK_WEBHOOK_URL,
  channel: '#monitoring',
  username: 'Website Monitor'
};
```

### Email Notifications
```javascript
// Configure SMTP settings
const EMAIL_CONFIG = {
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  }
};
```

### PagerDuty Integration
```javascript
// For critical alerts
const PAGERDUTY_CONFIG = {
  integrationKey: process.env.PAGERDUTY_INTEGRATION_KEY,
  severity: 'critical'
};
```

## 📊 Dashboards

### Grafana Dashboards

**Website Overview**
- Uptime status
- Response times
- Error rates
- SSL certificate status

**Performance Dashboard**
- Core Web Vitals
- Page load times
- Resource performance
- User experience metrics

**Security Dashboard**
- SSL certificate monitoring
- Security header compliance
- Failed authentication attempts
- Suspicious activity

### Custom Dashboards

**Business Metrics**
- Conversion rates
- Lead generation
- User engagement
- Revenue tracking

## 🔍 Troubleshooting

### Common Issues

**High Response Times**
```bash
# Check server resources
docker stats

# Analyze network latency
ping officetech-guinea.com

# Review CDN performance
curl -I https://officetech-guinea.com
```

**SSL Certificate Issues**
```bash
# Check certificate details
openssl s_client -connect officetech-guinea.com:443

# Verify certificate chain
ssl-cert-check -c officetech-guinea.com
```

**Monitoring Service Down**
```bash
# Restart monitoring services
docker-compose restart prometheus grafana

# Check service logs
docker-compose logs prometheus
docker-compose logs grafana
```

### Debug Mode

**Enable Verbose Logging**
```bash
DEBUG=* node scripts/monitor.js check
```

**Test Specific Components**
```bash
# Test SSL certificate
node -e "require('./scripts/monitor.js').checkSSLCertificate('officetech-guinea.com')"

# Test specific URL
node -e "require('./scripts/monitor.js').checkUrl('https://officetech-guinea.com')"
```

## 📋 Maintenance

### Regular Tasks

**Daily**
- Review alert notifications
- Check dashboard for anomalies
- Verify monitoring service health

**Weekly**
- Review performance trends
- Update alert thresholds if needed
- Check SSL certificate status

**Monthly**
- Update monitoring dependencies
- Review and optimize dashboards
- Analyze long-term trends

### Backup and Recovery

**Configuration Backup**
```bash
# Backup Grafana dashboards
docker exec grafana_container grafana-cli admin export-dashboard

# Backup Prometheus configuration
cp monitoring/prometheus.yml monitoring/prometheus.yml.backup
```

**Service Recovery**
```bash
# Restore from backup
docker-compose down
docker-compose up -d

# Verify services
curl http://localhost:9090/-/healthy
curl http://localhost:3000/api/health
```

## 🔗 Integration with External Services

### Google Analytics
- Enhanced ecommerce tracking
- Custom event monitoring
- Goal conversion tracking
- Real user monitoring (RUM)

### Search Console
- Search performance monitoring
- Crawl error tracking
- Index coverage reports
- Core Web Vitals data

### Third-party Services
- Pingdom for external monitoring
- New Relic for APM
- Sentry for error tracking
- Hotjar for user behavior

## 📞 Support

For monitoring issues:
- Check service status dashboards
- Review monitoring logs
- Contact DevOps team
- Create incident tickets

**Emergency Contacts**
- On-call engineer: +1-xxx-xxx-xxxx
- DevOps team: <EMAIL>
- Incident management: <EMAIL>
