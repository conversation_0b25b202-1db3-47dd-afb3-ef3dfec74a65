# Edit Mode Functionality

## Overview

The Edit Mode functionality provides inline editing capabilities for content on the frontend, allowing admins and content editors to directly edit text on any section/page with immediate save functionality to the database.

## Key Features

### 🔐 **Authentication-First Design**
- **Performance Optimized**: Edit mode components only load for users with `content_editor` role or higher
- **Fast for Regular Users**: Non-admin users see no edit mode overhead, keeping the page fast
- **Role-Based Access**: Only admins and content editors can access edit mode functionality

### ✏️ **Inline Editing**
- **Direct Text Editing**: Click any editable text to edit it in place
- **Multiple Input Types**: Supports single-line text, multi-line textarea, and HTML content
- **Visual Feedback**: Hover effects show editable areas with edit buttons
- **Save/Cancel Actions**: Each edit session has save and cancel buttons

### 🎯 **User Experience**
- **Hover to Edit**: Editable content shows edit buttons on hover
- **Keyboard Shortcuts**: 
  - `Enter` to save (single-line text)
  - `Shift+Enter` for new lines (textarea)
  - `Escape` to cancel
- **Visual Indicators**: Editing content is highlighted with blue outline
- **Loading States**: Save button shows spinner during database updates

## How It Works

### 1. **Edit Mode Toggle**
```tsx
<EditModeToggle />
```
- Only visible to users with `canEditContent` permission
- Toggles between view mode (👁️) and edit mode (🔧)
- Orange background when active

### 2. **Inline Editor Components**

#### **EditableText** - For single-line text
```tsx
<EditableText
  identifier="content-identifier"
  fieldName="title"
  value={content.title}
  contentId={content._id}
  as="h1"
  className="text-4xl font-bold"
  placeholder="Enter title..."
/>
```

#### **EditableTextarea** - For multi-line content
```tsx
<EditableTextarea
  identifier="content-identifier"
  fieldName="description"
  value={content.description}
  contentId={content._id}
  as="p"
  className="text-gray-600"
  placeholder="Enter description..."
/>
```

### 3. **Database Integration**
- Uses Convex mutations to update content
- Real-time updates across all connected clients
- Automatic error handling with toast notifications
- Optimistic UI updates for better UX

## Implementation Details

### **Authentication Check**
```tsx
const { canEditContent } = useAuth();
const { isEditMode } = useContentContext();

// Only show edit functionality if user can edit AND edit mode is on
if (!canEditContent || !isEditMode) {
  return <span>{value}</span>; // Regular display
}
```

### **Performance Optimization**
- Edit mode components are conditionally rendered
- No edit mode overhead for regular users
- Lazy loading of edit functionality
- Minimal bundle size impact for non-editors

### **Content Updates**
```tsx
const updateContent = useMutation(api.content.updateContent);

const handleSave = async () => {
  await updateContent({
    id: contentId,
    data: { [fieldName]: newValue }
  });
};
```

## Usage Examples

### **Hero Section**
```tsx
// Title editing
<EditableText
  identifier="home-hero"
  fieldName="title"
  value={data.title}
  contentId={content._id}
  as="h1"
  className="text-6xl font-bold text-white"
/>

// Subtitle editing
<EditableTextarea
  identifier="home-hero"
  fieldName="subtitle"
  value={data.subtitle}
  contentId={content._id}
  as="div"
  className="text-2xl text-blue-100"
/>
```

### **Service Cards**
```tsx
// Service title
<EditableText
  identifier="service-cybersecurity"
  fieldName="title"
  value={data.title}
  contentId={content._id}
  as="h3"
  className="text-xl font-bold"
/>

// Service description
<EditableTextarea
  identifier="service-cybersecurity"
  fieldName="description"
  value={data.description}
  contentId={content._id}
  as="div"
  className="text-gray-600"
/>
```

## Security & Permissions

### **Role Requirements**
- **Minimum Role**: `content_editor`
- **Permissions**: `content:write`
- **Database Security**: Server-side validation in Convex functions

### **Permission Hierarchy**
1. **viewer**: Read-only access
2. **content_editor**: Can edit and publish content ✅
3. **admin**: Full content management ✅
4. **super_admin**: All permissions ✅

## Future Enhancements

### **Planned Features**
- [ ] Rich text editor for HTML content
- [ ] Image upload and editing
- [ ] Bulk edit operations
- [ ] Content versioning with restore
- [ ] Collaborative editing indicators
- [ ] Auto-save functionality
- [ ] Content approval workflows

### **Technical Improvements**
- [ ] Optimistic updates with rollback
- [ ] Offline editing support
- [ ] Real-time collaboration
- [ ] Content locking during edits
- [ ] Advanced validation rules

## Best Practices

### **For Developers**
1. Always wrap editable content with appropriate inline editor components
2. Use semantic HTML elements with the `as` prop
3. Provide meaningful placeholders
4. Handle loading and error states
5. Test with different user roles

### **For Content Editors**
1. Enable edit mode before making changes
2. Save changes frequently
3. Use descriptive content that matches the context
4. Test changes on different screen sizes
5. Disable edit mode when done editing

## Memory Note
**Important**: All new text areas added to the project should include this inline editing functionality when edit mode is enabled, allowing admins and content editors to directly edit text on any section/page with a save button that updates the database.
