# SEO and Performance Optimization Guide

This guide covers the comprehensive SEO and performance optimization system implemented for the OfficeTech Guinea website.

## Overview

The system includes:
- **SEO Components**: Meta tags, structured data, Open Graph, Twitter Cards
- **Performance Components**: Resource preloading, lazy loading, caching, Core Web Vitals monitoring
- **Analytics Components**: Google Analytics, Google Tag Manager, Facebook Pixel integration
- **Layout Components**: Pre-configured layouts for different page types

## Components

### SEO Component

The `SEO` component handles all meta tags and SEO-related head elements.

```tsx
import { SEO } from '@/components/SEO';

<SEO
  title="Custom Page Title"
  description="Page description for search engines"
  keywords="keyword1, keyword2, keyword3"
  image="/images/page-image.jpg"
  canonical="https://officetech.gn/page"
  noIndex={false}
/>
```

### Performance Component

The `Performance` component handles performance optimizations.

```tsx
import { Performance } from '@/components/Performance';

<Performance
  preloadImages={['/images/hero.jpg', '/images/banner.jpg']}
  criticalCSS="body { font-family: Inter; }"
  deferNonCritical={true}
/>
```

### Analytics Component

The `Analytics` component handles tracking and analytics.

```tsx
import { Analytics } from '@/components/Analytics';

<Analytics
  pageTitle="Home Page"
  pagePath="/"
  userId="user123"
/>
```

## Pre-configured Layouts

### Home Page Layout

```tsx
import { HomePageLayout } from '@/components/SEOLayout';

export const HomePage = () => (
  <HomePageLayout>
    <div>Your home page content</div>
  </HomePageLayout>
);
```

### Services Page Layout

```tsx
import { ServicesPageLayout } from '@/components/SEOLayout';

export const ServicesPage = () => (
  <ServicesPageLayout>
    <div>Your services content</div>
  </ServicesPageLayout>
);
```

### Blog Post Layout

```tsx
import { BlogPageLayout } from '@/components/SEOLayout';

export const BlogPost = ({ post }) => (
  <BlogPageLayout
    title={post.title}
    description={post.excerpt}
    image={post.featuredImage}
    publishDate={post.publishDate}
    author={post.author}
  >
    <article>{post.content}</article>
  </BlogPageLayout>
);
```

## Custom SEO Layout

For custom pages, use the `SEOLayout` component:

```tsx
import { SEOLayout } from '@/components/SEOLayout';

export const CustomPage = () => (
  <SEOLayout
    seo={{
      title: "Custom Page",
      description: "Custom page description",
      keywords: "custom, page, keywords"
    }}
    performance={{
      preloadImages: ['/images/custom.jpg'],
      deferNonCritical: true
    }}
    analytics={{
      pageTitle: "Custom Page",
      pagePath: "/custom"
    }}
  >
    <div>Your custom content</div>
  </SEOLayout>
);
```

## HOC Pattern

Use the `withSEO` higher-order component for existing components:

```tsx
import { withSEO } from '@/components/SEOLayout';

const MyComponent = () => <div>Content</div>;

export default withSEO(
  MyComponent,
  { title: "My Page", description: "My page description" },
  { preloadImages: ['/images/my-image.jpg'] },
  { pageTitle: "My Page", pagePath: "/my-page" }
);
```

## Tracking Events

### Page Views
```tsx
import { trackPageView } from '@/components/Analytics';

trackPageView('Page Title', '/page-path');
```

### Custom Events
```tsx
import { trackEvent } from '@/components/Analytics';

trackEvent('button_click', {
  button_name: 'cta_button',
  page: 'home'
});
```

### Form Submissions
```tsx
import { trackFormSubmission } from '@/components/Analytics';

const handleSubmit = (success) => {
  trackFormSubmission('contact_form', success);
};
```

### Downloads
```tsx
import { trackDownload } from '@/components/Analytics';

const handleDownload = () => {
  trackDownload('brochure.pdf', 'pdf');
};
```

### External Links
```tsx
import { trackExternalLink } from '@/components/Analytics';

const handleExternalClick = () => {
  trackExternalLink('https://external-site.com', 'External Link');
};
```

## Structured Data

### Organization Schema
```tsx
import { useSEOOptimization } from '@/components/seo';

const { addOrganizationSchema } = useSEOOptimization();

useEffect(() => {
  addOrganizationSchema();
}, []);
```

### Service Schema
```tsx
const { addServiceSchema } = useSEOOptimization();

useEffect(() => {
  addServiceSchema({
    name: "Network Infrastructure",
    description: "Professional network setup and maintenance",
    provider: "OfficeTech Guinea",
    areaServed: "Guinea"
  });
}, []);
```

### Breadcrumb Schema
```tsx
const { addBreadcrumbSchema } = useSEOOptimization();

useEffect(() => {
  addBreadcrumbSchema([
    { name: "Home", url: "https://officetech.gn/" },
    { name: "Services", url: "https://officetech.gn/services" },
    { name: "Network Solutions", url: "https://officetech.gn/services/network" }
  ]);
}, []);
```

## Performance Monitoring

### Core Web Vitals
The system automatically monitors:
- **Largest Contentful Paint (LCP)**
- **First Input Delay (FID)**
- **Cumulative Layout Shift (CLS)**

### Custom Performance Metrics
```tsx
import { usePerformanceMetrics } from '@/components/Performance';

const MyComponent = () => {
  usePerformanceMetrics(); // Automatically logs performance data
  return <div>Content</div>;
};
```

## Service Worker

The service worker (`/public/sw.js`) provides:
- **Static asset caching**
- **Dynamic content caching**
- **Offline functionality**
- **Background sync**
- **Push notifications**

## Configuration

### Settings Context

SEO settings are managed through the `SettingsContext`:

```tsx
import { useSEOSettings } from '@/contexts/SettingsContext';

const seoSettings = useSEOSettings();
// Access: metaTitle, metaDescription, googleAnalyticsId, etc.
```

### Performance Settings

```tsx
import { usePerformanceSettings } from '@/contexts/SettingsContext';

const performanceSettings = usePerformanceSettings();
// Access: lazyLoading, imageOptimization, cacheEnabled, etc.
```

## Best Practices

### 1. Page-Specific SEO
Always provide page-specific titles, descriptions, and keywords.

### 2. Image Optimization
- Use appropriate image formats (WebP when supported)
- Implement lazy loading for below-the-fold images
- Provide proper alt text

### 3. Performance
- Preload critical resources
- Defer non-critical CSS and JavaScript
- Use service worker for caching

### 4. Analytics
- Track meaningful user interactions
- Monitor Core Web Vitals
- Set up conversion tracking

### 5. Structured Data
- Add organization schema to all pages
- Use breadcrumb schema for navigation
- Add article schema for blog posts

## Testing

### SEO Testing
- Use Google Search Console
- Test with Rich Results Test tool
- Validate Open Graph with Facebook Debugger

### Performance Testing
- Use Lighthouse for Core Web Vitals
- Test with PageSpeed Insights
- Monitor real user metrics

### Analytics Testing
- Use Google Analytics Real-Time reports
- Test event tracking in browser console
- Verify conversion tracking

## Troubleshooting

### Common Issues

1. **Meta tags not updating**: Ensure SEO component is rendered before other content
2. **Analytics not tracking**: Check if analytics is enabled in feature flags
3. **Performance issues**: Verify service worker registration and cache strategies
4. **Structured data errors**: Validate JSON-LD with Google's testing tool

### Debug Mode

Enable debug mode in performance settings to see detailed logging:

```tsx
// In SettingsContext
debugMode: true
```

This will log performance metrics, analytics events, and SEO data to the console.
