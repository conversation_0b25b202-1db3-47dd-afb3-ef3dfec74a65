# Clerk JWT Template Configuration

## Issue
The admin user management functionality currently uses temporary authentication fallbacks because the Clerk JWT template is not properly configured. This causes `ctx.auth.getUserIdentity()` to return `null` in Convex functions even for authenticated users.

## Solution
To fix this permanently, you need to configure a JWT template in Clerk:

### Step 1: Access Clerk Dashboard
1. Go to [Clerk Dashboard](https://dashboard.clerk.com)
2. Select your project: `valid-baboon-9`

### Step 2: Create JWT Template
1. Navigate to **JWT Templates** in the sidebar
2. Click **New template**
3. Set the template name to: `convex`
4. Configure the template with the following claims:

```json
{
  "aud": "convex",
  "exp": {{exp}},
  "iat": {{iat}},
  "iss": "https://valid-baboon-9.clerk.accounts.dev",
  "nbf": {{nbf}},
  "sub": "{{user.id}}"
}
```

### Step 3: Verify Configuration
The JWT template should match the configuration in `convex/auth.config.ts`:
- Domain: `https://valid-baboon-9.clerk.accounts.dev`
- Application ID: `convex`

### Step 4: Remove Temporary Code
Once the JWT template is configured and working:

1. Remove all "TEMPORARY" fallback code from `convex/adminRoles.ts`
2. Replace the fallback logic with proper authentication checks
3. Test that authentication works correctly

### Current Status
✅ Backend functionality works with development fallbacks
✅ User role management (promote/demote users)
✅ User status management (activate/deactivate)
✅ Admin invitation system
✅ User statistics and analytics
❌ Proper Clerk authentication (requires JWT template)

### Testing
You can test the current functionality at:
- `/admin/users` - User management interface
- All CRUD operations work with temporary super_admin privileges

The system will show warning messages in the console about authentication failures, but functionality remains intact through the fallback system.
