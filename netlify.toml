[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--prefix=/dev/null"

# Production context
[context.production]
  command = "npm run build"

# Deploy Preview context
[context.deploy-preview]
  command = "npm run build:dev"

# Branch deploy context
[context.branch-deploy]
  command = "npm run build:dev"

# Redirects and rewrites
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://connect.facebook.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.convex.cloud https://clerk.com https://www.google-analytics.com; frame-src 'self' https://www.youtube.com;"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Service worker should not be cached
[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "no-cache"

# Manifest files
[[headers]]
  for = "/*.webmanifest"
  [headers.values]
    Cache-Control = "public, max-age=604800"

# Functions configuration
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Plugin configuration
[[plugins]]
  package = "@netlify/plugin-lighthouse"
  
  [plugins.inputs.thresholds]
    performance = 0.9
    accessibility = 0.9
    best-practices = 0.9
    seo = 0.9

[[plugins]]
  package = "netlify-plugin-submit-sitemap"
  
  [plugins.inputs]
    baseUrl = "https://officetech-guinea.netlify.app"
    sitemapPath = "/sitemap.xml"
    ignorePeriod = 0
    providers = [
      "google",
      "bing"
    ]
